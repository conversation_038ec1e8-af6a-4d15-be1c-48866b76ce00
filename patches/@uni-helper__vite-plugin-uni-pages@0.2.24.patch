diff --git a/dist/index.cjs b/dist/index.cjs
index dbccc0070f539a0c407569bfca0f42ba9f09841d..5a97f92224b9bd635d2bf3e482ecbab129b28af8 100644
--- a/dist/index.cjs
+++ b/dist/index.cjs
@@ -542,7 +542,14 @@ class PageContext {
     const pagesMap = /* @__PURE__ */ new Map();
     const pages = this.withUniPlatform ? this.pageMetaData.filter((v) => !/\..*$/.test(v.path) || v.path.includes(uniEnv.platform)).map((v) => ({ ...v, path: v.path.replace(/\..*$/, "") })) : this.pageMetaData;
     pages.forEach((v) => pagesMap.set(v.path, v));
-    this.pageMetaData = [...pagesMap.values()];
+    // this.pageMetaData = [...pagesMap.values()];
+    const subPackagePagePaths = this.subPageMetaData.reduce((acc, subPackage) => {
+      const { pages } = subPackage;
+      return acc.concat(pages.map((page) => `${subPackage.root}/${page.path}`));
+    }, []);
+    this.pageMetaData = [...pagesMap.values()].filter(
+      (page) => !subPackagePagePaths.includes(page.path),
+    );
     this.options.onBeforeWriteFile(this);
     const data = {
       ...this.pagesGlobConfig,
diff --git a/dist/index.mjs b/dist/index.mjs
index ad1ba04ed661d2e57a4f605ad92af5fa9adfc3f0..6972424df3e4372d149aa35b4f2c2248dfc508ad 100644
--- a/dist/index.mjs
+++ b/dist/index.mjs
@@ -525,7 +525,14 @@ class PageContext {
     const pagesMap = /* @__PURE__ */ new Map();
     const pages = this.withUniPlatform ? this.pageMetaData.filter((v) => !/\..*$/.test(v.path) || v.path.includes(platform)).map((v) => ({ ...v, path: v.path.replace(/\..*$/, "") })) : this.pageMetaData;
     pages.forEach((v) => pagesMap.set(v.path, v));
-    this.pageMetaData = [...pagesMap.values()];
+    // this.pageMetaData = [...pagesMap.values()];
+    const subPackagePagePaths = this.subPageMetaData.reduce((acc, subPackage) => {
+      const { pages } = subPackage;
+      return acc.concat(pages.map((page) => `${subPackage.root}/${page.path}`));
+    }, []);
+    this.pageMetaData = [...pagesMap.values()].filter(
+      (page) => !subPackagePagePaths.includes(page.path),
+    );
     this.options.onBeforeWriteFile(this);
     const data = {
       ...this.pagesGlobConfig,
