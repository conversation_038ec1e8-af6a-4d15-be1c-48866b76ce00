import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages';

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
    h5: {
      navigationStyle: 'custom',
    },
  },
  easycom: {
    autoscan: true,
    custom: {
      '^uni-(.*)': '@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue',
      '^uv-(.*)': '@climblee/uv-ui/components/uv-$1/uv-$1.vue',
    },
  },
  tabBar: {
    color: '#666',
    selectedColor: '#56BE66',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [
      {
        iconPath: 'static/images/tabBar/home.png',
        selectedIconPath: 'static/images/tabBar/home-active.png',
        pagePath: 'pages/home/<USER>',
        text: '首页',
        mpAlipay: {
          transparentTitle: 'auto',
          titlePenetrate: 'YES',
        },
      },
      {
        iconPath: 'static/images/tabBar/nearby.png',
        selectedIconPath: 'static/images/tabBar/nearby-active.png',
        pagePath: 'pages/nearby/index',
        text: '附近',
      },
      {
        iconPath: 'static/images/tabBar/traffic.png',
        selectedIconPath: 'static/images/tabBar/traffic-active.png',
        pagePath: 'pages/travelServices/index',
        text: '出行',
      },
      {
        iconPath: 'static/images/tabBar/order.png',
        selectedIconPath: 'static/images/tabBar/order-active.png',
        pagePath: 'pages/order/index',
        text: '订单',
      },
      {
        iconPath: 'static/images/tabBar/mine.png',
        selectedIconPath: 'static/images/tabBar/mine-active.png',
        pagePath: 'pages/mine/index',
        text: '我的',
      },
    ],
  },
  pages: [],
});
