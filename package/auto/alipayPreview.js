// 光谷出行
const ci = require('miniprogram-ci');
const { Jimp } = require('jimp');
const qrcode = require('qrcode-reader');
const fs = require('fs');
const axios = require('axios');
const path = require('path');
const { minidev } = require('minidev');

const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};
// 获取命令行参数
const args = process.argv.slice(2);

console.log('参数:', args);

// 使用参数
const env = args[1] || 'prod';

const send = async (qr) => {
  console.log('发送请求', qr);
  axios.post(
    'https://admin.jscoder.com/prod-api/mini/add',
    {
      appId: '2021004192643782',
      platform: 'alipay',
      miniName: '汕头智行',
      env,
      qr,
    }
  );
};

(async () => {
  const { qrcodeUrl, version } = await minidev.preview({
    appId: '2021004192643782',
    identityKeyPath: './yaoalipay.json',
    project: '../../dist/build/mp-alipay',
    ignoreHttpReqPermission: true,
    ignoreHttpDomainCheck: true,
    ignoreWebViewDomainCheck: true,
    autoPush: false
  });
  // 预览二维码图片地址
  console.log('二维码地址', qrcodeUrl);

  sleep(2000);

  try {
    const image = await Jimp.read(qrcodeUrl);

    // 创建 QR 码读取器
    const qr = new qrcode();

    // 解码二维码
    qr.callback = (err, value) => {
      if (err) {
        console.error('解码二维码失败:', err);
        return;
      }
      console.log('二维码内容:', value.result);
      send(value.result);
    };

    // 将图片传递给解码器
    qr.decode(image.bitmap);
  } catch (error) {
    console.error('处理二维码失败:', error.message);
  }
})();
