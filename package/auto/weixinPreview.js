// 光谷出行
const ci = require('miniprogram-ci');
const { Jimp } = require('jimp');
const qrcode = require('qrcode-reader');
const fs = require('fs');
const axios = require('axios');
const path = require('path');
const jsQR = require('jsqr');

const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};
// 获取命令行参数
const args = process.argv.slice(2);

console.log('参数:', args);

// 使用参数
const env = args[1] || 'prod';

const send = async (qr) => {
  axios.post(
    'https://admin.jscoder.com/prod-api/mini/add',
    {
      appId: 'wx3d6f9582d4847a5a',
      platform: 'weixin',
      miniName: '汕头智行',
      env,
      qr,
    }
  );
};

const decodeQR = async (imagePath) => {
  try {
    const image = await Jimp.read(imagePath);
    const { width, height, data } = image.bitmap;

    const code = jsQR(data, width, height);
    console.error('code', code);
    if (code) {

      return code.data;
    }
    throw new Error('未能识别二维码');
  } catch (error) {
    throw new Error(`解析失败: ${error.message}`);
  }
};

(async () => {
  const project = new ci.Project({
    appid: 'wx3d6f9582d4847a5a',
    type: 'miniProgram',
    projectPath: '../../dist/build/mp-weixin',
    privateKeyPath: './private.wx3d6f9582d4847a5a.key',
    // ignores: ['node_modules/**/*'],
  });
  const previewResult = await ci.preview({
    project,
    setting: {
      es6: true,
      es7: true,
      minify: true,
    },
    qrcodeFormat: 'image',
    qrcodeOutputDest: `./qrcode-${env}.jpg`,
    onProgressUpdate: console.log,
    robot: env === 'prod' ? 1 : 2
  });

  sleep(5000);

  try {
    const qrcodePath = path.join(__dirname, `qrcode-${env}.jpg`);
    console.log('qrcodePath', qrcodePath);

    const qrcode = await decodeQR(qrcodePath);
    console.log('qrcode====>', qrcode);

    if (qrcode) {
      await send(qrcode);
      console.log('发送成功,微信预览结束');
    }
  } catch (error) {
    console.error('处理二维码失败:', error.message);
  }
})();
