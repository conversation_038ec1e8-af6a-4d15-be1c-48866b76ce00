// 光谷出行
const ci = require('miniprogram-ci');
const { Jimp } = require('jimp');
const qrcode = require('qrcode-reader');
const fs = require('fs');
const axios = require('axios');
const path = require('path');
const { minidev } = require('minidev');
const packageJson = require('../../package.json');
const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};
// 获取命令行参数
const args = process.argv.slice(2);

console.log('参数:', args);

// 使用参数
const env = args[1] || 'prod';

const send = async (qr) => {
  console.log('发送请求', qr);
  axios.post(
    'https://admin.jscoder.com/prod-api/mini/add',
    {
      appId: '2021004192643782',
      platform: 'alipay',
      miniName: '汕头智行',
      env,
      qr,
    }
  );
};

(async () => {
  const uploadResult = await minidev.upload({
    appId: '2021004192643782',
    identityKeyPath: './yaoalipay.json',
    project: '../../dist/build/mp-alipay',
    ignoreHttpReqPermission: true,
    ignoreHttpDomainCheck: true,
    ignoreWebViewDomainCheck: true,
    version: packageJson.miniVersion,
  });
  console.log('支付宝上传结果', uploadResult);
})();
