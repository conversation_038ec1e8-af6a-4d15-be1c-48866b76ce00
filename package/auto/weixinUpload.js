// 光谷出行
const ci = require('miniprogram-ci');
const { Jimp } = require('jimp');
const qrcode = require('qrcode-reader');
const fs = require('fs');
const axios = require('axios');
const path = require('path');
const packageJson = require('../../package.json');

const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};
// 获取命令行参数
const args = process.argv.slice(2);

console.log('参数:', args);

// 使用参数
const env = args[1] || 'prod';

const send = async (qr) => {
  axios.post(
    'https://admin.jscoder.com/prod-api/mini/add',
    {
      appId: 'wx3d6f9582d4847a5a',
      platform: 'weixin',
      miniName: '汕头智行',
      env,
      qr,
    }
  );
};

(async () => {
  const project = new ci.Project({
    appid: 'wx3d6f9582d4847a5a',
    type: 'miniProgram',
    projectPath: '../../dist/build/mp-weixin',
    privateKeyPath: './private.wx3d6f9582d4847a5a.key',
  });
  const result = await ci.upload({
    project,
    version: packageJson.miniVersion,
    setting: {
      es6: true,
      es7: true,
      minify: true,
    },
    qrcodeFormat: 'image',
    qrcodeOutputDest: './qrcode.jpg',
  });

  console.log('微信上传结果', result);
})();
