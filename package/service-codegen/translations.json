{"【汕头】充电平台C端服务/充电站点": "Station", "【基线】app/个人中心/用户钱包相关": "wallet", "【汕头】充电平台C端服务/地图搜索": "map search", "【基线】app/协议相关": "protocol related", "【汕头】充电平台C端服务/充电相关": "Charging", "【基线】app/资讯展位相关/展位相关服务": "booth", "【基线】app/资讯展位相关/demo": "Information Booth/demo", "【基线】app/服务健康检查": "service health check", "【汕头】充电平台C端服务/充电订单": "charging orders", "【基线】app/资讯展位相关/资讯栏目controller": "news", "【基线】app/个人中心/订单通知接口": "personal center/order notification", "【汕头】充电平台C端服务": "Charging services", "【基线】app/微信授权相关": "WeChat authorization related", "【基线】app/支付宝授权相关": "Alipay authorization related", "【基线】app/账号管理": "Account Management", "【汕头】充电平台C端服务/站点收藏": "Station collect", "【基线】app/短信相关": "SMS", "【基线】app/个人中心/反馈问题配置相关": "feedback configuration", "【基线】app/个人中心/反馈相关": "Feedback Related", "【基线】app/文件上传controller": "file upload controller", "【汕头】充电平台C端服务/发票订单": "invoice order", "【基线】app/发票/用户发票抬头": "invoice header", "【基线】app/发票/发票相关接口": "invoice Related", "【基线】app/个人中心/用户钱包相关/用户钱包相关": "Personal Center/User Wallet Related/User Wallet Related", "【基线】app/苏宁支付相关接口": "payment-related interfaces", "【汕头】充电平台C端服务/ParkingController": "ParkingController", "【汕头】充电平台C端服务/评价接口": "evaluation interface", "停车场查询": "Parking lot inquiry", "我的车辆": "my vehicle", "用户收藏车场": "User collection yard", "停车订单": "Parking order", "泊位预警": "Berth warning", "无牌车": "unlicensed car", "【汕头】充电平台C端服务/发票相关http接口": "invoice-order", "【汕头】充电平台C端服务/服务健康检查": "health check", "【基线】app/地图搜索": "map search", "【基线】app/天气 Controller": "Weather Controller", "【基线】app/资讯展位相关/展位相关服务/展位相关服务": "<PERSON>", "单车/小程序首页，单车散点接口": "Bicycle", "停车场通道查询": "Parking lot passage inquiry", "统一支付回调": "Unified payment callback", "【基线】app/小程序端-备案申请接口": "filing application", "发票查询": "invoice inquiry", "公共服务数据（公园和公厕数据）接口": "Public service", "Default": "<PERSON><PERSON><PERSON>", "搜索热词展示": "Search hot", "坐席号相关接口": "agent number", "投诉人管理": "Complainant management", "投诉记录管理": "Complaint record management", "场站查询": "Station inquiry", "用户查询": "user query", "信路通": "Xinlutong", "订单查询": "order inquiry", "客服退款管理": "Customer Service Refund Management", "回访相关接口": "Visit relevant interfaces", "健康检查": "health check", "C端-在线客服-相关接口": "C-terminal-Online customer service-Related interfaces", "文件操作": "file operation", "配置参数": "configuration parameters", "【基线】app/经营视图控制器": "business view", "【基线】app/营收视图控制器": "revenue view", "【基线】app/权益包": "rights package", "【基线】app/用户钱包相关": "user wallet", "【基线】app/看板入口控制器": "Kanban entry", "用户优惠券": "User Coupons", "【基线】app/营销平台-权益接口": "marketing rights", "【基线】app/用户优惠券": "user coupons", "【基线】app/预测查询控制器": "forecast query", "【汕头】充电平台C端服务/企业相关": "enterprise-related", "【基线】app/用户积分controller": "user points", "【基线】app/AppMerchantFacade": "AppMerchantFacade", "【基线】app/小程序端-商户备案统计": "filing application", "【基线】app/泊位精确导航controller": "berth navigation", "无感协议": "protocol free", "【基线】app/发票/小程序端商城发票接口": "mall invoice", "【基线】app/积分商城订单": "points mall order", "【基线】app/发票相关接口": "invoice related interface", "停车预约小程序端控制器": "parking reserve"}