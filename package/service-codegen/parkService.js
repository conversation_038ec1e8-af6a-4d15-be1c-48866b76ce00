// const OpenAPI = require('../');
const OpenAPI = require('@soeasy/service-codegen');
const path = require('path');
// const OpenAPI = require('service-codegen');
// const OpenAPI = require('openapi-typescript-codegen');
const tagServer = {
  '【基线】充电平台C端服务': '/charging-server',
  '【基线】app': '/app-server',
};
const generate = async (input, output) => {
  await OpenAPI.generate({
    input,
    output,
    httpClient: OpenAPI.HttpClient.AXIOSCUSTOM,
    useOptions: false,
    useUnionTypes: false,
    exportCore: true,
    exportSchemas: false,
    exportModels: true,
    exportServices: true,
    functionNameFormatter: (name) => {
      return name.replace('/api/v1', '');
    },
    functionFixPath: (path, tag) => {
      return `/parking-platform${path}`;
    },
    functionParameterFormatter: (list) => {
      return list.filter((item) => {
        return item.in !== 'header';
      });
    },
    axiosInstancePath: '@/utils/http',
  });
};

const main = async () => {
  const json = await OpenAPI.getJson({
    projectId: 5525782,
    auth: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTk4OTUwLCJ0cyI6ImE1ZjZlNzA4ZjlhNTFmMzciLCJpYXQiOjE3MzM5NzA5ODUzNDZ9.XABOLaIM8xDL1VKQ1xjoU9TWgvFdV5XqNVuuP5wCMT8',
    type: 1,
    // tagServer: tagServer,
  });
  await generate(json, './src/parkService');
};

main();
