# Changelog

# [1.14.0](/compare/1.13.0...1.14.0) (2025-05-15)

### Bug Fixes

- 充电页面loading问题修改 1a04de7

### Features

- 版本号更新 3488e5f
- 待支付数量查询取消筛选参数 6cf4dac
- 二维码预览请求路径更新 14b09f5
- 路侧入口放开 58f2aaa
- 筛选栏交互修复 f4badb3
- 时间选择点击展示输入框修复 f8eaf8d
- 首页弹窗支持列表 4467930
- 添加操作异常处理 46f32f4
- 我的页面onShow刷新用户信息 e3189d9
- 小程序名称更新 3250144
- 支付宝端列表滑动展示兼容 8705cc8

# [1.13.0](/compare/1.12.0...1.13.0) (2025-04-24)

### Bug Fixes

- 初始化方法优化、客服头像路径修改 9ff2f25
- 订单页面优化 861826f
- 多选订单页面支付后跳转逻辑修改 000f436
- 多选订单页面支付后跳转逻辑修改 9981c5c
- 多选订单页面支付后跳转逻辑修改 2b542fc
- 多选订单页面支付后跳转逻辑修改 d046eed
- 多选订单页面支付后跳转逻辑修改 49b373c
- 多选订单页面支付后跳转逻辑修改 9375a50
- 多选订单页面支付后跳转逻辑修改 e397935
- 多选订单页面支付后跳转逻辑修改 d87bd1a
- 多选订单页面支付后跳转逻辑修改 ad744fc
- 多选订单页面支付后跳转逻辑修改 744f139
- 多选订单页面支付后跳转逻辑修改 5682f26
- 多选订单页面支付后跳转逻辑修改 ef57f57
- 多选支付页面loading优化 4d412dc
- 多选支付页面loading优化 d5858ca
- 简化静默授权功能 566039c
- 支付宝小程序websocket无法连接wsbug修改 c649037
- 支付宝小程序websocket泄露bug修改 a80061a
- ws配地址更新小版本 f83a679

### Features

- 版本号更新 08b7ebb
- 版本号升级 1510f0d
- 备案号超长展示修复 690560d
- 备案申请联调 523991c
- 备案展示调整 43cd8fe
- 备案展示调整 670fb10
- 待支付列表页不展示隐藏入口 121dd4e
- 导航跳转不等待接口返回 9fcd337
- 导入刷新优化 5f0c45e
- 订单导入调试 0b37cec
- 订单导入逻辑 2efad64
- 解除无感场景刷新逻辑优化 090a4e7
- 历史订单导入静态 b5f6b9d
- 路侧备案逻辑 4232fa0
- 判断优化 4359211
- 泊位导航添加记录上传接口 4b3c6a8
- 设置登录拦截优化 c4849b3
- 提示信息展示调整 1e18418
- 添加区名 0574dc3
- 我的车辆展示修复 303546e
- 无感调试代码 c757657
- 无感调试ing 16a1b9b
- 无感交互调整 8f716d4
- 无感签约联调 3d4b276
- 无感签约协议展示调试 95d889b
- 无感添加入口开关+我的车辆页面优化 eaf0db8
- 无感优化 0f1851a
- 无感ing df69be1
- 资讯tab优化 b0c551c
- pdf 判断调整 608c26e

# [1.12.0](/compare/1.11.0...1.12.0) (2025-04-10)

### Bug Fixes

- 订单列表loading优化、初始化方法优化 0c78d2a
- 还原订单列表 939860c
- 还原订单列表 a467faf
- 还原订单列表页代码 6b52d75
- 支付结果页面提示方法优化 1ed34d1
- 支付结果页面文案调整 40e3190
- 支付结果页面文案调整 12a6761
- 支付结果页面文案调整 8106421
- ws配地址更新小版本 3310f1e

### Features

- 版本号更新 279a806
- 备案跳转调试 caa2bad
- 备案信息标题固定展示 f065cfb
- 备案信息展示逻辑补充 9b46867
- 备案状态添加 dbc5907
- 备案ing 96ab6d8
- 待离场展示优化 1567614
- 多张图片展示兼容 fa61728
- 公众号订单调试 07cff0c
- 固定展示逻辑优化 95b611f
- 解绑联调 ee28014
- 可用优惠券列表查询接口变更 d505397
- 文案调整 c8401b5
- 详情页顶部图片支持预览 6852c0a
- 消息订阅拦截补充 隐私设置优化 2409705
- 隐私设置ing 15e6250
- 优惠券入口展示条件判断 b68b534
- 在场订单优惠券查询调整 cc0a052
- 支付宝端头像更新调整 dcc6944
- service 更新 b63d564
- service更新 c2da912
- service更新 6a60881
- ts类型更新 0177b8e

# [1.11.0](/compare/1.10.0...1.11.0) (2025-04-02)

### Features

- 版本号更新 06d5ccb
- 充电展示调整 cbb379d
- 调试代码注销 391103a
- 服务费展示修复 dafebbb
- 我的页面首次进入查询用户信息 571d822
- 优惠券详情图片展示修复 1ba8b8b

# [1.10.0](/compare/1.9.0...1.10.0) (2025-03-27)

### Bug Fixes

- 待办数量刷新调整 8c07603
- 离场时间不存在就不展示 8339f80
- 离场时间加兜底15分钟 0037c5d
- 离场时间文案调整 35d1705
- 详情页面隐私设置调整 1780034
- 页面停留时间、筛选组件拆分 080533a
- 隐私设置调整 b731859
- 隐私设置调整 81712fd
- 隐私设置调整 7f9d425
- 隐私设置调整 b2f4e6e
- 隐私设置调整 50cb234
- 隐私设置调整 be31503

### Features

- 版本号更新 bfdd3bb
- 代码生成命令优化 8528661
- 倒计时文案兜底 6afe3ce
- 公众号缴费添加关单，添加异常页面 18d228f
- 公众号缴费停车时长展示逻辑调整 8f0c5c2
- 关单逻辑补充 4d0d946
- 合并支付逻辑补充 fa5ca5b
- 积分调试 8b523fa
- 积分规则调试 f24e651
- 积分联调 7ee19da
- 积分明细静态 f9596e0
- 积分明细ing 38b6186
- 积分入口判断登录态 3151252
- 积分入口隐藏 de72034
- 积分失效场景名称兜底 e51a5f6
- 积分跳转补充 c87ee57
- 积分展示更新 ed6ceda
- 缴费联调 a447d85
- 泊位导航静态 6464959
- 泊位导航需求 5086061
- 使用优惠券后0元逻辑调整 b66f472
- 无牌车查询优惠券 328f748
- 无牌车无价格场景不查优惠券 优惠券使用文案调整 9eeca5f
- 消耗积分展示优化 54458b5
- 样式优化 eee9e9f
- 异常处理+支付调试 245e6b8
- 优惠券静态 b852a55
- 优惠券选择/预览逻辑补充 5468249
- bug修复 a232759
- service 更新 79830ab
- service 更新 7e760d6
- service 更新 0ee78af
- service 更新 4f2fecf
- service更新 d9b2ff3
- service更新 298e08d
- service更新 6c86bd1
- service更新 f122ff1
- service更新 2824bec

# [1.9.0](/compare/1.8.0...1.9.0) (2025-03-20)

### Bug Fixes

- 出行页面tab跳转问题修改 c6e65bc

### Features

- 版本号更新 36cc6ce
- 代码优化 e115593
- 单车调试 2fdde01
- 单车入口隐藏 c11ccb7
- 附近页面展示更新 2429a0b
- 搜索优化 6e43097
- 文案更新 7712411
- 状态变更 a68688f
- service 更新 178e898

# [1.8.0](/compare/1.7.0...1.8.0) (2025-03-13)

### Bug Fixes

- 充电订单显示车牌号bug修改 5afd380
- 充电订单状态修改 38aad64
- 待支付详情页面bug修改 d37f729
- 订单详情页loading时隐藏底部操作按钮 09ae408
- 订单页面初始化数据两次请求bug优化 040ad2f
- 订单状态修改，退款状态优先级更高 0a26a91
- 评价入口条件判断 8f2d220
- 停车待支付详情页面无数据时添加loading、暂无数据交互 a001e9c
- 隐私设置逻辑调整 723f973
- loading修改 539fac3
- tab切换问题处理 6330045
- ts页面修改 0fc5086
- ts页面修改 9f89212
- ts页面修改 be22810
- ts页面修改 a1e7576
- ts页面修改、loading逻辑优化 0bb8093

### Features

- 版本号更新 5d1b665
- 订单隐藏优化 ebe8025
- 订单隐藏ing 3191caf
- 公众号订单评价交互优化 b7f19b7
- 历史缴费入口样式优化 6761b60
- 企业号开发联调 cca366c
- 取消发票选择场景不提示异常信息 6aaed4c
- 提示条件添加退款判断 e073cd7
- 添加强制刷新 d7c3167
- 图表样式优化 9bedcf6
- 微信发票抬头新增刷新异常修复 d392c1e
- 微信卡包抬头导入 c2aabb5
- 隐藏弹窗时清空输入框 48ae62c
- 用户注销调试 b0010ef
- 预测交互优化 d0124c1
- 预测入口查询开放 cfd6038
- 预测问题修复 18347e4
- 预测样式优化 a09d3f1
- 预警订阅样式优化 5b6224d
- 注销调试 e8e18ae
- 状态依据来源更新 f9572c9
- service 更新 c89aec5
- service 更新 c25bae9
- service更新 e7f8649
- ts校验优化 0f6ab6c

# [1.7.0](/compare/1.6.0...1.7.0) (2025-02-28)

### Bug Fixes

- 车牌号筛选框组件开发 88efde1
- 车牌号筛选框组件开发 70ddc79
- 充电订单功能 01dfe1f
- 充电订单提交 4e46caf
- 充电支付功能、开票入口 c7dfa27
- 调整扫码规则 45bfe8b
- 订单价格修改 7535317
- 订单价格修改 363c36e
- 订单价格修改 c9eb8c2
- 订单价格修改 ffa8c2b
- 多选不能点击问题修改 4ca9ac4
- 权益包页面开发 f98d87e
- 权益包页面开发 d21b314
- 入口控制 1eafaf1
- 入口控制 7dc838c
- 入口控制 d4bca56
- 扫码付款规则添加 0dfaeaa
- 退款滑动修改 70000b7
- 新增出行页面tab锁定 3ac3725
- 隐私设置查询未绑定车辆的隐私详情 c3167b3
- 隐私设置查询未绑定车辆的隐私详情 1bfce42
- 隐私设置查询未绑定车辆的隐私详情 8a3b4f2
- 隐私设置调整、充电禅道bug调整一波 cf08992
- 隐私设置提交 ec4179c
- 隐私设置修改 18e9c2e
- 隐私设置修改 00b44f5
- 隐私设置页面开发 bb9157e
- 支付宝样式兼容问题调整 10aa0d0
- 支付宝样式兼容问题调整 73aec68

### Features

- 14展示问题调整 2c58162
- 版本号更新 592d5f2
- 查询隐私配置展示 d517439
- 车位预测静态ing c977322
- 充电入口放开 bug修复 e91f638
- 充电预测+权益激活页面 032f341
- 充电桩个人企业对比 ca664cd
- 充电桩添加优惠券展示 02c55ef
- 代码生成更新 dc2680c
- 代码优化 b71b54d
- 单车入口隐藏 997e5c5
- 单车tab入口放开 68244e3
- 订单查询跟随隐私配置 86b08d3
- 丢失代码恢复 00e23e9
- 动态 37be4b8
- 堆叠+折柱混合调试 3704581
- 堆叠组件调试ing 108e31c
- 发票空兜底修复 63721a8
- 附近车场列表去除距离限制，地图默认查询范围调整为5 4a80947
- 附近锚定优化 cbd2a6b
- 附近页面添加回到顶部 fbc0f15
- 个人企业对比补充 2df7384
- 各区收入展示优化 2b95839
- 公众号订单调整 12631aa
- 公众号订单缴费隐藏停车时长字段 c19553c
- 公众号订单开发 f432a57
- 公众号订单开发ing 2e0fa27
- 公众号历史订单ing 869d813
- 公众号组件及跳转添加参数 5cb983b
- 环形图组件封装 柱状图调试ing d49ac63
- 激活逻辑补充 2ea8fa1
- 兼容备注场景超长展示 d76765c
- 进度颜色调整 a783a60
- 经营视图联调 a0767bb
- 开关配置优化 aa797a0
- 看板交互优化 8b5705c
- 看板联调 7665a98
- 看板联调ing 61d3193
- 看板联调ing 4cdf34d
- 看板图标添加 896a358
- 领导看板静态 64989ed
- 领导看板统计名称变更 15294c2
- 领导看板优化 85e05bd
- 启动充电优化 36e0839
- 权益包列表+激活调试 17f0b42
- 入口逻辑添加 83a76fc
- 时间选择组件调整 e90f7ca
- 首页附近查询场站数量调整 092b2c1
- 首页附近收藏添加收藏备注展示 54780e4
- 添加充电开关 e390d5a
- 添加兜底代码 21a58c5
- 添加默认车场展示 f08acb1
- 添加已认证展示 641410d
- 停车场图标更新 c2d4fdf
- 停车时长展示调整 63a6999
- 停车营收看板 c6408a0
- 图标动态封装优化 7a30b5b
- 图表优化 2ce3150
- 图表整体可滚动 b7c8744
- 图表tooltip优化 120dd56
- 文案变更+接口调整 38abeb4
- 我的收藏经纬度传参更新 eea0410
- 我的收藏收费停车展示调整 8d573f5
- 详情页面添加备注信息展示 cd8c495
- 样式优化 6d22dfc
- 页面静态 4ae796b
- 异常提示变更 c863118
- 隐私控制优化 80d1cc7
- 营收总览静态 b6c5eb1
- 优惠券 238df7b
- 优惠券调试 7a1c1a8
- 优惠券ing 73d67e5
- 优惠券ing b7d30b8
- 预测mock联调 3096db2
- 预警订阅隐藏交互调整 cc6edeb
- 预警订阅隐藏开发 50ad7ac
- 预警隐藏样式优化 3baa919
- 展示tooltip f9be5d7
- 站点详情页目录更新，无用文件删除 eaa80d9
- 折线图封装 fe3c626
- 支付联调 4d541a5
- 资源总览ing 13ae170
- bug修复 225b105
- service 更新 a89131e
- service 更新 e52dff9
- service 更新 7211e74
- service 更新 80381a9
- service更新 ef0680b
- service更新 1333e6c
- service更新 1f4118c
- service更新 52b3f8e
- service更新 a151fba
- service更新 2bc3320
- service更新 a17205e
- service更新 569a663
- service更新 5a52daa
- service生成更新 91afc01
- x轴展示自适应 7fe7af7

# [1.6.0](/compare/1.5.0...1.6.0) (2025-02-21)

### Bug Fixes

- 更新版本号 560d126
- 行驶证文案修改 4d1016f

### Features

- 支付宝端地理位置授权拒绝后访问周期内不再唤起（除点击地图重置） c3f63d2

# [1.5.0](/compare/1.4.0...1.5.0) (2025-02-12)

### Bug Fixes

- 调整扫码规则 f48c0c2
- 调整扫码规则 9a8bf62
- 调整扫码规则 d79a74d
- 调整扫码规则 1887eed
- 调整扫码规则 1156af2
- 调整扫码规则 b932e37
- 扫码支付新增规则 35a827a
- 扫码支付新增规则 f6e32df
- 扫码支付新增规则 adaa8bf
- 扫码支付新增规则 2219498
- 扫码支付新增规则 101e219
- template 飘红 ac99eaa

### Features

- 分页请求添加判空拦截 cc436b6
- 高级授权/手机号注册失败场景清空token 9f31ef1
- 扫码逻辑整合优化 16fc0b8
- 扫一扫中转页面兜底 33f9b11
- 停车中展示优化 7df4066
- 通道静态码开发 5a82b9f

# [1.4.0](/compare/1.3.0...1.4.0) (2025-01-22-2)

### Bug Fixes

- 接口不断刷新bug调整 9549273
- 接口不断刷新bug调整 ab792d2
- 删除未定义方法 312a60b

### Features

- 版本号更新 dbd4133
- 登录页样式优化 57754ed
- 客服电话从接口获取 1ed4b3c
- 无刷新token时重置登录缓存态 f65e8f7
- service 更新 2a26fd9
- service 生成更新 a0e6872
- service生成更新 848da83
- service生成更新 13285eb

# [1.3.0](/compare/1.2.0...1.3.0) (2025-01-22)

### Bug Fixes

- 客服模块 b9db089
- 客服模块 c9fcda1
- 客服模块 5c69463

### Features

- 首页logo更新优化 7b7331d
- logo变更 596fe13

# [1.2.0](/compare/1.1.0...1.2.0) (2025-01-20)

### Bug Fixes

- 补充ts校验 30c6b4d
- 禅道bug提交 966e3db
- 禅道bug提交 896bcc7
- 禅道bug提交 0c93316
- 禅道bug提交 396dc1f
- 禅道bug提交 66471a8
- 禅道bug提交 87d513f
- 发票兼容性问题修改 1c70376
- 发票兼容性修改 6eb6245
- 关于我们协议跳转 6846824
- 客服 798ac7e
- 客服 2004031
- 客服功能模块 c5577d3
- 客服功能模块 2370335
- 客服接口 2405bbe
- 客服模块 5232c9a
- 客服模块 aaa1886
- 客服模块 43b18ea
- 客服模块 4a17dee
- 客服模块 0d917a3
- 客服模块 e9fefc1
- 客服模块 f1c33e8
- 客服模块 bbfb999
- 客服模块 c2d04a2
- 客服模块 288e4ff
- 客服模块 93f845a
- 客服模块 19eac68
- 客服模块 dd5739f
- 客服模块 a9dd54e
- 客服模块 c591cc3
- 客服模块 a025476
- 客服模块 e5fc94b
- 客服模块 4bba81b
- 客服模块 ddd5e0e
- 客服模块 ce23d77
- 客服模块 ad973b2
- 客服模块 0f61702
- 客服模块 e8f7a57
- 客服模块 a5502df
- 客服模块 1b16ce4
- 领导看板文件 176a4c1
- 领导看板文件 6a1b1f1
- 退款功能提交 5707886
- 退款功能提交 19ee37c
- 我的车辆优化 5c931f4
- 在线客服 c9983dd
- 在线客服 8e27d70
- 在线客服 36a604f
- ts校验类型添加 9e9550a

### Features

- 版本号更新 63bb6a5
- 地图重置防抖优化 7512a98
- 客服service生成 624a956
- 收费信息展示优化 8f8cc6d
- 优化需求开发 b63f067
- ci构建命令更新 cfcaedb
- onshow刷新附近模块 4a78e75
- service更新 bc3ba2f

# 1.1.0 (2025-01-06)

### Bug Fixes

- 更新 .gitignore b5b7da6
- 更新 .gitignore 632233d
- 更新 .gitignore fb1dbce
- 暂时将子组件移动到全局组件 04f41b6
- 1. 公众号跳转 2. 移除 pagespy 75a2b1b
- 1. 组件地图跳转问题 2. 闲忙情况切换 tab 没有查接口 3. 地图筛选项独立查询，未查询字段置空处理 afd65ed
- 1.默认定位调整 2.车场详情充电桩详情快慢充切换 286f6dc
- 安全距离+showModal组件替换 2d54e27
- 安卓查看列表样式 ca55319
- 绑定错误提示 36bc5d7
- 背景 3181f43
- 标题 c4c928f
- 不同机型下对话框错位 4812bfa
- 不同机型下对话框错位- 5f79bbc
- 禅道bug ba4b021
- 禅道bug 3db93d3
- 禅道bug d6317d7
- 禅道bug 06b179c
- 禅道bug提交 6c64c57
- 禅道bug提交 6f61420
- 禅道bug提交 58c73d6
- 禅道bug提交 390a9a6
- 禅道bug提交 0506ca1
- 禅道bug提交 8f1c35d
- 禅道bug提交 9d1df42
- 禅道bug提交 db36155
- 禅道bug提交 8b88f0f
- 禅道bug提交 37ea805
- 禅道bug提交 9f05b04
- 禅道bug提交 75afddb
- 禅道bug提交 afb20d4
- 禅道bug提交 28a6720
- 禅道bug提交 4c2fa6b
- 禅道bug提交 4558d25
- 禅道bug提交 e5e1ddf
- 禅道bug提交 196e514
- 禅道bug提交 23bba8f
- 禅道bug提交 bedc44e
- 禅道bug提交 84cf78b
- 禅道bug提交 3421283
- 禅道bug提交 696637c
- 禅道bug提交 30d7230
- 禅道bug提交 e01ac9a
- 禅道bug提交 8972ead
- 禅道bug提交 0a33747
- 禅道bug提交 5e98d6e
- 禅道bug提交 8e44ec0
- 禅道bug提交 3cd1561
- 禅道bug提交 10e2c63
- 禅道bug提交 148f5d5
- 禅道bug提交 a20bb65
- 禅道bug提交 3fa1a26
- 禅道bug提交 faa551e
- 禅道bug提交 c33e32c
- 禅道bug提交 5b6f7e7
- 禅道bug提交 aaab05d
- 禅道bug提交 67acb97
- 禅道bug提交 3c9e39c
- 禅道bug提交 d0ee0e3
- 禅道bug提交 5cd0efe
- 禅道bug提交 a342b6d
- 禅道bug提交 4229152
- 禅道bug提交 e08bcc9
- 禅道bug提交 acd157c
- 禅道bug提交 d7d6e84
- 禅道bug提交 61be9b6
- 禅道bug提交 e720ac0
- 禅道bug提交 ac9fe8a
- 禅道bug提交 d0f4ae0
- 禅道bug提交 0ec5916
- 禅道bug提交 5f3923e
- 禅道bug提交 9956657
- 禅道bug提交 db51800
- 禅道bug提交 ad8f24c
- 禅道bug提交 d569455
- 禅道bug提交 8e70253
- 禅道bug提交 eda6b8e
- 禅道bug提交 5807eac
- 禅道bug提交 a34068f
- 禅道bug提交 d61c5ba
- 禅道bug提交 aa6a1d6
- 禅道bug提交 dc2b504
- 禅道bug提交 8bb34eb
- 禅道bug提交 c151b0b
- 禅道bug提交 7cc6d83
- 禅道bug提交 b198dc3
- 禅道bug提交 0bba681
- 禅道bug提交 3eeadce
- 禅道bug提交 77a6a1e
- 禅道bug提交 f380eb3
- 禅道bug提交 2b8a33c
- 禅道bug提交 122fd59
- 禅道bug提交 eb35ba4
- 禅道bug提交 9fd5e44
- 禅道bug提交 e5359fa
- 禅道bug提交 a5dea00
- 禅道bug提交 b024a8f
- 禅道bug提交 34e411e
- 禅道bug提交 d81111e
- 禅道bug提交 0b71d00
- 禅道bug修改 7d0d9db
- 禅道bug修改 900ffb0
- 禅道bug修改 3edcece
- 禅道bug修改 548b812
- 禅道bug修改 d2a21ec
- 禅道bug修改 8e2bac4
- 场站详情字段修改 2390d50
- **车场详情:** 闲忙情况点击无效 365abe2
- 车辆品牌非必选 b4ef00f
- 车辆信息颜色 a3a6b00
- 充电 83c6a2b
- 充电模块 4f06f74
- 充电模块 3a04d84
- **充电桩详情:** 充电桩详情进度显示异常 e2ab6c7
- 出行tabt添加 2215ccc
- 出行tabt添加 39885ec
- 触底未触发scrolltolower e04693a
- **地图:** 初始化完成再去请求接口 a15a4ef
- **地图:** 地图针对微信端优化 05a8d4f
- **地图:** 快慢充地图价格数量显示 18198d1
- **地图:** 输入桩编号跳转详情页面 8026696
- **地图:** cover-view 优化 9a3e9a1
- **登录:** 用户协议的跳转 6e27b79
- 调试忽略文件 be58c66
- 订单禅道bug修改 e01d392
- 订单禅道bug修改 2cb2f7c
- 订单禅道bug修改 7a4d5c3
- 订单禅道bug修改 4f3975f
- 订单禅道bug修改 46e4da8
- 订单禅道bug修改 c9f0247
- 订单金额展示 65eac6c
- 订单模块开发 3acf51f
- 订单模块开发 004a0ba
- 订单展示 d689b2e
- 订单展示 618fb39
- 订单支付 d558223
- 订单支付 2cd075c
- 兜底+列表样式去掉grid 06a03b2
- 分享标题不展示 fcecb62
- 附近卡片组件字段修改 f89b326
- 该车牌已被其他用户绑定 6b823b2
- 个人不展示税号 21461d2
- **个人信息:** 个人信息修改之后不刷新 caf9057
- 关联订单跳转 a49db3e
- 滚动优化 fa31a9a
- 合并 9b56f81
- 合并冲突 1ab7894
- 接口配置 f1a5840
- 解除绑定按钮 c262564
- 卡片组件间距 28c3a10
- 卡片组件以及unocss的theme e166a96
- **客服中心:** 使用 hook 简化代码 7d82eba
- **客服中心:** 使用帮助判断是否存在 063c8e3
- **客服中心:** 添加使用帮助 7a8131b
- 列表滚动 5f5b4aa
- 列表滚动+ tab滚动条 2bebe3d
- 其他类型车牌 5670cb5
- 企业充电优惠券展示 adec4f7
- 企业充电展示修改 e6fccfa
- 切换tab重复请求 6bf030b
- 请求全部采用Service更换 d5a26e0
- 请求全部改为Service，场站详情样式修复 e7c910a
- 请求全部改为Service，场站详情样式修复- 43a69cc
- 请求全部改为Service，场站详情样式修复-- d3470ae
- 请求全部改为Service，场站详情样式修复--- b8fa72a
- 去掉用户注册协议 5534902
- **全局背景图:** 背景替换 0f234cf
- 热门资讯详情页更新 2eac5f0
- 认证通过、认证失败、申诉失败的详情页说明 4dd0fec
- 扫码入口 8abb513
- 扫码入口 f83091d
- 删除多余代码+代码格式化 512a2a6
- 删除文件 52dd3e3
- 升级 uniapp 版本 7594e7d
- 失败提示 5bbeb2b
- **收藏:** 收藏时候传经纬度 d47c382
- 首页、详情页、我的页面接口联调，axios改Service ce72614
- 首页布局使用grid-flex重写完毕 ecb54aa
- 首页布局使用grid-flex重写完毕- f5e0b25
- 首页部分样式修改 7bdf775
- 首页接口调试 7f7e919
- 首页接口基本联调完毕，准备跳转页 ef01d10
- 首页样式合并 72bbbc6
- 首页样式新设计稿重置 e3eeea3
- 首页样式新设计稿重置- 340c85a
- 首页样式新设计稿重置-- 7030d18
- 输入键盘想要那种有汉字的 b9639a7
- 输入新能源位的时候，车牌颜色自动选中绿色 07f9593
- 税号校验 063756c
- 搜索 c7526b1
- 搜索框样式 4e96a30
- 搜索框样式调整 5b6317e
- 搜索删除按钮隐藏 adff999
- **搜索:** 搜索空值判断 dca0f4e
- **搜索:** keyword 参数 4d1587f
- 特殊车牌校验 2d95af2
- 特殊车牌校验 cd2e4ca
- 特殊车牌颜色 2358d09
- 替换页面路径 4185ac3
- 替换页面路径- 7ecaf8d
- 跳转 43ed5bc
- 跳转 9185bbf
- 跳转登陆校验 b0e161b
- 停车场列表 379e5ac
- 文件格式 3c231b2
- 文件内报错以及文件名全大写开头 7ba6540
- 我的反馈跳转路径修改 cef1afa
- 无车牌进出 df24714
- 无车牌进出 385c3d1
- 无数据提示 a89f227
- 限制input输入长度 ff1b35d
- 新增第一辆车默认 cc4e872
- 新增文件 94f5a07
- 新增重新申诉接口 42f5f6c
- 修改生成命名 aa46f15
- 修改提示 bd7f3f6
- 样式部分修改 b86170f
- 样式以及文件名调整 79f3550
- 样式预修改 0a70a34
- 样式预修改- 71a3985
- 移出 demo 目录 7ec628f
- 移除引入的 unocss 预设 b450fe6
- 已有页面接口尝试联调 eabf9a1
- 已有页面接口尝试联调- ea1bcb9
- 意见反馈组件拆分;内容ts化 adb930f
- **优惠券:** 优惠券显示优化 0d3f4bc
- **优惠券:** 优惠券详情 834359a
- **优惠券:** 优惠券优化 f8bdd00
- 余额展示 111ba2e
- 圆环图 fa13cf4
- 圆环图点击 f7c3f13
- 岳阳首页样式调整 230b61d
- 岳阳首页样式调整- 8352b4e
- 岳阳首页样式调整- 460bed9
- 暂无数据组件 b9d515f
- 站场弹框详情联调 c326131
- 站点详情优化 3267cb7
- **站点详情:** 站点详情费用信息时间异常问题 e07cad0
- 支付宝小程序编译问题 b3eabe1
- 支付宝主页样式问题 d645215
- 支付联调 5f67b68
- 中心点位置 87bc4db
- 重新申诉 7a968da
- 重新申诉跳转 5c8a288
- 主页面接口完毕，添加咨询页面 5ff997b
- 主页以及我的样式、优惠券组件 e5438dd
- 注销协议展示 9a3edef
- 资讯对接联调 c77403d
- 资讯对接联调 5c12de6
- 资讯对接联调 dabee84
- 字体以及主页样式修正 8f80893
- 字体以及主页样式修正- dab2824
- 组件格式修改 20075dd
- 最多绑定5个车牌 94f5a51
- **Apopup:** 扩大关闭按钮的点击范围 d767af8
- app 图片不显示问题 517840c
- banner展位跳转 960c586
- font改变后客服和反馈页面调整 56fba3c
- h5 模式 rpx 不转换问题 dd5b021
- home页面defineProps改withDefaults 99eedc7
- http 优化 15a4a1a
- **map:** 地图优化 1145005
- patch @uni-helper/vite-plugin-uni-pages a263d25
- pinia ts 文件引入方法调整 a23e73e
- service 更新 693bd1d
- **service:** 更新 service e385e5a
- **service:** 更新 service 26721f3
- settings修改 6a1c59e
- tabBar层叠以及支付宝样式问题 9ad0d11
- tabBar层叠以及支付宝样式问题- fa2dc91
- tabBar层叠以及支付宝样式问题-- cb08fdc
- tabBar扫一扫图片 531872b
- tabBar遮罩层 75bde09
- uchart 0cff368
- ui 优化 00f0011
- **ui:** ui 细节优化 4c85f01
- upload报错 e7e181a
- **vite-config:** 更新 uniapp 版本 c849151
- watch a521a75

### Features

- 分享hook引用更新 80ee41c
- 新增发票相关页面 9cdaaeb
- 1. 升级 uniapp 版本 2. 编译慢 vite 构建优化 08c21fc
- 1. 透明标题栏 2. button 组件 8cefed7
- 1. 修复大小写导致的编译错误 2. ci 构建 85b7eae
- 1.部分引用修改 2.添加 service-codegen b4df527
- 按钮显示控制 92782af
- 版本 86109ad
- 版本号更新 ebc8cb3
- 版本号升级 e8c86ec
- 绑定失败 f520a0d
- 备案静态 6de2924
- 备案看板调试 cd432b5
- 备案联调 c1970d0
- 备案列表超长展示优化 2a9c9d3
- 边框1px展示优化 ea1c48f
- 编译更新 88dcc0a
- 编译问题 8f0a110
- 补充跳转 发票申请交互优化 693dbc3
- 部分逻辑补充 bd748bf
- 部分问题 cea4767
- 材料部分兼容pdf 032a8bf
- 测试 f9df35b
- 插件更新 9336916
- 插件更新 b661beb
- 插件迁移到分包中 062366e
- 查询充电站接口 ff7c6b4
- 查询目的地 5d0e27e
- 查询条件调整 ad5aa9c
- **场站详情:** 弹框优化 52749b6
- 场站详情页面样式 bd7db9c
- 场站详情页面以及弹窗样式 7bc64ba
- 场站详情优化 67be909
- 车辆列表 bb8df35
- 车辆品牌型号列表 cf75497
- 车辆认证 1e12aa9
- 车辆认证 084ba1c
- 车辆认证标题 2458944
- 车辆申诉 79ebad9
- 车辆信息 3f21936
- 车辆信息 0b58a56
- 车辆信息+车辆认证 40d3a51
- 车牌号输入变更 启动和订单详情页面添加加载和异常展示 d1f32b2
- 车牌号输入静态 2a074ee
- 车牌申诉取消 1fc33f9
- 车牌输入交互优化 138938d
- 车牌输入提示调整 0c42fdb
- 车牌选择提示交互补充 逻辑优化 56f3667
- 充电卡片添加附加信息 ca03c1c
- 充电列表、停车列表功能实现 0fbed69
- 充电流程优化 ddc08d5
- 充电流程优化 75b9482
- 充电完成ing 1c03147
- 充电详情ing 9739c2a
- 充电页面部分静态 71c7db2
- 充电页面开发 6a7e915
- 充电页面优化 078245e
- 充电页面优惠券增加入参 8f45ee7
- 充电站查询 d6293bb
- 充电中调试 54dead4
- 充电中动效调整 910e5df
- 充电中各页面样式调整 74a1add
- 充电中页面绘制 22953ad
- 充电中页面开发 010ad17
- 出行页面入口隐藏 487eb0e
- 打包配置持久化到配置文件 f001a06
- 代码生成工具更新 24171be
- 代码优化 bbf48c0
- 代码优化 31b4989
- 代码优化调试 875593d
- 待离场补充+搜索交互优化 1ae1949
- 待离场判断更新 a4089ac
- 单车调试 e6342d9
- 单车逻辑补充 fb0438f
- 单车优化 fc15b50
- 单车service生成 052e9e7
- 弹窗+弹出层 de41ebc
- 导航 d444d05
- 导航栏 dca50f4
- 导航栏 a0c3127
- 地图 cf7840a
- 地图调试 e82afaa
- 地图调试 67b30df
- **地图:** 调整滑动面板高度 26addf9
- 地图定位优化 b40fd74
- **地图:** 慢充总字段异常 32d4c6d
- 地图收缩调试 8086c57
- 地图图钉优化 ac6453e
- 地图样式 e6872e9
- 地图优化 a0c2b4d
- 地图ing 4792c61
- 地图layout ec9a60a
- 地址字段取值调整 48bb26b
- 登陆拦截 ebb1374
- 登录功能联调 1c6cf8c
- 登录开发 940898c
- 登录拦截 294c268
- 登录联调 7a48746
- 登录流程优化 0efcea6
- 登录校验、收藏字段调整 ca1d3f9
- 登录校验优化 c81109c
- 登录页面样式修改 487833b
- 登录状态判断修改 53d3bf4
- 第二位禁止输入数字 03e2dd9
- 点击历史搜索+存在的搜索提升位置 b930e20
- 电子发票首页、抬头信息列表、新增抬头 5344006
- 调试本地插件 3376684
- 调试代码回退 0becb53
- 调试代码删除 52892b9
- 调试优化 fd72b74
- 调整 7c1679b
- 调整 app 中间图标，去除阴影 845f500
- 调整部分样式 3fd4ce4
- 调整分包位置 3606231
- 订单及优惠券联调 61f240a
- 订单联调 18e15b2
- 订单联调 27c1707
- 订单列表修改 4a8ee5f
- 订单模块联调 ef995e2
- 订单退款展示修改 6699939
- 订单退款展示修改 c10b291
- 订单详情绘制优化 bc1652d
- 订单页面更新 代码优化 0c5d60c
- 订单页面增加tabbar dadc788
- 兜底 1b4d196
- 兜底分享标题取值修复 7dc05f1
- 兜底坐标更新 9d48166
- 多场站选择 7ce9c04
- 二维码接口联调 unocss规则添加 开始充电 订单详情逻辑补充 5467662
- 发票功能开发 c7057a4
- 发票功能开发 d2e6150
- 发票静态ing c30c31e
- 发票抬头 7d992eb
- 发票信息 d10cfa1
- 发票信息联调 46626bb
- 发票中心充电开票入口隐藏 a2a4b37
- 防抖添加 95281e7
- 防抖优化 9bdeae5
- 防止重复请求 2071f9b
- 分包调整，无关文件删除 4235563
- 分包配置修复 8cb0f4f
- 分享标题修改 cc84b07
- 分享更新 14f66a7
- 分享配置 7717bca
- 分享配置更新 9fcadbd
- 分页hook添加总数量返回 c8294d5
- 服务层生成工具 a106815
- 服务协议 95e92b2
- 附近+微信公众号缴费优化 4fed3db
- 附近静态 e793080
- 附近静态中 d3e77dc
- 附近逻辑调试ing 7d70441
- 附近模块判断优化 71ee98a
- 附近请求逻辑优化 64c4568
- 附近页面不接收参数指定 2ee95f9
- 附近页面不再先查询默认位置的场站 74ff86b
- 附近页面调试 07164e9
- 附近页面开发 711d21d
- 附近页面清空按钮优化 86151e9
- 附近页面请求次数优化 d16feae
- 附近页面收藏按钮跳转 8e38a11
- 附近页面手势滑动优化 061f935
- 附近站点列表 277b710
- 附近ing 08e48cd
- 附近ing eca8185
- 富文本协议内容从详情接口获取 b1b9dc9
- 高德地图搜索 7c9358e
- 高德搜索数据格式 b02f453
- 个人产权单位类型取值变更 075fdc5
- 更多入口添加判断 fbfd3b7
- 更改modal弹窗 2e3582c
- 更新 service 90076c0
- 更新 service 18899ed
- 更新 uni 版本 25ae096
- 更新基础包版本到最新，更新 uniapp appid cca6746
- 公共服务 939c7ca
- 公众号缴费页面兼容支付宝环境，去除调试代码 cada2f0
- 公众号缴费异常提示优化 e3a2e8e
- 公众号缴费优化，备案联调 dab1658
- 公众号支付添加防抖 4c07dd6
- 忽略配置更新 d4736a7
- 忽略配置更新 18ad6ba
- 忽略配置更新 a697647
- 环境变量变更 c2204e0
- 环境判断OS_TYPE 7b1d04f
- 环形添加加载交互 9a47a0a
- 加载、异常优化 e473a86
- 键盘输入交互优化 582424c
- 交互优化 1cc9117
- 交互优化 c0eca32
- 交互优化 403b3de
- 脚本更新 4c2fca1
- 缴费卡片交互更新 2d07e9d
- 缴费提示 4d75dd8
- 接口更新 bfb3955
- 接口联调 f7c5344
- 接口联调 9dbaf2e
- 接口请求封装 1b8436b
- 接口生成 b8170b1
- 接口异常也进行倒计时判断 5f13654
- 接口异常也进行倒计时判断 8face30
- 接口引用更新 cec4b53
- 结算页绘制 代码优化 7756d06
- 解决冲突 59eb142
- 解决冲突 ddbe4a8
- 静态ing 9d0d656
- 静态ing 0662aed
- 静态ing 6cae30b
- 卡片 73688dc
- 卡片提示信息超出省略优化 6787376
- 开发ing 08f9b12
- 开发ing 5c6cce2
- 开票关联订单 6021725
- 开票联调 103f2b1
- 看板格式化处理取消 6c92bd0
- 看板静态 de920e4
- 看板迁移到mine-sub分包中 01d879a
- 看板图表更新 3d31995
- 客服电话变更 d4ec99b
- 客服电话变更 搜索展开图标优化 5c26259
- 客服交流基本完毕 b0ab48f
- 离场文案变更 0470e74
- 联调 a24e4cf
- 联调 808485d
- 联调优化 167ed6f
- 联调ing c83ea2b
- 列表 e4a346f
- 列表兜底 9b1ff69
- 列表分页 8e7edd0
- 录入桩枪编码弹框优化，使用原生组件 c10098b
- 默认经纬度调整 31568ce
- 目的地列表 c7c84d2
- 内地车牌 ad1c182
- 判断提示优化 df9d483
- 判断优化 9667cae
- 配置更新 bbf9667
- 配置文件更新 e63ad90
- 拼写错误调整 c64e50f
- 品牌列表 fc90bad
- 泊位预警弹窗关闭后数据重置 21a5e39
- 企业充电&新增订阅 4b24e30
- 企业充电接口字段调整 99726eb
- 企业充电流程优化 6d6eebc
- 企业充电流程优化 4a1cbbc
- 企业充电流程优化 00de45b
- 企业样式调试 c73f248
- 企业样式兼容 46802f4
- 企业优惠标签 831f8b1
- 企业优惠标签 cc3f983
- 启动充电+充电中页面联调 35b762c
- 启动充电动画 a0c6fca
- 钱包余额充值 827a2b6
- 欠费刷新修复 8a41b52
- 欠款补缴加锁 b6fe9d0
- 切换修复 ebd295c
- 取消订阅修复 69c0466
- 取消首页充电卡片 f417ed2
- 去除纯色 ba5d08b
- 去除默认查询条件 7c4510d
- 去除accessId配置 377eb13
- 去掉测试代码 bf0d02e
- 去掉登录页面tabbar b44947d
- 去掉侨胞余额入口 cdc4a04
- 去掉修改登陆密码 8ae5a9f
- 去掉自定义弹框组件 0147573
- 全局配置会退 导航栏背景调整为白色 0b07f71
- **全局:** 页面标题修改为怀宁充电 e1913e5
- 热词搜索 4be08f7
- 热门资讯联调 c6e5f50
- 热门资讯联调，首页 ui 优化 c8c69f7
- 热门资讯详情页 0ec8b5c
- 三段式滑动面板 cfe3fb8
- 扫码+终端输入 0c00154
- 扫码充电交互补充 225a186
- 扫码逻辑调整 dfd4c9d
- 扫码优化 3fc17b4
- 扫码优化 32182c5
- 扫一扫调试+资讯展示优化 aa86d97
- 扫一扫逻辑补充 896ad4e
- 筛选栏样式优化 a8b9fdd
- 筛选气泡交互优化 9ca4a0e
- 筛选项安卓机型展示兼容 eba4588
- 删除插件 b7e073d
- 删除调试文件 aa58df0
- 删除非必要内容 4074cdc
- 删除非必要组件和页面 6602bcc
- 删除未使用页面 a5149ec
- 删除指定页面打开 5a0934d
- 申诉成功 d3f4cd0
- 审核结果 c493f60
- 生产环境链接变更 fbc1822
- 生产环境命令添加 f5fff0f
- 生产环境配置 be72cff
- 生成接口 e1b93ec
- 生成接口 89c171c
- 生成接口 4a321ff
- 生成接口 8389637
- 生成接口 b2dce2e
- 生成路径变更 d6bddc0
- 时间戳hooks 4dfaa38
- 使用 process.env.UNI_PLATFORM 访问平台 6f8d919
- 使用支付宝头像修改 822d388
- 视觉优化 86b90b3
- 收藏调整 7930b48
- 收藏功能 6f56276
- 收藏页面取消路边和充电站 7880f59
- 收藏站点查询 a4657dc
- 手动录入桩枪编码 423d73d
- 手机号登录场景重定向优化 27d50d7
- 手机号登录返回优化 43e6476
- 手机号登录加密 个人资料展示优化 0a58c17
- 手机号登录流程优化 d716609
- 首页、附近列表+详情联调 391db2d
- 首页+附近交互逻辑补充 d704fac
- 首页标题栏自定义 3630830
- 首页充电/停车中改为动图 21519ff
- 首页充电状态联调 54b0ecb
- 首页弹窗 3fa8a72
- 首页附近去掉距离限制 15d291a
- 首页附近页面充电入口隐藏 9dd12f6
- 首页静态 de4dccc
- 首页静态中 225c31f
- **首页:** 开屏幕帘 670a4fc
- 首页联调 d488303
- **首页:** 幕帘异常 168be55
- 首页欠费笔数展示数据源更新 74bb97d
- **首页:** 首页充电状态优化 4700d49
- 首页停车卡片调试 a47bdb5
- 首页停车卡片交互优化 3fda565
- 首页透明标题栏以及unocss前缀 8078300
- 首页页面部分样式 6ab5ccd
- 首页优化 59bc2e8
- 首页圆角优化，时长英文版兼容秒 9f6f87c
- 首页资讯 397a653
- 首页logo更新 cf0f49d
- 首页tab+金刚位排序 fc3b86a
- 首页tabbar展示修复，停车卡片刷新优化 b01f140
- 授权相关、防抖 0ce5209
- 刷新token时重新获取用户信息 3405e4d
- 搜素框 75c3986
- 搜索 8bdafeb
- 搜索调整 b5a9b0a
- 搜索页面开发 cbd397c
- 搜索页隐藏充电站 594e547
- 搜索页站点列表 96dd9d1
- 抬头信息联调 81b4daf
- 提示modal使用封装的方法 b7f852b
- 添加 docker 构建脚本 0dada0c
- 添加 unocss 部分自动完成 b1c928f
- 添加车牌输入静态ing ba662fe
- 添加地理位置授权打印 e40381c
- 添加朗新 icon 9960494
- 添加默认抬头 3c32da6
- 添加生成ci 32b7cc0
- 添加实人验证，通过 hbx 进行打包 7836e0b
- 添加微信支付宝上传预览构建脚本 97d23f3
- 添加小程序调试工具 47bfd49
- 添加颜色色值 3f9ece8
- 添加异常兜底 c50293f
- 添加重新输入的入口 ef25b1a
- 添加H5跳转方法 761af4d
- 跳转更新 1f9e6b9
- 跳转开票信息 a11d99a
- 跳转站点详情 5698c93
- 停车备案圆环图 38691bb
- 停车场列表兜底 7cd6548
- 停车场扫码缴费修复 8e69d26
- 停车详情 0102e39
- 停车中图标调整成静态 e88c50e
- 通用月卡 8b0436a
- 通用月卡 da7df3b
- 同步代码 bf42dd6
- 透明标题栏优化 cf78e8f
- 透明标题栏优化 780b3d0
- 退出登录接口 7fbdb04
- 退款联调 7611546
- 拖拽交换代码优化 d56c439
- 完成收藏、预警订阅联调逻辑 6dfe734
- 完善错误处理组件与hooks d990bad
- 完善弹出层组件 87a23dc
- 完善新增发票表单 cb3a826
- 完善list b0e0732
- 完善list组件 da308c9
- 微信地图开发 b095d14
- 微信地图开发 ee6c8d5
- 微信登录，初步实现 660fb48
- 微信登录请求参数修改 5ddff2a
- 微信端添加检测授权提示 04cb811
- 微信端无法滑动修复 0d525b6
- 微信端ci生成调试 6f70edd
- 微信公众号调试 b5c6603
- 微信公众号交费 2541603
- 微信泊位预警模板id更新 e1ddd62
- 微信ci调试 7f1f50d
- 文案变动、分享优化 3da2b6b
- 文案变更，公众号支付测试环境接口成功默认跳转首页 b4323ce
- 文案调整 f9ad0ec
- **我的:** 1. 页面地址调整 2. ui 优化 af73816
- **我的:** 背景色调整 1580933
- 我的车辆列表 cde93af
- 我的初始化逻辑修改 43b6406
- **我的:** 关于我们等修改 7d0698e
- 我的开发完成 0a69b60
- 我的收藏/卡券页面以及nearCharge重写 c614cba
- 我的收藏/卡券页面以及nearCharge重写- a9e08fe
- 我的搜索逻辑优化 522f462
- 我的搜索删除效果 55f6959
- **我的:** 我的轮播优化 b6307e3
- **我的:** 新增优惠券 eabc533
- 我的页面调试 ddb74a5
- 我的页面添加看板入口 4a80f03
- 我的月卡-月卡办理 bfc4b1d
- **我的:** tabbar 等调整 c176b61
- 无用文件清除 fe13345
- 误删文件恢复 0fc7a85
- 显示非聚合点 0210fa7
- 详情优化、隐私政策 d5ca8e4
- 详情优化、隐私政策等 d2818e5
- 消息订阅ing b0f989f
- 消息模板代码添加 7c1cab7
- 小程序名称更新 14814e9
- 小程序名称更新 678d677
- 协议弹窗交互优化 8c9f15d
- 协议跳转 b7a4eb7
- 协议跳转 92fb618
- 新建添加车辆页面 d8c27f2
- 新增单标签组件 385e648
- 新增弹出层组件 ab1b215
- 新增发票订单页 256eb3f
- 新增发票相关组件 18fec48
- 新增分享功能 a21d4f5
- 新增客服中心、问题反馈、反馈列表页面 74b73eb
- 新增客服中心首页 93e1901
- 新增跳转微信小程序方法 e0d0ba2
- 新增跳转微信小程序方法 ca01bc9
- 新增跳转微信小程序方法 3074352
- 新增网络错误、设置页、注销页、帮助 84bc607
- 新增我的订单 c2a482a
- 新增我的钱包 19c6182
- 新增页面跳转组件 02e5acc
- 新增预付充电 721f128
- 新增hook useErrorHandle 1c9beac
- 新增rules、新增卡片、通知、缺省组件 2bbe230
- 性别选择器调整 64eac2d
- 修复popup支付宝无遮罩问题 1a729b4
- 修改列表 e67951e
- 修改密码 3e4ca59
- 修改缺省 eaabb0f
- 修改手机号 91de758
- 修改手机号 d8e430a
- 修改手机号 79e8ed1
- 修改list组件 b32313e
- 选/绑车牌 5af6e79
- 选择车场 e1b85c7
- 选择车辆交互 32430b4
- 选择发票抬头 7a341f5
- 选择套餐 b6a22c9
- 样式调整 430d41e
- 样式调整 6b2e480
- 样式问题修复 e711c53
- 样式修改 649f9ec
- 样式优化 aa8ce21
- 样式优化 ec21fbd
- 样式优化 307042e
- 页脚添加 fe179e2
- 页脚文案更新 8fcb0aa
- 异常文案更新 780cf9f
- 意见反馈联调 0f4503c
- 意见反馈修改 30d2c5b
- 引用恢复 05076ef
- 隐藏注销入口、昵称添加长度限制、登录入口添加防抖 870a638
- 用户相关优化 e03e9da
- 优化 703109c
- 优化 7e04c46
- 优化代码 243fd8f
- 优化反馈相关逻辑 07a4782
- 优化列表组件展示 7150b5c
- 优化企业订单信息 03dc21d
- 优化字体图标 543c56f
- 优惠券部分静态 7ea8cc2
- 优惠券分页处理 5b19d22
- 优惠券联调 超充调试 d6b134d
- 优惠券列表优化 121899d
- 优惠券显示优化 fa1d12c
- **优惠券:** 优惠券多种状态优化 c613ac8
- 优先筛选调整 a1ec9d2
- 预充订单调试 d87d32d
- 预充添加防抖 136b221
- 预警订阅、我的收藏静态 6b6dfee
- 原生插件 bd5926e
- 圆形裁剪ing 71687d9
- 暂存 b47d986
- 暂存 703a7fd
- 暂存 367f243
- 展示优化 df5ec9c
- 展示优化 87108e1
- 展位调试 7c43d4f
- 展位跳转tab栏判断调整 735d925
- 站场轮播 9e7738c
- 站场收藏联调 aef6289
- 站场详情跳转地图 5907283
- 站点标签展示调整 737beb7
- 站点显示 25a125f
- 站点详情 466acca
- **站点详情:** 逻辑优化 2740b28
- 站点详情页面闲忙情况兼容 8b26e38
- 账号与安全 80d6f3e
- 账户与安全 0848e6c
- 账户与安全 2477a99
- 账户注销驳回+冻结 0800f26
- 之前文件删除 tab栏更新 ece59d9
- 支付宝登录联调 f124801
- 支付宝登录联调 部分样式优化 ac5f5a9
- 支付宝端兼容优化 74a4ba3
- 支付宝附近调试 e21563f
- 支付宝设置全局分享配置 c2398c3
- 支付宝头像上传调整 284bece
- 支付宝样式兼容 84b1703
- 支付调试 ace62ee
- 支付调试ing 00dc139
- 支付方法&授权优化 8ef3433
- 支付方式补充ing fdbd6a4
- 支付封装调整 1b9d354
- 支付通用方法和自定义弹框组件 48f4fd7
- 支付通用方法和自定义弹框组件 88b471d
- 只有省数据时支持点击省位直接更换 ab935bc
- 中心点+切换按钮组件 3235cf8
- 主页内容主题切换完毕;我的页面样式 dc37993
- 主页内容主题切换完毕;我的页面样式初步处理 38c1c85
- 注释取消 ee87a24
- 注销 8870999
- 注销链路 b43126b
- 注销流程优化 c8608d0
- 注销申请 d3bbe6a
- 注销申请中 cf01f94
- 注销账户 208190f
- 注销账户 52c3f25
- 注销账户样式 b3c4264
- 字段调整 a5c0d04
- 字段调整、列表刷新 e2496d7
- 字段定义命名优化 6834a0e
- 字体更新 c9b8fa0
- app 地图调试 ac7d634
- app 隐藏标题栏 c9fea5b
- app端滑动修复 2804e09
- app附近滑动修复 b8689d8
- bug修复 d01617f
- bug修复 625ca3c
- bug修复 c589b82
- ci调试 0ccbb03
- ci预览微信端环境码错误修复 5cf0688
- **din 字体:** 字体统一调整为加粗 D-DIN 585f753
- echarts插件添加 b691161
- **env:** 1. 地图优化 2. 站点详情优化 d52b203
- **env:** 替换图片地址 eef4f8d
- eslint 调整 d5ebafc
- getLocation 调试 c64e6b0
- **http:** http 自动刷新 token 864c82f
- lfs 管理二进制文件 b343a04
- lock 函数 df60f6a
- logo更新 首页顶部更新 bda5f1b
- lottie添加 6e9d718
- marker点卡片 5cadf84
- miniprogram-ci 版本锁定 a3ef1b5
- miniprogram-ci更新 04d9707
- nearby 微信地图开发 529156d
- nearby 微信地图开发 d59c876
- nvue 地图开发 e9d8296
- pages.json更新 455933b
- pages.json文件更新 e1e8953
- pagesjson更新 4ab0583
- private 字段配置 103acb7
- release-it 补充 1b28b9f
- renderjs 67badb4
- route标签属性更新 d82e3a7
- service 代码 66b2476
- service 更新 bdf7634
- service 更新 f3d7140
- service 更新 0c273b5
- service 更新 27aba5a
- service 更新 406392b
- service 更新 837b0d7
- service 更新 bc87150
- service 更新 部分类型定义更新 d887940
- service 更新+首页资讯入参添加 947dcce
- service 生成更新 4a77b64
- service 生成更新 3ad7a26
- service 同步 4510922
- service 文件更新 2d65b4e
- service 文件生成更新 1fed44a
- service g更新 114db37
- service更新 897308e
- service更新 4a4f2f4
- service更新 a0906db
- service更新 b921fa1
- service更新 a0420a7
- service更新 879d898
- service更新 7f51ea8
- service更新 64de3e3
- service更新 0450e93
- service更新 1bb771c
- service更新 a00ff44
- **service:** 更新 ea6de73
- service生成 header参数处理 4bb4261
- service生成更新 fff4d7c
- service生成更新 bd5347c
- service生成更新 16c8a90
- service生成更新 c1700fc
- service文件更新 66a7f73
- tabbar、支付宝小程序配置 012e74c
- **tabBar:** 使用 layout 处理部分页面 d7f30e5
- **tabBar:** 添加 tabBar 对应页面 aa17730
- **tabBar:** 优化安全区域写法 1c1b4c2
- token过期刷新补充 c3a3c4d
- token刷新 fcc60e3
- **uni:** 更新包版本 a0516d1
- **uni:** 更新包版本，uniapp 编译 bug 解决 7022b41
- unocss 变体的实现 5630bcf
- unocss 添加不同平台 css 区分前缀 dee8d92
- unocss前缀 5263095
- webview授权兼容 e7b9256
