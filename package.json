{"name": "y<PERSON><PERSON>", "type": "commonjs", "version": "1.14.0", "miniVersion": "0.0.26", "private": true, "description": "基线小程序", "packageManager": "pnpm@8.15.1", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "prod:mp-weixin": "uni -p mp-weixin --mode production", "prod:mp-alipay": "uni -p mp-alipay --mode production", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp": "uni build -p mp-weixin", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "build:mp-weixin-dev": "uni build -p mp-weixin --mode development", "build:mp-alipay-dev": "uni build -p mp-alipay --mode development", "prepare": "node ./shell/postinstall.js && husky install && cd package/auto && pnpm install", "type-check": "vue-tsc --noEmit", "dev:upload-image": "cross-env NODE_ENV=development node script/upload-image-minio.js", "upload:image-release": "cross-env NODE_ENV=release node script/upload-image-minio.js", "upload:image-prod": "cross-env NODE_ENV=production node script/upload-image-minio.js", "generate": "node package/service-codegen/index.js && node package/service-codegen/parkService.js && node package/service-codegen/customService.js && npm run generate:format", "generate:format": "prettier --write \"src/service/**/*\" \"src/parkService/**/*\" \"src/customService/**/*\" && eslint --fix \"src/service/**/*\" \"src/parkService/**/*\" \"src/customService/**/*\"", "preinstall": "npx only-allow pnpm", "preview:mp-weixin-dev": "npm run build:mp-weixin-dev && cd package/auto && node weixinPreview.js -- dev", "preview:mp-alipay-dev": "npm run build:mp-alipay-dev && cd package/auto && node alipayPreview.js -- dev", "preview:mp-weixin-prod": "npm run build:mp-weixin && cd package/auto && node weixinPreview.js -- prod", "preview:mp-alipay-prod": "npm run build:mp-alipay && cd package/auto && node alipayPreview.js -- prod", "upload:mp-weixin-dev": "npm run build:mp-weixin && cd package/auto && node weixinUpload.js -- dev", "upload:mp-alipay-dev": "npm run build:mp-alipay && cd package/auto && node alipayUpload.js -- dev", "release": "release-it"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@climblee/uv-ui": "^1.1.20", "@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-harmony": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-alipay": "3.0.0-4030620241128001", "@dcloudio/uni-mp-baidu": "3.0.0-4030620241128001", "@dcloudio/uni-mp-jd": "3.0.0-4030620241128001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4030620241128001", "@dcloudio/uni-mp-lark": "3.0.0-4030620241128001", "@dcloudio/uni-mp-qq": "3.0.0-4030620241128001", "@dcloudio/uni-mp-toutiao": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "@dcloudio/uni-mp-xhs": "3.0.0-4030620241128001", "@dcloudio/uni-quickapp-webview": "3.0.0-4030620241128001", "@dcloudio/uni-ui": "1.5.0", "@esbuild/darwin-x64": "0.20.2", "@huolala-tech/page-spy-uniapp": "^1.9.3", "@iconify-json/mdi": "^1.1.66", "@rollup/rollup-darwin-x64": "^4.17.2", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.8", "axios": "^1.6.8", "dayjs": "1.11.10", "decimal.js": "^10.4.3", "lottie-miniprogram": "^1.0.12", "minio": "^8.0.2", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "^6.11.2", "vue": "3.4.21", "vue-i18n": "9.9.0", "wot-design-uni": "^1.3.7"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@iconify-json/carbon": "^1.1.27", "@release-it/conventional-changelog": "^9.0.4", "@soeasy/service-codegen": "^1.7.0", "@ttou/uv-typings": "^1.10.2", "@types/node": "^20.17.9", "@types/wechat-miniprogram": "^3.4.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-cloud-types": "^0.5.2", "@uni-helper/uni-ui-types": "^0.5.11", "@uni-helper/vite-plugin-uni-components": "^0.0.9", "@uni-helper/vite-plugin-uni-layouts": "^0.1.7", "@uni-helper/vite-plugin-uni-manifest": "^0.2.3", "@uni-helper/vite-plugin-uni-pages": "0.2.24", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-attributify": "^0.59.4", "@unocss/preset-mini": "^0.59.4", "@unocss/preset-tagify": "^0.59.4", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "ali-oss": "^6.20.0", "autoprefixer": "^10.4.16", "chokidar": "^3.6.0", "commitlint": "^18.4.3", "crypto": "^1.0.1", "dotenv": "^16.4.5", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-vue": "^9.19.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "release-it": "^17.11.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.69.5", "stylelint": "^16.0.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^35.0.0", "stylelint-config-standard-scss": "^12.0.0", "terser": "^5.26.0", "typescript": "^5.7.2", "unocss": "^0.58.0", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.2", "vite": "5.2.8", "vite-plugin-restart": "^0.4.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^5.1.0", "vue-global-api": "^0.4.1", "vue-tsc": "^1.0.24"}, "pnpm": {"patchedDependencies": {"@uni-helper/vite-plugin-uni-pages@0.2.24": "patches/@<EMAIL>"}}}