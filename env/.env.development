# 变量必须以 VITE_ 为前缀才能暴露给外部读取
NODE_ENV='development'
# 是否去除console 和 debugger
VITE_ENV='development'
VITE_DELETE_CONSOLE=false
ALI_OSS_REGIN=oss-cn-hangzhou
ALI_OSS_ACCESS_KEY_ID=LTAI5t7izHQfwFfmLBUGpZiJ
ALI_OSS_ACCESS_KEY_SECRET=******************************
ALI_OSS_BUCKET=donghu-test

MINIO_ACCESS_KEY=Ehwz1oY4RUlRXpkq
MINIO_SECRET_KEY=pybWZxl50H1CgNqi4eFS1LJBUcrZsZlR
MINIO_BUCKET=shantou
MINIO_ENDPOINT=minio-test.zhcsyy.com
MINIO_API_PORT=80
MINIO_USE_SSL=false
VITE_ALI_OSS_URL_PREFIX=http://minio-test.stzhcsyy.com:30219/shantou

# VITE_BASE_URL=http://10.10.0.30:30219/apigateway
VITE_BASE_URL=http://api-test.stzhcsyy.com:30219/apigateway
# websocket
VITE_SERVICE_WEBSOCKET_URL=ws://customer-service-test.stzhcsyy.com:30219/customer-service
