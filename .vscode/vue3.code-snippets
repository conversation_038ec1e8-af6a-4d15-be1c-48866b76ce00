{
  // Place your unibest 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  "Print unibest Vue3 SFC": {
    "scope": "vue",
    "prefix": "v3",
    "body": [
      "<route lang=\"json5\" type=\"page\">",
      "{",
      "  layout: 'default',",
      "  style: {",
      "    navigationBarTitleText: '$1',"
      "  },",
      "}",
      "</route>\n",
      "<template>",
      "  <view class=\"\">$2</view>",
      "</template>\n",
      "<script lang=\"ts\" setup>",
      "//$3",
      "</script>\n",
      "<style lang=\"scss\" scoped>",
      "//$4",
      "</style>\n",
    ],
  },
  "Print unibest style": {
    "scope": "vue",
    "prefix": "st",
    "body": ["<style lang=\"scss\" scoped>", "//", "</style>\n"],
  },
  "Print unibest script": {
    "scope": "vue",
    "prefix": "sc",
    "body": ["<script lang=\"ts\" setup>", "//$3", "</script>\n"],
  },
  "Print unibest template": {
    "scope": "vue",
    "prefix": "te",
    "body": ["<template>", "  <view class=\"\">$1</view>", "</template>\n"],
  },
}
