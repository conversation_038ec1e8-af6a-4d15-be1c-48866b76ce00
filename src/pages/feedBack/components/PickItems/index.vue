<template>
  <view>
    <picker @change="firstPickerChange" :value="firstSelectorIndex" :range="firstArray">
      <view class="border-box flex justify-between items-between">
        <view class="text-8 font-medium">问题类型</view>

        <view class="text-#999999 text-8">
          {{ firstArray[firstSelectorIndex] }}
          <image class="w-48rpx h-48rpx" src="/static/common/right-arrow.png"></image>
        </view>
      </view>
    </picker>

    <picker @change="secPickerChange" :value="secSelectorIndex" :range="secondArray">
      <view class="border-box flex justify-between items-between">
        <view class="text-8 font-medium">问题选择</view>

        <view class="text-#999999 text-8">
          {{ secondArray[secSelectorIndex] }}
          <image class="w-48rpx h-48rpx" src="/static/common/right-arrow.png"></image>
        </view>
      </view>
    </picker>
  </view>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { MainClass } from '../../feed-back';

const props = defineProps({
  selectArray: {
    type: Array as () => MainClass[],
    required: true,
  },
});
// 选择序号
const firstSelectorIndex = ref<number>(0);
const secSelectorIndex = ref<number>(0);

// 选择框变更
function firstPickerChange(e: any) {
  console.log('1选择变更', e.detail.value);
  firstSelectorIndex.value = e.detail.value as number;
}
function secPickerChange(e: any) {
  console.log('2选择变更', e.detail.value);
  secSelectorIndex.value = e.detail.value as number;
}

// 变为正常数组的第一、第二个数组
const firstArray = computed(() => props.selectArray.map((item) => item.mainClassName));
const secondArray = computed(() =>
  props.selectArray[firstSelectorIndex.value].subclass.map((item) => item.mainClassName),
);
</script>

<style lang="scss" scoped>
.border-box {
  @apply rounded-8px bg-white p-24rpx mb-16px mx-4;
}
</style>
