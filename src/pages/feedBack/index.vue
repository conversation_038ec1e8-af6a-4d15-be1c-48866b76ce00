<!-- <route lang="json5">
{
  style: { navigationBarTitleText: '反馈页面' },
}
</route> -->
<template>
  <view class="bg-#F6F7F9 h-full overflow-hidden">
    <OSSImg
      className="fixed top-0 left-0 bg-center bg-no-repeat bg-cover"
      width="750"
      height="440"
      src="/images/mine/service/bg1.png"
    />
    <view class="pos-relative w-702rpx mt-224rpx m-a">
      <pickItem :selectArray="selectArray" />
      <view class="border-box">
        <view class="flex justify-between">
          <view class="text-8 font-medium">问题描述</view>
          <view class="text-#999999 text-8">{{ inputText.length }}/500</view>
        </view>
        <view>
          <textarea
            class="rounded-md my-16rpx bg-gray-100 h-70 w-full p-32rpx text-8 box-border"
            type="text"
            placeholder="请输入详细内容(限500字)"
            :maxlength="500"
            v-model="inputText"
          ></textarea>
        </view>
        <view>相关问题的截图或照片（可选）</view>
        <view class="flex flex-wrap mt-16rpx">
          <view
            v-for="(item, index) in imgList"
            :key="index"
            class="w-160rpx h-160rpx rounded-8rpx mr-16rpx mb-16rpx pos-relative"
          >
            <image
              :src="item"
              class="w-160rpx h-160rpx rounded-8rpx mr-16rpx mb-16rpx"
              mode="aspectFill"
            />
            <view
              class="pos-absolute top--10rpx right--10rpx w-36rpx h-36rpx"
              @click="deleteImg(index)"
            >
              <uni-icons type="clear" size="18" color="#999999" />
            </view>
          </view>
          <view
            class="w-160rpx h-160rpx rounded-8rpx mr-16rpx mb-16rpx text-12 text-center leading-160rpx text-#333 bg-#F5F5F5"
            @click="chooseImage"
          >
            +
          </view>
        </view>
      </view>
      <AppButton color="#F33813" className="'w-678rpx'">提交</AppButton>
    </view>
    <AppBottom className="mb-92rpx ">
      <view class="w-750rpx text-8 text-center color-#ff4935 mb-92rpx">我的反馈</view>
    </AppBottom>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import AppBottom from '@/components/AppBottom/index.vue';
import AppButton from '@/components/AppButton/index.vue';

import pickItem from './components/PickItems/index.vue';
import { MainClass } from './feed-back';
// 提建议页面切换展示
const pageShow = ref<boolean>(true);

// 图片列表
const imgList = ref<string[]>([
  'https://img1.baidu.com/it/u=1198509217,2438059415&fm=253&fmt=auto&app=120&f=JPEG?w=171&h=114',
  'https://img0.baidu.com/it/u=1908777673,784807360&fm=253&fmt=auto&app=138&f=JPEG?w=200&h=200',
]);

/**
 * @description: 删除图片
 * @param {*} index
 * @return {*}
 */
const deleteImg = (index: number) => {
  imgList.value.splice(index, 1);
};

/**
 * @description: 选择照片
 * @return {*}
 */
const chooseImage = () => {
  uni.chooseImage({
    count: 9,
    success: (res) => {
      imgList.value = [...imgList.value, ...res.tempFilePaths];
    },
  });
};

// 提建议按钮
function submitSuggest() {
  pageShow.value = !pageShow.value;
  // 如果展示，则请求展位列表
  if (pageShow.value) {
    getStandList();
  }
}

// 提交按钮
function submit() {
  console.log('触发请求');
}

// 选择框内参数
const selectArray = ref<MainClass[]>([
  {
    mainClassName: '功能优化问题',
    subclass: [
      {
        mainClassName: '停车类',
      },
      {
        mainClassName: '充电类',
      },
    ],
  },
]);

// 输入框
const inputText = ref<string>('');

// 获取反馈列表
onMounted(() => {
  // ProblemFeedbackService.postUserFeedbackCustomerServiceCenterGetFeedbackList().then((res) => {
  //   console.log('获取反馈列表', res);
  // });
});

// 获取展位列表
async function getStandList() {
  // const result = await QuestionTypeService.postUserProblemConfigQueryListProblemConfig({
  //   mainClass: ['feedback'],
  // });
  console.log('展位列表', result);
}
</script>

<style lang="scss" scoped>
.img-bg {
  width: 240px;
  margin-top: 22%;
}

.border-box {
  @apply rounded-8px bg-white p-32rpx mb-32rpx mx-8rpx;
}
</style>
