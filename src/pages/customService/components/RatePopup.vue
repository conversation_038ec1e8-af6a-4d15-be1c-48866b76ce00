<template>
  <view class="appraise">
    <view class="invite-text">
      {{ inviteEvaluateText }}
    </view>
    <view class="appraise-wrap">
      <view>
        <view
          v-for="item in list"
          :key="item.name"
          class="flex flex-col items-center"
          @click="clickAppraise(item.icon, item.level)"
        >
          <OSSImg
            className="shrink-0"
            :width="80"
            :height="80"
            :src="`/images/customService/${getIcon(item)}.png`"
          />
          <view>{{ item.name }}</view>
        </view>
      </view>
    </view>
    <view class="appraise-button" @click="submitEvaluate">提交评价</view>
  </view>
</template>

<script lang="ts" setup>
import OSSImg from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    value: boolean;
    inviteEvaluateText: string;
    id: string;
  }>(),
  {
    value: false,
    inviteEvaluateText: '请您对本次服务做出评价',
    id: '',
  },
);
const emit = defineEmits(['submit']);

const level = ref<number>(0);
const commentFlag = ref<string>('');
const list = ref([
  {
    name: '满意',
    icon: 'satisfied',
    level: 1,
  },
  {
    name: '一般',
    icon: 'general',
    level: 2,
  },
  {
    name: '不满意',
    icon: 'dissatisfied',
    level: 3,
  },
]);

// 点击评价
const clickAppraise = (icon: string, index: number) => {
  commentFlag.value = icon;
  level.value = index;
};
/**
 * 提交评价
 */
const submitEvaluate = () => {
  if (!level.value) {
    uni.showToast({
      title: '请进行评价',
      icon: 'none',
    });
  }
  emit('submit', { level: level.value, id: props.id });
};

const getIcon = (item) => {
  return commentFlag.value === item.icon ? item.icon : `${item.icon}-gray`;
};
</script>
<style lang="scss" scoped>
.invite-text {
  padding: 24rpx 59rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.appraise {
  width: 518rpx;
  font-size: 32rpx;
  text-align: center;
  background: #fff;
  border-radius: 24rpx;

  &-title {
    margin-bottom: 44rpx;
    font-size: 36rpx;
    font-weight: 600;
    text-align: left;
  }

  &-wrap {
    padding-bottom: 36rpx;
    margin-top: 44rpx;
    border-bottom: 1px solid #eee;

    > view {
      display: flex;
      justify-content: space-around;
      padding: 0 59rpx;
    }

    image {
      width: 100rpx;
      height: 100rpx;
    }
  }

  &-button {
    padding: 24rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: #56be66;
    text-align: center;
  }
}
</style>
