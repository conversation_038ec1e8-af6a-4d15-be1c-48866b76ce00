<template>
  <view class="unresolved-box">
    <view class="unresolved-title">反馈类型</view>
    <view class="unresolved-content">
      <view
        v-for="x in feedbackList"
        :key="x.value"
        :class="['unresolved-content-item', msg.feedbackType === x.value ? 'isActive' : '']"
        @click="chooseFeedback(index, x.value)"
      >
        {{ x.title }}
      </view>
    </view>
    <view class="submit-btn" @click="submitFeedback(msg, index)">提交</view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { CTerminalOnlineCustomerServiceRelatedInterfacesService } from '@/customService/services/CTerminalOnlineCustomerServiceRelatedInterfacesService';

const props = withDefaults(
  defineProps<{
    msg: { [key: string]: any };
    index: number;
  }>(),
  {
    msg: () => ({}),
    index: 0,
  },
);

const emit = defineEmits(['chooseFeedback', 'submitFeedback']);
const feedbackList = ref([]);
onMounted(() => {
  getFeedbackList();
});
/**
 * 获取反馈类型 列表
 */
const getFeedbackList = async () => {
  const [, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineGetQuestionFeedback(
      {},
    );
  if (res?.code === '10000') {
    const list: any = res.data || [];
    feedbackList.value = list.map((x: any) => {
      return { title: x.dictLabel, value: x.dictValue };
    });
  }
};

/**
 * 选择反馈类型
 * @param index 点击的反馈类型在列表中的索引
 * @param val 点击的反馈类型的值
 */
const chooseFeedback = (index: any, val: any) => {
  emit('chooseFeedback', { index, val });
};
/**
 * 提交反馈
 * @param msg 当前消息
 * @param index 点击的反馈类型在列表中的索引
 */
const submitFeedback = (msg: any, index: any) => {
  emit('submitFeedback', { msg, index });
};
</script>

<style scoped lang="scss">
.unresolved-box {
  margin-top: 37rpx;
  color: #333;
  background-color: #fff;
  border-radius: 24rpx;

  .unresolved-title {
    padding: 24rpx 28rpx 0;
    font-size: 32rpx;
    font-weight: 500;
  }

  .unresolved-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 24rpx 28rpx 4rpx;
    margin-top: 24rpx;
    text-align: center;

    &-item {
      padding: 12rpx 16rpx;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      line-height: 42rpx;
      border: 1px dashed #999;
      border-radius: 8rpx;
    }

    .isActive {
      color: #56be66;
      background: #eef8ef;
      border: 1px solid #56be66;
    }
  }

  .submit-btn {
    padding: 24rpx 0;
    font-size: 36rpx;
    font-weight: 500;
    color: #56be66;
    text-align: center;
    border-top: 1px solid #eee;
  }
}
</style>
