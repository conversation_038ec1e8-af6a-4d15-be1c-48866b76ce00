<template>
  <view class="w-518rpx bg-white-color rounded-24rpx">
    <view class="box-title">请选择咨询的问题</view>
    <view class="tabs-box">
      <view
        v-for="item in tabDict"
        :key="item.value"
        :class="activeTab == item.value ? 'active-tab' : ''"
        @click="activeTab = item.value"
      >
        {{ item.title }}
      </view>
    </view>

    <view
      v-for="(item, index) in hotQuestionList"
      :key="index"
      class="question-list"
      @click="addChat(item)"
    >
      <view>{{ item.question }}</view>
      <uni-icons type="right" size="20" color="#999999"></uni-icons>
    </view>

    <view class="reset-question" @click="changeHotQuestion">
      <image src="@/static/customer/icon-loop.png" class="w-40rpx h-40rpx mr-8rpx" />
      <text>换一换</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useChatStore } from '@/store';
import { CTerminalOnlineCustomerServiceRelatedInterfacesService } from '@/customService/services/CTerminalOnlineCustomerServiceRelatedInterfacesService';
import { onMounted, watch } from 'vue';
import { TabItem, HotQuestion } from '../custom-service';

const emit = defineEmits(['send', 'resetQuestionList']);

// 子传父，用于在消息队列添加新内容

// tabs顶部激活列表
const tabDict = ref<TabItem[]>([
  {
    title: '停车',
    value: 1,
    code: 'parking',
  },
  {
    title: '充电',
    value: 2,
    code: 'charging',
  },
  {
    title: '其他',
    value: 3,
    code: 'other',
  },
]);
const queryParams = ref({
  pageSize: 5,
  pageNum: 1,
  tenantId: '1',
});
// tabs顶部激活key
const activeTab = ref<number>(1);
const total = ref<number>(0);
// 知识库问题列表
const hotQuestionList = ref<HotQuestion[]>([]);

const props = withDefaults(
  defineProps<{
    question: string;
  }>(),
  {
    question: '',
  },
);

/**
 * 切换activeTab时获取知识库列表
 */
watch(
  () => activeTab.value,
  (val) => {
    console.log('activeTab.value', activeTab.value);
    getQuestionList(val);
  },
);
onMounted(() => {
  getQuestionList(activeTab.value);
});
/**
 * 获取常见问题列表
 * @param activeTab
 */
const getQuestionList = async (activeTab: any) => {
  const params: any = queryParams.value;
  if (props.question) {
    // 模糊搜索时，不传scene
    params.question = props.question;
  } else {
    const scene = tabDict.value.find((x) => x.value === activeTab)?.code;
    params.scene = scene;
  }
  const [, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineQuestionList(
      params,
    );
  if (res?.code === '10000') {
    hotQuestionList.value = res.data;
    total.value = res.total;
  }
  // 未匹配到问题时返回提示消息，不显示问题列表
  if (props.question && hotQuestionList.value.length === 0) {
    emit('resetQuestionList');
  }
};
// 点击知识库后向父组件传值
const addChat = (item: HotQuestion) => {
  emit('send', item);
};

// 知识库换一批
const changeHotQuestion = () => {
  if (queryParams.value.pageNum * queryParams.value.pageSize >= total.value && total.value > 0) {
    queryParams.value.pageNum = 1;
  } else {
    queryParams.value.pageNum++;
  }
  getQuestionList(activeTab.value);
};
</script>

<style lang="scss" scoped>
.box-title {
  padding: 25rpx 45rpx;
  font-size: 35rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);
  border-top-left-radius: 8rpx;
  border-top-right-radius: 20rpx;
}

.tabs-box {
  display: flex;

  // width: 500rpx;
  margin: 24rpx 28rpx;
  font-size: 28rpx;
  color: #666;

  // justify-content: space-around;
  text-align: center;

  > view {
    padding: 12rpx 0;
    margin-right: 50rpx;
  }

  .active-tab {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    border-bottom: 6rpx solid #56be66;
  }
}

.question-list {
  // width: 500rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  margin: 0 28rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.reset-question {
  display: flex;
  justify-content: center;
  padding: 25rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  color: #56be66;
}
</style>
