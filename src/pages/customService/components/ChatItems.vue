<template>
  <view class="px-32rpx">
    <view v-if="msg.messageType == 'content'" class="grid gap-y-32rpx">
      <!-- 右侧对话框 -->
      <view v-if="isMySelf" class="flex justify-end my-2">
        <view>
          <view class="text-24rpx mr-2 text-#666666 flex justify-end">{{ msg.sendTime }}</view>
          <view class="flex justify-end mr-2">
            <view :class="['read-status', msg.isRead ? 'read-already' : 'read-not']">
              {{ msg.isQuestion ? '' : msg.isRead ? '已读' : '未读' }}
            </view>
            <image
              v-if="msg.contentType === 'image'"
              :src="msg.content"
              mode="widthFix"
              style="max-width: 400rpx"
              :class="msg.defaultImg ? 'default-content-img' : ''"
            />
            <view v-else class="chart-item-right p-20rpx text-8">{{ msg.content }}</view>
          </view>
        </view>
        <view class="head-icon bg-#999999 mr-2">
          <image
            class="w-64rpx h-64rpx"
            v-if="userInfo.avatar"
            mode="aspectFill"
            :src="userInfo.avatar"
          />
          <OSSImg width="64" height="64" v-else src="/images/customer/avatar.jpg" />
        </view>
      </view>

      <!-- 左侧对话框 -->
      <view v-if="!isMySelf" class="flex justify-start my-2">
        <view class="head-icon bg-#999999 ml-2">
          <OSSImg width="64" height="64" src="/images/customer/logo.png" />
        </view>
        <view class="ml-20rpx">
          <view class="flex items-center">
            <text class="text-28rpx font-medium leading-40rpx">小智</text>
            <text class="text-24rpx ml-2 text-#666666 flex justify-start">{{ msg.sendTime }}</text>
          </view>
          <view class="flex flex-col w-518rpx">
            <view
              class="chart-item-left text-8 bg-white text-#333333"
              v-if="msg.contentType === 'question'"
            >
              <QuestionBox
                @send="addChat"
                @resetQuestionList="resetQuestionList(index)"
                :question="msg.self_question"
              />
            </view>

            <view v-else class="chart-item-con">
              <image
                v-if="msg.contentType == 'image'"
                :src="msg.content"
                mode="widthFix"
                style="max-width: 400rpx"
              />
              <view v-else-if="msg.contentType == 'answer'">
                <view class="answer-box">
                  <view class="answer-title">{{ msg.question }}</view>
                  <view>{{ msg.content }}</view>
                </view>
                <view v-if="msg.content.length > 99" class="more-answer" @click="getMore">
                  查看更多
                  <uni-icons type="right" color="#56be66"></uni-icons>
                </view>
              </view>
              <text v-else>
                {{ msg.content }}
              </text>
            </view>

            <!-- 自动回复评价 -->
            <view v-if="msg.contentType == 'answer'" class="question-rate">
              <view
                class="question-rate-item"
                style="margin-right: 24rpx"
                @click="handleRateQuestion(true, msg, index)"
              >
                <uni-icons
                  type="hand-up-filled"
                  size="20"
                  :color="msg.isResolved ? '#56BE66' : '#ccc'"
                ></uni-icons>
                <text :class="msg.isResolved ? 'question-rate-item-blue' : ''">已解决</text>
              </view>
              <view class="question-rate-item" @click="handleRateQuestion(false, msg, index)">
                <uni-icons
                  type="hand-down-filled"
                  size="20"
                  :color="msg.isResolved === false ? '#FF8F1F' : '#ccc'"
                ></uni-icons>
                <text :class="msg.isResolved === false ? 'question-rate-item-orange' : ''">
                  未解决
                </text>
              </view>
            </view>
            <!-- 反馈类型-->
            <FeedbackType
              v-if="msg.isResolved === false && msg.hasSubmit !== true"
              :msg="msg"
              :index="index"
              @chooseFeedback="chooseFeedback"
              @submitFeedback="submitFeedback"
            />
          </view>
        </view>
      </view>
    </view>
    <view v-else-if="msg.messageType == 'withdraw_message' || msg.isWithDraw == 1" class="sys-box">
      <view class="sys-info">
        "{{ msg.serviceNickname || coreContent.nickname || '小智' }}" 撤回了一条消息
      </view>
    </view>
    <view v-else-if="msg.messageType == 'customer_close'" class="sys-box">
      <view class="sys-info">客服关闭了会话窗口</view>
    </view>
    <view v-else-if="msg.messageType == 'customer_busy'" class="sys-box">
      <view class="sys-info">当前处于忙碌状态，请稍等片刻</view>
    </view>
    <view v-else-if="msg.messageType == 'customer_status'" class="sys-box">
      <view class="sys-info">客服状态</view>
    </view>
    <view v-else-if="msg.messageType == 'blacklist'" class="sys-box">
      <view class="sys-info">由于客服将您添加至黑名单，您的消息已被拒收</view>
    </view>
    <view v-else-if="msg.messageType == 'info'" class="sys-box">
      <view class="sys-info">
        您好，将由{{ msg.serviceNickname || coreContent.nickname }}为您服务
      </view>
    </view>
    <view v-else-if="msg.messageType == 'disconnect'" class="sys-box">
      <view class="sys-info">由于您长时间没有回复，已自动结束会话！</view>
    </view>
    <wd-popup v-model="morePopup" position="bottom" background-color="#fff">
      <view class="more-popup">
        <uni-icons
          type="closeempty"
          size="24"
          style="float: right"
          color="#D8D8D8"
          @click="morePopup = false"
        ></uni-icons>
        <view class="more-title">
          {{ moreMsg.question }}
        </view>
        <view class="more-content">
          {{ moreMsg.content }}
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import QuestionBox from '@/pages/customService/components/QuestionBox.vue';
import FeedbackType from '@/pages/customService/components/FeedbackType.vue';
import { useUserStore } from '@/store';

const props = withDefaults(
  defineProps<{
    isMySelf: boolean;
    msg: { [key: string]: any };
    index: number;
    coreContent: { [key: string]: any };
  }>(),
  {
    isMySelf: false,
    msg: () => ({}),
    index: 0,
    coreContent: () => ({}),
  },
);
const emit = defineEmits([
  'sendChat',
  'handleRateQuestion',
  'chooseFeedback',
  'submitFeedback',
  'resetQuestionList',
]);
const { userInfo } = useUserStore();

// 通知父组件添加新对话内容
const addChat = (item) => {
  emit('sendChat', item);
};
const moreMsg = ref({});
const morePopup = ref(false);
const getMore = () => {
  morePopup.value = true;
  moreMsg.value = props.msg;
};
const handleRateQuestion = (isResolved: any, msg: any, index: any) => {
  emit('handleRateQuestion', {
    isResolved,
    msg,
    index,
  });
};

/**
 * 选择反馈类型
 * @param index 点击的反馈类型在列表中的索引
 * @param val 点击的反馈类型的值
 */
const chooseFeedback = (data) => {
  emit('chooseFeedback', data);
};
/**
 * 提交反馈
 * @param msg 当前消息
 * @param index 点击的反馈类型在列表中的索引
 */
const submitFeedback = (data) => {
  emit('submitFeedback', data);
};

const resetQuestionList = (index: number) => {
  emit('resetQuestionList', index);
};
</script>

<style scoped lang="scss">
/* 右侧对话框 */
.chart-item-right {
  max-width: 65vw;
  color: #fff;
  background: #56be66;
  border-radius: 20rpx;
  border-top-right-radius: 4rpx;
}
/* 左侧对话框 */
.chart-item-left {
  max-width: 65vw;
  border-radius: 20rpx;
  border-top-left-radius: 4rpx;
}

.read-status {
  display: flex;
  align-items: flex-end;
  margin: 10rpx;
  margin-right: 8rpx;
  font-size: 24rpx;
}

.read-already {
  color: #999;
}

.read-not {
  color: #56be66;
}

.head-icon {
  width: 64rpx;
  height: 64rpx;
  overflow: hidden;
  border-radius: 64rpx;
}

.chart-item-con {
  position: relative;
  display: inline-block;
  padding: 27rpx 28rpx;
  font-size: 32rpx;
  color: #333;
  word-break: break-word;
  overflow-wrap: break-word;
  background: white;
  border-radius: 20rpx;
  border-top-left-radius: 4rpx;
}

.answer-box {
  display: inline-block;
  max-height: 330rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.answer-title {
  margin-bottom: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.more-answer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 518rpx;
  padding: 103rpx 0 25rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #56be66;
  text-align: center;
  background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, #fff 46%);
}

.sys-box {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;

  .sys-info {
    padding: 8rpx 28rpx;
    font-size: 28rpx;
    color: #666;
    background: #eee;
    border-radius: 8rpx;
  }
}

.more-popup {
  padding: 40rpx 25rpx;
  margin-bottom: 42rpx;
}

.more-title {
  margin-bottom: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.more-content {
  font-size: 32rpx;
  color: #333;
}

.question-rate {
  display: flex;
  margin-top: 20rpx;

  &-item {
    display: flex;
    flex: 1;
    justify-content: center;
    min-width: 200rpx;
    padding: 14rpx;
    font-size: 28rpx;
    color: #666;
    background-color: #fff;
    border-radius: 261rpx;

    &-blue {
      color: #56be66;
    }

    &-orange {
      color: #fe9327;
    }
  }
}
</style>
