<template>
  <wd-popup v-model="value" :mask-click="false">
    <view class="alert-dialog">
      <view class="alert-dialog-title">提示</view>
      <view class="alert-dialog-text">
        您的发送内容已涉嫌违反《中华人民共和国网络安全法》中相关规定，请修改后重新发送！
      </view>
      <view class="alert-dialog-btn" @click="closeDialog">确定</view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    value: boolean;
    index: number;
  }>(),
  {
    value: false,
    index: 0,
  },
);
const emit = defineEmits(['closeDialog']);

const closeDialog = () => {
  emit('closeDialog');
};
</script>

<style scoped lang="scss">
.alert-dialog {
  width: 540rpx;

  // height: 324rpx;
  background-color: #fff;
  border-radius: 16rpx;

  &-title {
    padding: 40rpx 30rpx 16rpx;
    font-size: 36rpx;
    font-weight: 500;
    line-height: 42rpx;
    color: #333;
    text-align: center;
  }

  &-text {
    padding: 0 30rpx 40rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #999;
    text-align: center;
  }

  &-btn {
    padding: 24rpx 0;
    font-size: 36rpx;
    font-weight: 500;
    line-height: 43rpx;
    color: #56be66;
    text-align: center;
    border-top: 1px solid #eee;
  }
}
</style>
