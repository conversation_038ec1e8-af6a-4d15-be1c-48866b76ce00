<route lang="json5">
{
  style: { navigationBarTitleText: '在线客服' },
}
</route>
<template>
  <view class="bg-#F6F7F9 h-screen flex flex-col justify-between">
    <view class="flex-1 mt-50" v-if="!chatList.length">
      <AEmpty className="mt-50" v-if="appLoading" type="loading" />
      <AEmpty v-else :type="error ? 'net' : 'content'" :button="true" @buttonClick="appRefresh" />
    </view>
    <view v-else @click="handleClick">
      <scroll-view
        :scroll-top="scrollTop"
        :scroll-y="true"
        :class="emojiShow || imgShow ? 'short-scroll-Y' : 'scroll-Y'"
        :scroll-into-view="reachedId"
        :style="customStyle"
      >
        <view class="more-chat-note">
          <text v-if="moreNoteVisible" @click="handleGetMoreNote">点击加载更多记录</text>
          <text v-else class="more-chat-note-none">暂无历史记录</text>
        </view>
        <ChatItems
          v-for="(item, index) in chatList"
          :key="index"
          :isMySelf="item.isMySelf"
          :msg="item.msg"
          :index="index"
          :coreContent="coreContent"
          @sendChat="addChat"
          @handleRateQuestion="handleRateQuestion"
          @chooseFeedback="chooseFeedback"
          @submitFeedback="submitFeedback"
          @resetQuestionList="resetQuestionList"
        />
        <!-- 评价弹窗start -->
        <view class="flex flex-center py-40rpx">
          <RatePopup
            v-if="showRate"
            @submit="evaluateSubmit"
            :inviteEvaluateText="inviteEvaluateText"
            :id="coreContent.recordId"
          ></RatePopup>
        </view>
        <view id="bottomOccupy" class="mb-50rpx"></view>
      </scroll-view>
    </view>

    <!-- 评价弹窗end -->
    <view class="flex flex-col justify-center px-32rpx pt-20rpx pb-40rpx bg-#F6F7F9">
      <!-- 底部按钮部分 -->
      <view class="flex">
        <AppButton
          :color="appStore.colors['white-color']"
          @click="handleShowQuestion"
          shape="circle"
          class-name="w-160rpx h-72rpx"
          :custom-style="getClass('knowledge')"
        >
          知识库
        </AppButton>
        <AppButton
          v-if="!isCustomerOn"
          :color="appStore.colors['white-color']"
          @click="handleOpenService"
          shape="circle"
          class-name="ml-24rpx w-192rpx h-72rpx"
          :custom-style="getClass('human')"
        >
          人工客服
        </AppButton>
      </view>

      <!-- 底部输入框部分 -->
      <view class="flex justify-between items-center mt-20rpx" v-if="!isBlack && showInput">
        <view
          class="flex items-center flex-between text-8 bg-white rounded-28px pl-30rpx pr-15rpx text-#666666 flex-1 h-90rpx"
        >
          <input
            v-model="talkContent"
            placeholder="请输入您想咨询的内容"
            :adjust-position="true"
            :enableNative="false"
            cursor-spacing="30"
          />
          <view @click.stop="showEmojiFn">
            <OSSImg width="60" height="60" src="/images/customService/emoji-button.png" />
          </view>
        </view>
        <view @click.stop="showImgFn" class="font-light">
          <uni-icons type="plus" size="40"></uni-icons>
        </view>
        <button
          v-if="talkContent.length > 0"
          class="w-120rpx bg-#56BE66 text-white rounded-4px text-28rpx"
          @click.stop="addChat(talkContent)"
        >
          发送
        </button>
      </view>

      <!-- 表情 -->
      <view v-if="emojiShow" class="p-4 bg-white flex flex-wrap h-300rpx overflow-scroll">
        <view
          v-for="item in emojis"
          :key="item"
          @click="addEmojiFn(item)"
          class="w-1/9 px-2 flex justify-around hover:bg-gray-100"
        >
          {{ item }}
        </view>
      </view>

      <!-- 图片发送 -->
      <view
        @click.stop="imgSelectFn"
        v-if="imgShow"
        class="p-4 bg-gray-100 flex flex-wrap h-300rpx overflow-scroll"
      >
        <view class="rounded-16px p-4 bg-white w-120rpx h-120rpx hover:bg-gray-200">
          <OSSImg width="120" height="120" src="/images/customer/icon-picture.png" />
        </view>
      </view>

      <SendAduit :value="sendAduitFlag" @closeDialog="closeDialog" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, watch, nextTick } from 'vue';
import { useAppStore, useChatStore, useUserStore } from '@/store';
import AppButton from '@/components/AppButton/index.vue';
import RatePopup from '@/pages/customService/components/RatePopup.vue';
import SendAduit from '@/pages/customService/components/SendAduit.vue';
import { CTerminalOnlineCustomerServiceRelatedInterfacesService } from '@/customService/services/CTerminalOnlineCustomerServiceRelatedInterfacesService';
import { silentAuth } from '@/store/user/service';
import WsRequest from '@/utils/wsRequest';
import { payChannelEnum } from '@/utils/pay/basePay';
import OSSImg from '@/components/OSSImg/index.vue';
import { checkLogin } from '@/hooks/useCheckLogin';
import { emojis } from './components/dict';
import ChatItems from './components/ChatItems.vue';

const scrollTop = ref<number>(0); // 滚动条位置
const appStore = useAppStore();

const activeName = ref<string>('');
const getClass = (name) => {
  if (activeName.value === name) {
    return 'color: #56BE66; font-size: 32rpx;border: 1rpx solid #56BE66;background: #EEF8EF;';
  } else {
    return 'color: #111111; font-size: 32rpx;';
  }
};

const moreNoteVisible = ref<boolean>(true);
const { userInfo, getUserInfo, isLogin, token } = useUserStore();
const authInfo = ref({});
const channel = ref<number>(1);
const isCustomerOn = ref<boolean>(false);
const sendAduitFlag = ref<boolean>(false);
const ws = ref<any>();
const wsUrl = `${import.meta.env.VITE_SERVICE_WEBSOCKET_URL}/online/chat/`;
const platform = ref('');
const baseUrl = import.meta.env.VITE_BASE_URL;
const picNum = ref<number>(0);
const customerStatus = ref<string>('');
const showRate = ref<boolean>(false);
const appLoading = ref<boolean>(true);
const isBlack = ref<boolean>(false);
const showInput = ref<boolean>(true);
const isEvaluateOn = ref<boolean>(false);
const inviteEvaluateText = ref<string>('');
const btnLoading = ref<boolean>(false);
const chatList = ref<ChatItem[]>([]);
const talkContent = ref<string>(''); // 输入框内容
// 表情框
const emojiShow = ref<boolean>(false);
const coreContent = ref({
  recordId: '',
  content: '',
  fromUser: '',
  head: '', // 头像
  nickname: '', // 昵称
});
// 历史记录传参
const chatHistoryParams = ref<any>({
  pageSize: 10,
  recordDetailId: '',
  userUuid: '',
  tenantId: '1',
});

onLoad(async () => {
  await checkLogin('/pages/customService/index');
  authInfo.value = await silentAuth();
  init();
  // 识别当前设备环境
  // #ifdef MP-WEIXIN
  channel.value = 1;
  // #endif
  // #ifdef MP-ALIPAY
  channel.value = 2;
  // #endif
  getEvaluateSet();
});

const wsParams = computed(() => {
  return `1@@c@@${channel.value}@@${userInfo.userId}`;
});

/**
 * 初始化页面需要执行的逻辑
 */
const init = async () => {
  // 获取会话列表数据
  chatHistoryParams.value = {
    pageSize: 10,
    recordDetailId: '',
    userUuid: userInfo.userId,
    tenantId: '1',
  };
  await handleGetMoreNote();
  // 会话列表末尾塞一条客服信息
  chatList.value.push({
    isMySelf: false,
    isQuestion: false,
    msg: {
      content: '您好，请问您有什么需要帮助的？可点击左下角知识库以及人工客服获取帮助！',
      contentType: 'text',
      messageType: 'content',
      sendTime: getCurrentDateTime(),
    },
  });
  reachBottom();
};

/**
 * 添加知识库卡片
 */
const handleShowQuestion = () => {
  activeName.value = 'knowledge';
  const newMessage = {
    isMySelf: false,
    msg: {
      contentType: 'question',
      messageType: 'content',
      sendTime: getCurrentDateTime(),
    },
  };
  chatList.value = [...chatList.value, newMessage];
};

/**
 * 获取当前时间
 */
const getCurrentDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 获取会话列表数据-消息历史记录
 */
const handleGetMoreNote = async () => {
  const [err, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineChatRecord({
      ...chatHistoryParams.value,
    });
  if (err) {
    appLoading.value = false;
    return;
  }
  if (res?.code === '10000' && res.success) {
    if ((res.data as Array<any>) && (res.data as Array<any>).length > 0) {
      // 将每条数据插入当前聊天记录之前
      (res.data as Array<any>).forEach((item: any) => {
        // 判断聊天记录所属者
        const isMySelf = item.sendType === 'user' || false;
        chatList.value.unshift({
          msg: { messageType: 'content', ...item },
          isMySelf,
        });
      });
      // 将最小标识赋值recordDetailId
      chatHistoryParams.value.recordDetailId = (res.data as Array<any>)[
        (res.data as Array<any>).length - 1
      ].recordDetailId;
      moreNoteVisible.value = true;
    } else {
      moreNoteVisible.value = false;
    }
  }
};

/**
 * 点击 人工客服
 */
const handleOpenService = async () => {
  isCustomerOn.value = true;
  activeName.value = 'human';
  showInput.value = true;
  if (isBlack.value) {
    chatList.value.push({ msg: { messageType: 'blacklist' } });
    reachBottom();
    return;
  }
  await loginSocketService();
  reachBottom();
};

const handleAvatarUrl = (url) => {
  // 使用正则表达式提取路径部分
  const regex = /https?:\/\/[^/]+(\/[^?]*)/;
  const match = url.match(regex);
  return match ? match[1] : url;
};
/**
 * 连接WebSocket服务器
 */
const loginSocketService = async () => {
  if (!userInfo.userId) {
    return false;
  }
  coreContent.value.recordId = '';
  coreContent.value.fromUser = '';
  chatHistoryParams.value.recordDetailId = '';
  chatList.value.length = 0;
  await handleGetMoreNote();
  const avatarUrl = handleAvatarUrl(userInfo.avatar);
  const query = `?nickname=${encodeURI(userInfo.nickname)}&head=${
    avatarUrl
  }&mobile=${userInfo.mobile}`;
  ws.value = new WsRequest(() => {
    getLatestChatList();
  });
  console.log('ws.value', ws.value);
  ws.value.init(wsUrl + wsParams.value + query);
  ws.value.onmessage = async (msg: any) => {
    if (msg.recordId) {
      coreContent.value.recordId = msg.recordId;
    }
    console.log('msg', msg);
    switch (msg.messageType) {
      case 'content':
        showInput.value = true;
        // 聊天消息
        chatList.value.push({
          msg,
          isMySelf: false,
          isQuestion: false,
          sendTime: getCurrentDateTime(),
        });
        // 用户已读
        ws.value.send(
          JSON.stringify({
            recordId: coreContent.value.recordId,
            toUser: coreContent.value.fromUser,
            messageType: 'read_status',
            fromUser: wsParams.value,
          }),
        );
        reachBottom();
        break;
      case 'info':
        // 发送用户、客服信息
        showInput.value = true;
        coreContent.value.fromUser = msg.fromUser;
        coreContent.value.head = msg.head;
        coreContent.value.nickname = msg.nickname;
        chatList.value.push({ msg, isMySelf: false });
        reachBottom();
        break;
      case 'customer_close':
        // 客服关闭了会话窗口
        chatList.value.push({ msg, isMySelf: false });
        if (isEvaluateOn.value) {
          showRate.value = true;
        }
        closeSocket();
        reachBottom();
        uni.showToast({
          title: '客服结束了聊天会话',
          icon: 'none',
        });

        break;
      case 'user_close':
        // 用户关闭了会话窗口
        break;
      case 'disconnect':
        chatList.value.push({ msg, isMySelf: false });
        // 客服断开连接
        uni.showToast({
          title: '断开连接',
          icon: 'none',
        });
        closeSocket();
        coreContent.value.recordId = '';
        coreContent.value.fromUser = '';
        break;
      case 'customer_busy':
        chatList.value.push({ msg, isMySelf: false });
        customerStatus.value = 'busy';
        showInput.value = false;
        reachBottom();
        handleShowQuestion();
        break;
      case 'user_list':
        break;
      case 'customer_status':
        showInput.value = true;
        chatList.value.push({ msg, isMySelf: false });
        customerStatus.value = 'online';
        reachBottom();
        break;
      case 'user_online':
        // 用户上线
        break;
      case 'show_question':
        // 展示问题库
        handleShowQuestion();
        break;
      case 'read_status':
        // 已读
        chatList.value.forEach((item: any) => {
          item.msg.isRead = true;
        });
        break;
      // 被拉黑
      case 'blacklist':
        isBlack.value = true;
        chatList.value.push({ msg, isMySelf: false });
        reachBottom();
        customerStatus.value = '';
        coreContent.value.recordId = '';
        coreContent.value.fromUser = '';
        closeSocket();
        break;
      case 'withdraw_message': {
        // 撤回消息
        chatList.value.forEach((item) => {
          if (item.msg.recordDetailId === msg.recordDetailId) {
            item.msg.messageType = msg.messageType;
          }
        });
        break;
      }
      default:
    }
  };
  // 已读
  setTimeout(() => {
    ws.value.send(
      JSON.stringify({
        recordId: coreContent.value.recordId,
        toUser: coreContent.value.fromUser,
        messageType: 'read_status',
        fromUser: wsParams.value,
      }),
    );
  }, 1000);
};

/**
 * 获取评价相关数据。包括：是否已评价、评价文案
 */
const getEvaluateSet = async () => {
  const [, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineGetEvaluateSet(
      {},
    );
  if (res?.code === '10000') {
    const { status, content } = res.data;
    isEvaluateOn.value = status;
    inviteEvaluateText.value = content;
  }
};

/**
 * 关闭WebSocket连接
 */
const closeSocket = async () => {
  if (ws.value) {
    ws.value.close();
    isCustomerOn.value = false;
  }
};

/**
 * 用户重连后获取最新消息
 */
const getLatestChatList = async () => {
  chatHistoryParams.value.recordDetailId = '';
  chatList.value = [];
  await handleGetMoreNote();
  setTimeout(() => {
    ws.value.send(
      JSON.stringify({
        recordId: coreContent.value.recordId,
        toUser: coreContent.value.fromUser,
        messageType: 'read_status',
        fromUser: wsParams.value,
      }),
    );
  }, 4000);
};
/**
 * 点击发送
 * @param msg 输入框中的内容
 */
const addChat = async (msg: any) => {
  if (btnLoading.value) {
    return;
  }
  // 输入文字，msg是字符串，且开启了人工客服，则直接ws通信
  if (typeof msg === 'string') {
    if (isCustomerOn.value) {
      // 调接口 判断敏感词
      btnLoading.value = true;
      const [err, res] =
        await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineTextAudit({
          content: msg,
        });
      btnLoading.value = false;
      if (err?.code === '61000') {
        // 存在敏感词，提示弹窗
        sendAduitFlag.value = true;
      } else if (res?.code === '10000') {
        // 校验通过✅
        sendMessage('text', msg);
        talkContent.value = '';
      } else {
        // 其他校验失败情况
        uni.showToast({
          title: err?.subMsg || err?.msg || '文本审核失败',
          icon: 'none',
        });
      }
    } else {
      // 未开启人工客服，用户输入咨询信息时，请求问题列表接口模糊搜索

      // 发送消息:自动塞入一条当前用户发送的消息数据
      sendMessage('text', msg, '', false, '', true);
      // 自动塞入一条知识库回答的消息数据
      const msgCustomerService = {
        contentType: 'question',
        messageType: 'content',
        sendTime: getCurrentDateTime(),
        self_question: msg,
      };
      chatList.value.push({ msg: msgCustomerService, isMySelf: false });
      reachBottom();
      // 清空输入框
      talkContent.value = '';
    }
  } else {
    // 点击知识库卡片中的问题（msg应该是个对象，取msg.id，即：问题id）
    const params = {
      questionId: msg.id,
      channel: channel.value,
      tenantId: '1',
    };
    const [, res] =
      await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineQuestionEvaluate(
        params,
      );
    if (res?.code === '10000') {
      const mainId = res.data;
      // 知识库自动回复
      const message = {
        messageType: 'content',
        contentType: 'text',
        content: msg.question,
        id: msg.id,
        isQuestion: true,
      };
      const answer = {
        messageType: 'content',
        contentType: 'answer',
        content: msg.answer,
        question: msg.question,
        id: msg.id,
        isResolved: '',
        hasSubmit: false,
        feedbackType: '',
        mainId,
      };

      chatList.value.push(
        { msg: message, isMySelf: true, sendTime: getCurrentDateTime() },
        { msg: answer, isMySelf: false, sendTime: getCurrentDateTime() },
      );
      reachBottom();
    }
  }
};

/**
 * 自动回复 点击问题是否解决
 * @param isResolved 是否已解决
 * @param msg 当前消息数据
 * @param index 当前消息所在 会话列表中的索引
 */
const handleRateQuestion = async ({
  isResolved,
  msg,
  index,
}: {
  isResolved: any;
  msg: any;
  index: any;
}) => {
  // 如果已经提交，则按钮不能再点击
  if (!chatList.value[index].msg.hasSubmit) {
    chatList.value[index].msg.isResolved = isResolved;

    const params = {
      questionId: msg.id,
      isResolve: isResolved ? 'Y' : 'N',
      channel: channel.value,
      id: msg.mainId,
      tenantId: '1',
    };
    const [, res] =
      await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineQuestionEvaluate(
        params,
      );
    if (isResolved) {
      if (res?.code === '10000') {
        chatList.value[index].msg.hasSubmit = true;
        uni.showToast({
          title: '提交成功！',
          icon: 'none',
        });
      }
    }
  }
};

/**
 * 选择反馈类型
 * @param index 点击的反馈类型在列表中的索引
 * @param val 点击的反馈类型的值
 */
const chooseFeedback = ({ index, val }: { index: number; val: string }) => {
  chatList.value[index].msg.feedbackType = val;
};
/**
 * 点击提交未解决的反馈类型
 * @param msg 当前消息
 * @param index 点击的反馈类型在列表中的索引
 */
const submitFeedback = async ({ msg, index }: { msg: any; index: any }) => {
  if (!msg.feedbackType) {
    uni.showToast({
      title: '请选择反馈类型',
      icon: 'none',
    });
    return;
  }
  const params = {
    questionId: msg.id,
    isResolve: 'N',
    feedback: msg.feedbackType,
    channel: channel.value,
    id: msg.mainId,
    tenantId: '1',
  };
  const [, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineQuestionEvaluate(
      params,
    );
  if (res?.code === '10000') {
    chatList.value[index].msg.hasSubmit = true;
    uni.showToast({
      title: '已反馈',
      icon: 'none',
    });
  }
};

/**
 * 关闭弹框
 */
const closeDialog = () => {
  sendAduitFlag.value = false;
};

/**
 * 上传
 */
const imgSelectFn = () => {
  uni.chooseImage({
    count: 1, // 最多可选择的图片数量
    success: (chooseRes) => {
      picNum.value++;
      const { tempFilePaths } = chooseRes;
      const file = {
        status: 'uploading',
        message: '上传中...',
        file: {
          path: tempFilePaths[0],
        },
      };
      // 展示审核中的图片消息，不调用ws
      sendMessage(
        'image',
        `${import.meta.env.VITE_ALI_OSS_URL_PREFIX}/static/images/customer/audit-ing.png`,
        picNum.value,
        false,
        true,
      );
      uploadImage(file, picNum.value);
    },
    fail: (error) => {
      console.log('选择图片失败', error);
    },
  });
};

/**
 * 文件上传
 * @param file
 * @param picId
 */
const uploadImage = (file: any, picId: any) => {
  const uploadTask = uni.uploadFile({
    url: `${baseUrl}/customer-service/customer/online/imageAudit`,
    filePath: file.file.path,
    name: 'file',
    fileType: 'image',
    fileName: 'file',
    header: {
      Authorization: token || '',
    },
    success: async (res) => {
      const data = JSON.parse(res.data);
      if (data.code === '10000') {
        // 图片审核通过
        chatList.value.forEach((item) => {
          if (item.picId === picId) {
            item.msg.content = data.data;
            item.msg.defaultImg = false;
          }
        });
        const message = chatList.value.find((x) => x.picId === picId)?.msg;
        // 发送ws消息
        if (
          (platform.value === payChannelEnum.ALIPAY_MP && !ws.value.isConnected) ||
          (platform.value !== payChannelEnum.ALIPAY_MP &&
            ws.value.socket &&
            ws.value.socket.readyState !== 1) ||
          coreContent.value.recordId === ''
        ) {
          await loginSocketService();
        }
        ws.value.send(JSON.stringify(message));
      } else {
        // 审核不通过
        chatList.value.forEach((item) => {
          if (item.picId === picId) {
            item.msg.content = `${import.meta.env.VITE_ALI_OSS_URL_PREFIX}/static/images/customer/audit-fail.png`;
          }
        });
      }
    },
    fail: (res) => {
      console.log('上传失败', res);
    },
  });

  uploadTask.onProgressUpdate((res) => {
    file.percentage = res.progress;
  });
};

/**
 * 发送消息
 * @param contentType
 * @param content
 * @param picId
 * @param sendWs
 * @param defaultImg
 */
const sendMessage = async (
  contentType: any,
  content: any,
  picId?: any,
  sendWs: any = true,
  defaultImg: any = false,
  isRead: any = false,
) => {
  if (!content) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none',
    });
    return;
  }

  const message = {
    recordId: coreContent.value.recordId,
    toUser: coreContent.value.fromUser,
    messageType: 'content',
    fromUser: wsParams.value,
    contentType,
    content,
    isRead,
    sendTime: getCurrentDateTime(),
    defaultImg,
  };
  chatList.value.push({
    msg: message,
    isMySelf: true,
    picId: picId || '',
  });
  reachBottom();
  if (sendWs) {
    if (
      (platform.value === payChannelEnum.ALIPAY_MP && !ws.value.isConnected) ||
      (platform.value !== payChannelEnum.ALIPAY_MP &&
        ws.value.socket &&
        ws.value.socket.readyState !== 1) ||
      coreContent.value.recordId === ''
    ) {
      await loginSocketService();
    }
    ws.value.send(JSON.stringify(message));
  }
};

/**
 * 提交评价
 * @param level 评价等级
 * @param id 当前客服recordId
 */
const evaluateSubmit = async ({ level, id }: { level: any; id: any }) => {
  const params = {
    level,
    recordId: id,
    openId: userInfo.userId,
  };
  const [, res] =
    await CTerminalOnlineCustomerServiceRelatedInterfacesService.postCustomerOnlineEvaluate(params);
  if (res?.code === '10000') {
    uni.showToast({
      title: '感谢评价！',
      icon: 'none',
    });
  }
  showRate.value = false;
  customerStatus.value = '';
  coreContent.value.recordId = '';
  coreContent.value.fromUser = '';
};

/**
 * 聊天室 点击消息区域 关闭表情和加号内容
 */
const handleClick = () => {
  emojiShow.value = false;
  imgShow.value = false;
};

/**
 * 无法搜索到知识库返回信息
 */
const resetQuestionList = (index: any) => {
  chatList.value[index].msg = {
    content:
      '对不起，您的问题我暂时无法解答，您可以尝试用简短的方式描述下您的诉求（如“停车”，“充电”），或者点击下方联系人工客服帮您解答哦～',
    contentType: 'text',
    messageType: 'content',
    sendTime: getCurrentDateTime(),
  };
};
const appRefresh = async () => {
  appLoading.value = true;
  await init();
  getEvaluateSet();
  appLoading.value = false;
};

// 聊天框部分
interface Message {
  contentType?: string;
  content?: string; // 可选属性，根据不同的 contentType 存在与否
  isRead?: number;
  sendTime?: string;
}

interface ChatItem {
  isMySelf?: boolean;
  msg?: Message;
}
const showEmojiFn = () => {
  emojiShow.value = true;
  imgShow.value = false; // 保证两个框显示互斥
};
const addEmojiFn = (value: string) => {
  talkContent.value += value;
};

// 图片发送框
const imgShow = ref<boolean>(false);
const showImgFn = () => {
  imgShow.value = true;
  emojiShow.value = false; // 保证两个框显示互斥
};

const reachedId = ref('');

const reachBottom = () => {
  // 先重置
  reachedId.value = '';
  // 再设置新值
  setTimeout(() => {
    reachedId.value = 'bottomOccupy';
  }, 100);
};

watch(
  chatList,
  (newval, oldval) => {
    if (newval !== oldval) {
      reachBottom();
    }
  },
  { deep: true },
);
const customStyle = ref('');
const handleFocus = (event) => {
  imgSelectFn.value = false;
  emojiShow.value = false;
  const height = event?.detail.height || 0;
  const top = `${height + 20}px`;
  // 弹起键盘
  setTimeout(() => {
    customStyle.value = `height: calc(100vh - 270rpx - ${top});`;
    console.log('customStyle', customStyle.value);
    reachBottom();
  }, 0);
};
</script>

<style scoped lang="scss">
/* scroll容器高度 */
.scroll-Y {
  height: calc(100vh - 250rpx);
  overflow-y: auto;
}

.short-scroll-Y {
  height: calc(100vh - 570rpx);
  overflow-y: auto;
}
/* 底部表情图标高度 */
.icon-height {
  height: 31px;
}
/* 底部两个按钮 */
.bottom-button {
  @apply w-48 text-7 bg-white rounded-28px py-2 text-#666666 hover:bg-gray-100 inline-block;
}

.more-chat-note {
  padding: 16rpx 0;
  font-size: 24rpx;
  color: #56be66;
  text-align: center;
  background: 0 0;

  text {
    cursor: pointer;
  }

  &-none {
    color: #ccc;
  }
}
</style>
