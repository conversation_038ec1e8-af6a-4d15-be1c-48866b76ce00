<template>
  <wd-overlay :show="props.show" @click="preventCloseOnOverlayClick">
    <view class="flex-center h-100vh mx-75rpx" @click.stop>
      <view class="bg-white rounded-8 py-10 px-13 flex flex-col">
        <view class="text-primary-36 text-text-primary text-center mb-6">预约失败</view>
        <scroll-view scroll-y class="max-h-90 of-scroll">
          <view class="text-secondary-26 text-text-secondary">
            {{ errMsg }}
          </view>
        </scroll-view>

        <view class="flex-center mt-4">
          <view
            class="text-center p-6 bg-#56be66 text-white rounded-4 text-primary-30 w-124"
            @click="handleConfirm"
          >
            确定
          </view>
        </view>
      </view>
    </view>
  </wd-overlay>
</template>

<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    show: boolean;
    errMsg: string;
  }>(),
  {
    show: false,
    errMsg: '',
  },
);

const dontShowAgain = ref(false);

const emits = defineEmits(['close', 'confirm']);

// 阻止点击遮罩层关闭弹窗
const preventCloseOnOverlayClick = (e: Event) => {
  // 这里只处理点击遮罩层的情况，内容区域的点击已通过@click.stop阻止冒泡
  handleClose();
};

const handleClose = () => {
  emits('close', dontShowAgain.value);
};
const handleConfirm = () => {
  emits('confirm', dontShowAgain.value);
};
</script>
