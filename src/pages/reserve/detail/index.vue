<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '预约停车详情页',
  },
}
</route>
<template>
  <view class="h-100vh pb-safe-40 flex-center box-border" v-if="isQuerying || isError">
    <AEmpty type="loading" v-if="isQuerying" />
    <AEmpty type="net" :button="true" buttonText="刷新" @buttonClick="refreshPage" v-else />
  </view>
  <view class="pb-safe-60" v-else @touchmove="clearTooltip">
    <HeaderSwiper :imgs="parkDetail.imageUrl || []" />
    <view class="bg-page-background mt--8 rounded-t-8 of-hidden relative z-2">
      <ParkItem
        type="detail"
        :name="parkDetail.parkName || ''"
        :address="parkDetail.address || ''"
        :distance="parkDetail.distance || 0"
        :latitude="parkDetail.latitude || 0"
        :longitude="parkDetail.longitude || 0"
        :parkId="parkId"
        :reserveConfig="parkDetail.reserveConfig"
        :remainNum="parkDetail.remainNum"
        :plateNum="parkDetail.plateNum"
        :spaceStatus="parkDetail.spaceStatus"
      ></ParkItem>
      <!-- 可预约 -->
      <template v-if="true">
        <!-- 预约入场时间选择 -->
        <view class="bg-white p-6">
          <view class="text-primary-32 text-text-primary mb-3">预约入场时间</view>
          <view class="text-secondary-24 text-text-sub">
            请在预约入场时间前进入停车场，否则可能造成爽约
          </view>

          <!-- 时间选择按钮组 -->
          <view class="flex flex-wrap">
            <view
              v-for="(item, index) in entryTimeOptions"
              :key="index"
              class="py-2 px-4 rounded-[8px] text-secondary-26 mt-6 mr-6"
              :class="
                selectedEntryTime === item.value
                  ? 'bg-#56be66 text-white'
                  : 'bg-page-background text-text-secondary'
              "
              @click="selectedEntryTime = item.value"
            >
              {{ item.label }}
            </view>
          </view>
        </view>

        <!-- 预约车牌号码 -->
        <view class="bg-white mt-3 p-6">
          <view class="text-primary-32 text-text-primary mb-6">预约车牌号码</view>

          <view class="flex flex-wrap mb-6">
            <view
              v-for="(plate, index) in plateOptions"
              :key="index"
              class="py-2 px-4 rounded-[8px] text-secondary-26 mt-6 mr-6"
              :class="
                selectedPlate === plate.value
                  ? 'bg-#56be66 text-white'
                  : 'bg-page-background text-text-secondary'
              "
              @click="selectedPlate = plate.value"
            >
              {{ plate.label }}
            </view>
          </view>

          <span
            class="py-2 px-4 rounded-[8px] items-center bg-page-background text-#56be66 text-secondary-26"
            @click="goAddPlate"
          >
            + 新车辆
          </span>
        </view>

        <!-- 联系方式 -->
        <view class="bg-white mt-3 p-6">
          <view class="text-primary-32 text-text-primary mb-6">联系方式</view>
          <view class="flex items-center mb-4">
            <text class="text-secondary-24 text-text-secondary w-160rpx">手机号</text>
            <text class="text-primary-24 text-text-sub">{{ contactPhone }}</text>
          </view>
        </view>

        <!-- 底部提交按钮 -->
        <view class="fixed bottom-0 left-0 w-full pb-safe-6 pt-6 bg-white box-border px-6">
          <view class="flex items-center justify-center mb-6">
            <radio
              :checked="isAgreed"
              :color="'#56be66'"
              @click="isAgreed = !isAgreed"
              class="transform scale-80"
            />
            <view class="text-secondary-26 text-text-secondary flex items-center ml-2">
              <text>我已阅读并同意</text>
              <text class="text-#56be66" @click="openPolicy">《汕头市公共停车场预约须知》</text>
            </view>
          </view>
          <AppButton type="primary" className="w-full h-25" @click="submitReservation">
            提交预约
          </AppButton>
        </view>
      </template>

      <view v-else class="bg-white pb-10">
        <AEmpty type="content" title="抱歉，该停车场当日未开放预约" desc="请选择其他停车场预约" />
      </view>
    </view>
  </view>

  <!-- 预约须知弹窗 -->
  <PolicyModal :show="showPolicyModal" @close="handlePolicyClose" @confirm="handlePolicyConfirm">
    <PolicyContent />
  </PolicyModal>

  <ErrModal />

  <!-- 预约失败弹窗 -->
  <view v-if="showFailModal" class="fixed inset-0 z-999 flex items-center justify-center">
    <!-- 遮罩层 -->
    <view class="absolute inset-0 bg-black bg-opacity-50" @click="closeFailModal"></view>

    <!-- 弹窗内容 -->
    <view class="relative bg-white rounded-12 mx-8 p-6 w-80% max-w-320">
      <view class="text-center">
        <view class="text-32 font-500 text-#111 mb-4">预约失败</view>
        <view class="text-28 text-#666 mb-8">{{ failReason || '失败原因' }}</view>
        <AppButton
          type="primary"
          className="w-full h-12"
          @click="closeFailModal"
          style="background-color: #56be66; border-color: #56be66"
        >
          确定
        </AppButton>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import AppButton from '@/components/AppButton/index.vue';
import { DEFAULTGEO } from '@/config';
import {
  BaseParkDetailVO,
  ParkingLotInquiryService,
  MyVehicleService,
  ParkingReserveService,
} from '@/parkService';

import { ForecastQueryService } from '@/service';
import { useUserStore } from '@/store';
import { isEmpty } from '@/utils/is';
import { getLocation, showSingleToast } from '@/utils/jsapi';
import HeaderSwiper from './components/HeaderSwiper.vue';
import ParkItem from '../list/components/ParkItem.vue';
import PolicyModal from './components/PolicyModal.vue';
import ErrModal from './components/ErrModal.vue';
import PolicyContent from './components/PolicyContent.vue';

const { isLogin, userInfo } = storeToRefs(useUserStore());
const parkId = ref('');
const parkDetail = ref<BaseParkDetailVO>({});
const userLocation = ref({
  latitude: DEFAULTGEO.latitude,
  longitude: DEFAULTGEO.longitude,
});
const isQuerying = ref(true);
const isError = ref(false);
const shortPrediction = ref([]);
const longPrediction = ref([]);

// 预约时间选项
const entryTimeOptions = ref([
  { label: '30分钟内', value: 30 },
  { label: '1小时内', value: 60 },
  { label: '1小时30分钟内', value: 90 },
  { label: '2小时内', value: 120 },
]);
const selectedEntryTime = ref(30);

// 车牌选项
const plateOptions = ref([]);
const selectedPlate = ref('');

// 联系方式
const contactPhone = ref('');

// 是否同意协议
const isAgreed = ref(false);

// 预约须知弹窗
const showPolicyModal = ref(false);

// 预约失败弹窗
const showFailModal = ref(false);
const failReason = ref('');

onLoad(async (options: any) => {
  parkId.value = options.parkId;
  getPrediction();
  await getUserLocation();
  await getStationDetail();
  await getMyPlateList();

  // 自动补全手机号
  if (isLogin.value && userInfo.value.fullMobile) {
    contactPhone.value = userInfo.value.fullMobile;
  }

  // 检查是否需要自动显示预约须知弹窗
  checkAndShowPolicyModal();
});

// 页面显示时重新获取车牌列表
onShow(() => {
  if (isLogin.value) {
    getMyPlateList();
  }
});

// 检查并自动显示预约须知弹窗
const checkAndShowPolicyModal = () => {
  const dontShowPolicyAgain = uni.getStorageSync('dontShowPolicyAgain');
  if (!dontShowPolicyAgain) {
    showPolicyModal.value = true;
  }
};

watch(
  () => isLogin.value,
  (newVal) => {
    if (newVal) {
      getStationDetail();
      getMyPlateList();
    }
  },
);

const getUserLocation = async () => {
  const { longitude, latitude } = await getLocation();
  userLocation.value = {
    longitude,
    latitude,
  };
};

const getStationDetail = async () => {
  const [err, res] = await ParkingLotInquiryService.postParkDetail({
    parkId: parkId.value,
    longitude: userLocation.value.longitude,
    latitude: userLocation.value.latitude,
  });
  isQuerying.value = false;
  if (err || !res?.data) {
    isError.value = true;
    return;
  }

  parkDetail.value = {
    ...res.data,
    imageUrl: isEmpty(res.data.imageUrl)
      ? [`${import.meta.env.VITE_ALI_OSS_URL_PREFIX}/images/charging/park-detail.png`]
      : res.data.imageUrl,
  };
};

const getPrediction = async () => {
  const [, res] = await ForecastQueryService.getPredictionPark(parkId.value);
  if (res?.data) {
    shortPrediction.value = res.data.shortPrediction || [];
    longPrediction.value = res.data.longPrediction || [];
  }
};

const getMyPlateList = async () => {
  if (!isLogin.value) return;

  const [, res] = await MyVehicleService.getMycarList();
  if (res?.data) {
    plateOptions.value = res.data.map((item) => ({
      label: `${item.plateNo} (${item.plateColor === 'BLUE' ? '蓝' : item.plateColor === 'GREEN' ? '绿' : '黄'})`,
      value: item.plateNo,
      color: item.plateColor,
    }));

    // 设置默认选中的车牌
    if (plateOptions.value.length > 0) {
      const defaultPlate = plateOptions.value.find((item) => item.default) || plateOptions.value[0];
      selectedPlate.value = defaultPlate.value;
    }
  }
};

const refreshPage = () => {
  isQuerying.value = true;
  isError.value = false;
  getStationDetail();
};

const goAddPlate = () => {
  uni.navigateTo({
    url: '/pages/mine-sub/car/add/index',
  });
};

const openPolicy = () => {
  // 打开预约须知弹窗
  showPolicyModal.value = true;
};

const handlePolicyClose = () => {
  showPolicyModal.value = false;
  // 点击遮罩关闭时不保存设置
};

const handlePolicyConfirm = (dontShowAgain: boolean) => {
  showPolicyModal.value = false;
  isAgreed.value = true;
  if (dontShowAgain) {
    // 只有点击确定按钮且勾选了"下次不再提醒"才保存设置
    uni.setStorageSync('dontShowPolicyAgain', true);
  }
};

// 关闭预约失败弹窗
const closeFailModal = () => {
  showFailModal.value = false;
  failReason.value = '';
};

const submitReservation = async () => {
  if (!selectedPlate.value) {
    showSingleToast('请选择车牌');
    return;
  }

  if (!isAgreed.value) {
    showSingleToast('请阅读并同意《汕头市公共停车场预约须知》');
    return;
  }

  if (!isLogin.value) {
    showSingleToast('请先登录');
    uni.navigateTo({
      url: '/pages/login/index',
    });
    return;
  }

  // 显示加载中
  uni.showLoading({
    title: '提交中...',
  });
  const [err, res] = await ParkingReserveService.postReservationApply({
    parkingId: parkId.value,
    plateNo: selectedPlate.value,
    plateColor:
      plateOptions.value.find((item) => item.value === selectedPlate.value)?.color || 'UNKNOWN',
    arrivalWithinTime: selectedEntryTime.value,
  });

  uni.hideLoading();

  if (err) {
    failReason.value = err.subMsg || '提交失败，请重试';
    showFailModal.value = true;
    return;
  }

  // 跳转到预约结果页面
  uni.navigateTo({
    url: `/pages/reserve/result/index?parkId=${parkId.value}`,
  });
};

const clearTooltip = () => {
  uni.$emit('clearTooltip');
};
</script>
