<route lang="json5" type="page">
{
  layout: 'page',
}
</route>
<template>
  <view>
    <web-view :src="showUrl" id="webview-id" @message="handleMessage"></web-view>
  </view>
</template>

<script lang="ts" setup>
import { checkLogin } from '@/hooks/useCheckLogin';
import { useQuery } from '@/hooks/useQuery';
import { useShare } from '@/hooks/useShare';
import { useUserStore } from '@/store';

const { onShareAppMessage, onShareTimeline } = useShare();
const { token } = useUserStore();
const { query } = useQuery<{
  url: string;
  title?: string;
  needAuth?: 'Y' | 'N';
}>({ url: '', title: '', needAuth: 'N' });
const baseUrl = ref('');
const showUrl = ref('');
const webViewContext = ref();
onLoad(async () => {
  // #ifdef MP-ALIPAY
  webViewContext.value = my.createWebViewContext('webview-id');
  // #endif
  uni.setNavigationBarTitle({
    title: query.value.title || '',
  });
  baseUrl.value = decodeURIComponent(query.value.url);
  if (!baseUrl.value.includes('://')) {
    baseUrl.value = decodeURIComponent(baseUrl.value);
  }
  console.error('baseUrl===', baseUrl.value);
  if (query.value.needAuth === 'Y') {
    await checkLogin(
      `/pages/common/webview/index?url=${query.value.url}&title=${query.value.title || ''}&needAuth=${query.value.needAuth}`,
    );

    const hashIndex = baseUrl.value.indexOf('#');
    if (hashIndex > -1) {
      const urlBeforeHash = baseUrl.value.substring(0, hashIndex);
      const hashPart = baseUrl.value.substring(hashIndex);

      // 检查hash部分是否包含查询参数
      if (hashPart.includes('?')) {
        // 情况1: 参数在hash后
        showUrl.value = urlBeforeHash + hashPart.replace('?', '?' + `authToken=${token}&`);
      } else {
        // 情况2: 参数在hash前
        showUrl.value = `${
          urlBeforeHash + (urlBeforeHash.indexOf('?') > -1 ? '&' : '?')
        }authToken=${token}${hashPart}`;
      }
    } else {
      showUrl.value = `${baseUrl.value + (baseUrl.value.indexOf('?') > -1 ? '&' : '?')}authToken=${token}`;
    }
  } else {
    showUrl.value = baseUrl.value;
  }
  // uni.showShareMenu({
  //   path: `/pages/common/webview/index?url=${query.value.url}&title=${query.value.title || ''}`,
  // });
});
const handleMessage = (e: any) => {
  console.error('handleMessage------', e);
  // #ifdef MP-ALIPAY
  if (e.detail.type === 'scan') {
    my.scan({
      success(res) {
        console.log(`条码类型：${res.scanType}`);
        console.log(`条码内容：${res.result}`);
        webViewContext.value.postMessage({ data: res.result });
      },
    });
  }
  // #endif
};
</script>

<style lang="scss" scoped>
//
</style>
