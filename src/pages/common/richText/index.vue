<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="px-6 pt-6 pb-10 box-border">
    <rich-text :nodes="contentVal"></rich-text>
  </view>
</template>

<script lang="ts" setup>
import { useSetTitle } from '@/hooks/useSetTitle';
import { useRichTextStore } from '@/store';
import { storeToRefs } from 'pinia';

const store = useRichTextStore();
const { content } = storeToRefs(store);
const contentVal = ref<string>('');

onLoad((options) => {
  const key = options!.key as string;
  console.log('content', content.value[key]);
  contentVal.value = content.value[key]?.content;
  useSetTitle(content.value[key]?.name);
});
</script>

<style lang="scss" scoped>
.h5-iframe {
  pointer-events: none;
  border: none;
}
</style>
