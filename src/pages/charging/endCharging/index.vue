<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '订单详情',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'always',
    },
    'app-plus': {
      titleNView: {
        type: 'transparent',
        titleText: '订单详情',
      },
    },
  },
}
</route>
<template>
  <AEmpty className="mt-100" v-if="isLoading" type="loading" />
  <AEmpty
    className="mt-50"
    v-else-if="isError"
    type="net"
    :button="true"
    @buttonClick="refreshPage"
  />
  <view v-else :class="['end-charging-page', Number(orderInfo.owedAmount) > 0 ? 'pb-safe-37' : '']">
    <TransparentTitle title="订单详情" />
    <view class="head-top-bg header-bg"></view>
    <!-- 顶部 -->
    <view class="flex pt-10 h-263rpx pb-46rpx box-border px-6 box-border relative z-3">
      <template v-if="orderInfo.orderStatus && endStatusCfg[orderInfo.orderStatus]">
        <OssImg :src="endStatusCfg[orderInfo.orderStatus]?.icon" :width="56" :height="56" />
        <view class="ml-4">
          <view class="text-leading text-text-primary">
            {{ endStatusCfg[orderInfo.orderStatus]?.title }}
          </view>
          <view class="text-secondary-26 mt-4 text-text-secondary">
            {{ endStatusCfg[orderInfo.orderStatus]?.subtitle }}
          </view>
        </view>
      </template>
    </view>
    <!-- 主体 -->
    <view class="w-full bg-page-background rounded-t-8 p-6 pb-58rpx box-border mt--22 relative z-3">
      <!-- 站点信息 -->
      <StationInfo :baseInfo="orderInfo">
        <view class="my-6 slider-1px"></view>
        <view class="text-secondary-26 flex-between text-text-secondary">
          <view>电量：{{ orderInfo.chargeCapacity }}度</view>
          <view>时长：{{ orderInfo.chargeTime || '--' }}</view>
        </view>
      </StationInfo>
      <!-- <view class="common-card mt-6"></view> -->
      <InfoCard
        v-if="orderInfo.orderStatus && orderInfo.orderStatus !== 'SETTLING'"
        title="费用信息"
        :infoList="priceInfoList"
        :data="orderInfo"
      >
        <template #realPrice>
          <view class="flex text-brand-primary">
            <template v-if="orderInfo.realPrice">
              <view>¥</view>
              <view class="ml-1 text-10 leading-10 font-DIN font-bold">
                {{ orderInfo.realPrice }}
              </view>
            </template>
            <view v-else>--</view>
          </view>
        </template>
      </InfoCard>
      <InfoCard
        v-if="orderInfo.orderStatus && orderInfo.orderStatus !== 'SETTLING'"
        title="订单信息"
        :infoList="orderInfoList"
        :data="orderInfo"
      >
        <template #orderId>
          <view class="flex flex-col items-end">
            <view class="text-text-primary">{{ orderInfo.orderId || '--' }}</view>
            <view
              class="text-brand-primary font-500 w-max"
              @click="setClipboardData(orderInfo.orderId)"
              v-if="orderInfo.orderId"
            >
              复制
            </view>
          </view>
        </template>
        <template #footer v-if="orderInfo.refundDetails?.length">
          <view class="flex justify-end mt-5" @click="changeShowRefundInfoPop(true)">
            <view class="text-brand-primary text-primary-28">订单退款记录</view>
            <OssImg
              :width="20"
              :height="20"
              className="mt-11rpx"
              src="/images/charging/arrow-right-red.png"
            />
          </view>
        </template>
      </InfoCard>
    </view>
    <view
      class="fixed w-full left-0 bottom-0 bg-white p-6 pb-safe-6 box-border flex-between items-center bordered-t-1-solid-divider-color z-5"
      v-if="showFooterButton"
    >
      <view>
        <text class="text-primary-28 font-400">欠费金额：</text>
        <text class="text-primary-32">¥{{ orderInfo.owedAmount }}</text>
      </view>
      <AppButton className="w-339rpx h-25" type="brand" @click="goPay">立即支付</AppButton>
    </view>
    <RefundInfoPop
      :refundInfo="orderInfo.refundDetails[0] || {}"
      v-if="showRefundInfoPop && orderInfo.refundDetails?.length"
      @close="changeShowRefundInfoPop(false)"
    />
  </view>
</template>
<script lang="ts" setup>
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import AppButton from '@/components/AppButton/index.vue';
import OssImg from '@/components/OSSImg/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import StationInfo from '@/pages/charging/index/components/StationInfo.vue';
import {
  ChargingOrdersService,
  OrderDetailQueryRespVO,
  StartChargeReqVO,
  UpOrderReqVO,
  WalletService,
} from '@/service';
import payment from '@/utils/pay/unifyPay';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { productCodeEnum, sourceChannelEnum } from '../index/constants';
import InfoCard from './components/InfoCard.vue';
import RefundInfoPop from './components/RefundInfoPop.vue';
import {
  endStatusCfg,
  orderInfoList,
  basePriceInfo,
  personalPriceInfo,
  enpPriceInfo,
} from './constants';
import { ENP_PAY_MODE } from '../constants';

dayjs.extend(duration);

const orderInfo = ref<OrderDetailQueryRespVO>({});

// 是否展示底部按钮
const showFooterButton = computed(() => {
  if (orderInfo.value && Number(orderInfo.value.owedAmount) > 0) {
    if (
      orderInfo.value.payMode === StartChargeReqVO.payMode.ENP_CREDIT ||
      orderInfo.value.payMode === StartChargeReqVO.payMode.ENP_PREPAID
    ) {
      return false;
    } else {
      return true;
    }
  } else {
    return false;
  }
});

// 订单信息列表
const priceInfoList = computed(() => {
  if (orderInfo.value && orderInfo.value.payMode) {
    const { payMode } = orderInfo.value;
    if (ENP_PAY_MODE.includes(payMode)) {
      return [...basePriceInfo, ...enpPriceInfo];
    } else {
      return [...basePriceInfo, ...personalPriceInfo];
    }
  } else {
    return [];
  }
});

const timer = ref();
const isLoading = ref(true);
const isError = ref(false);
const isPaying = ref(false);

const goPay = async () => {
  let result = false;
  if (isPaying.value) {
    return;
  }
  isPaying.value = true;
  if (orderInfo.value.payMode === OrderDetailQueryRespVO.payMode.ACC_BALANCE) {
    result = await goWalletPay();
  } else {
    result = await goPrepay();
  }
  isPaying.value = false;
  if (result) {
    uni.showLoading();
    init();
  }
};
const goPrepay = async () => {
  const [err, res] = await WalletService.postWalletOweOrderRecharge({
    bizOrderId: orderId.value,
    // @ts-ignore
    sourceChannel: sourceChannelEnum[process.env.UNI_PLATFORM],
  });
  if (res?.data) {
    const payResult = await payment.dispatchPay(res.data);
    return payResult === 'success';
  } else {
    return false;
  }
};
const goWalletPay = async () => {
  console.log(process.env.UNI_PLATFORM);
  const [err, res] = await WalletService.postWalletRecharge({
    // @ts-ignore
    sourceChannel: sourceChannelEnum[process.env.UNI_PLATFORM],
    bizType: UpOrderReqVO.bizType.RECHARGE,
    // @ts-ignore
    productCode: productCodeEnum[process.env.UNI_PLATFORM],
    orderType: UpOrderReqVO.orderType.RECHARGE_ORDER,
    orderAmount: Number(orderInfo.value.owedAmount || 0) * 100,
    callbackUrl: '233',
  });
  console.error('--------', err, res);

  if (res?.data) {
    const payResult = await payment.dispatchPay(res.data);
    return payResult === 'success';
  } else {
    return false;
  }
};

const orderId = ref('');
onLoad((options) => {
  orderId.value = options?.orderId;
  init();
  timer.value = setInterval(() => {
    init();
  }, 3000);
});
onPageScroll(() => {}); // 需要重写一下，不然是不会生效的

const clearTimer = () => {
  clearInterval(timer.value);
  timer.value = null;
};
onUnload(() => {
  clearTimer();
});
const refreshPage = () => {
  isLoading.value = true;
  isError.value = false;
  init();
  timer.value = setInterval(() => {
    init();
  }, 3000);
};
const init = async () => {
  const [err, res] = await ChargingOrdersService.postOrderDetail({
    orderId: orderId.value,
  });
  isLoading.value = false;
  if (err) {
    clearTimer();
    uni.hideLoading();
    // showSingleToast(err.subMsg || '系统异常');
    isError.value = true;
    return;
  }
  if (res) {
    orderInfo.value = res.data || {};
    // @ts-ignore
    orderInfo.value.type = res.data?.chargeType;
    if (res.data?.orderStatus === 'FINISHED') {
      clearTimer();
      uni.hideLoading();
    }
  }
};
const setClipboardData = (data: any) => {
  uni.setClipboardData({
    data,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      });
    },
  });
};
const showRefundInfoPop = ref(false);
const changeShowRefundInfoPop = (val: boolean) => {
  showRefundInfoPop.value = val;
};
</script>
<style lang="scss" scoped>
.end-charging-page {
  .header-bg {
    background-image: url('#{$oss-prefix}/images/common/common-header-bg.png');
    background-size: 100% 100%;
  }
}
</style>
