<template>
  <APopup
    title="退款信息"
    :center="true"
    maxHeight="70vh"
    :bottom="true"
    :safeAreaInsetBottom="true"
    @close="emits('close')"
  >
    <view>
      <view class="mb-10">
        <InfoCard :infoList="infoList" :data="refundInfo" className="p-0 mt-0 mb-10">
          <template #refundStatus>
            <text
              v-if="refundInfo.refundStatus"
              :style="{ color: refundStatusName[refundInfo.refundStatus].color }"
            >
              {{ refundStatusName[refundInfo.refundStatus].label }}
            </text>
          </template>
        </InfoCard>
      </view>
    </view>
    <template #bottom>
      <AppButton type="brand" @click="emits('close')">确定</AppButton>
    </template>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { RefundDetailVO } from '@/service';
import { refundInfoList, refundStatusName } from '../constants';
import InfoCard from './InfoCard.vue';

const props = withDefaults(
  defineProps<{
    refundInfo: RefundDetailVO;
  }>(),
  {
    refundInfo: () => ({}),
  },
);
const emits = defineEmits(['close']);
const infoList = computed(() => {
  if (props.refundInfo.refundStatus === 'SUCCESS') {
    return refundInfoList;
  } else {
    return refundInfoList.filter(
      (item) => item.key !== 'refundAmount' && item.key !== 'refundApplyTime',
    );
  }
});
</script>
