<template>
  <view :class="['common-card', 'mt-6', className]">
    <view v-if="title" class="text-primary-32 text-text-primary mb-10">{{ title }}</view>
    <view>
      <view
        class="mt-5 flex-between items-center text-primary-28 font-400"
        v-for="item in infoList"
        :key="item.key"
      >
        <view :class="['text-text-sub', 'shrink-0', item.labelTop ? 'self-start' : '']">{{
          item.label
        }}</view>
        <slot v-if="item.slotName" :name="item.slotName"></slot>
        <view class="text-text-primary" v-else-if="data[item.key]">
          {{ item.unitBefore || '' }}{{ item.format ? item.format(data[item.key]) : data[item.key]
          }}{{ item.unitAfter || '' }}
        </view>
        <view class="text-text-primary" v-else>--</view>
      </view>
    </view>
    <slot name="footer"></slot>
  </view>
</template>
<script lang="ts" setup>
interface infoItem {
  label: string;
  key: string;
  unitBefore?: string;
  unitAfter?: string;
  slotName?: string;
  labelTop?: boolean;
  format?: (value: any) => void;
}
const props = withDefaults(
  defineProps<{
    title: string;
    infoList: infoItem[];
    data: { [key: string]: any };
    className?: string;
  }>(),
  {
    title: '',
    infoList: () => [],
    data: () => ({}),
    className: '',
  },
);
</script>
