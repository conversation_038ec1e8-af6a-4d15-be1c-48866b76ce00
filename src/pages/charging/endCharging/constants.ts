import { OrderDetailQueryRespVO } from '@/service';

export const endStatusCfg = {
  SETTLING: {
    icon: '/images/charging/end-wait.png',
    title: '结算中',
    subtitle: '充电订单正在结算中',
  },
  PAY_WAIT: {
    icon: '/images/charging/end-wait.png',
    title: '待支付',
    subtitle: '本次充电已完成，期待您再次充电',
  },
  START_ERROR: {
    icon: '/images/charging/end-warning.png',
    title: '已完成',
    subtitle: '充电订单启动失败',
  },
  FINISHED: {
    icon: '/images/charging/end-success.png',
    title: '已完成',
    subtitle: '本次充电已完成，期待您再次充电',
  },
  INIT: undefined,
  STARTING: undefined,
  CHARGING: undefined,
};

export const basePriceInfo = [
  {
    label: '实付金额',
    key: 'realPrice',
    slotName: 'realPrice',
  },
  {
    label: '订单总额',
    key: 'originPrice',
    unitBefore: '¥',
  },
  {
    label: '电费',
    key: 'originElecPrice',
    unitBefore: '¥',
  },
  {
    label: '服务费',
    key: 'originServicePrice',
    unitBefore: '¥',
  },
];

export const personalPriceInfo = [
  {
    label: '电费优惠',
    key: 'elecDiscountPrice',
    unitBefore: '- ¥',
  },
  {
    label: '服务费优惠',
    key: 'serviceDiscountPrice',
    unitBefore: '- ¥',
  },
];

export const enpPriceInfo = [
  {
    label: '电费优惠',
    key: 'enpElecDisPrice',
    unitBefore: '- ¥',
  },
  {
    label: '服务费优惠',
    key: 'enpServiceDisPrice',
    unitBefore: '- ¥',
  },
];

export const payModeName = {
  [OrderDetailQueryRespVO.payMode.ACC_BALANCE]: '账户余额',
  [OrderDetailQueryRespVO.payMode.PREPAY]: '用户预充',
  [OrderDetailQueryRespVO.payMode.ENP_CREDIT]: '企业授信',
  [OrderDetailQueryRespVO.payMode.ENP_PERSONAL]: '用户预充',
  [OrderDetailQueryRespVO.payMode.ENP_PREPAID]: '企业预存',
  [OrderDetailQueryRespVO.payMode.THIRD_PAY]: '三方平台',
};
export const orderInfoList = [
  {
    label: '订单编号',
    key: 'orderId',
    slotName: 'orderId',
    labelTop: true,
  },
  {
    label: '支付方式',
    key: 'payMode',
    format: (val: OrderDetailQueryRespVO.payMode) => {
      return payModeName[val];
    },
  },
  {
    label: '预存金额',
    key: 'rechargeAmount',
    unitBefore: '¥',
  },
  {
    label: '退还金额',
    key: 'rechargeRefundAmount',
    unitBefore: '¥',
  },
  {
    label: '下单时间',
    key: 'orderTime',
  },
  {
    label: '结算时间',
    key: 'endTime',
  },
];
export const refundInfoList = [
  {
    label: '退款说明',
    key: 'refundReason',
  },
  {
    label: '退款金额',
    key: 'refundAmount',
    unitBefore: '¥',
  },
  {
    label: '退款时间',
    key: 'refundApplyTime',
  },
  {
    label: '退款状态',
    key: 'refundStatus',
    slotName: 'refundStatus',
  },
];
export const refundStatusName = {
  AUDITING: {
    label: '审核中',
    color: '#F33813',
  },
  AUDIT_CANCEL: {
    label: '审核取消',
    color: '#F33813',
  },
  AUDIT_REJECT: {
    label: '退款失败',
    color: '#F33813',
  },
  SUCCESS: {
    label: '退款成功',
    color: '#1FB3BE',
  },
  FAIL: {
    label: '退款失败',
    color: '#F33813',
  },
};
