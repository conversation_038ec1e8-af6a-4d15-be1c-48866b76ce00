<route lang="json5">
{
  style: {
    navigationBarTitleText: '扫码充电',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'always',
    },
  },
}
</route>
<template>
  <view class="h-screen flex-col common-page-bg">
    <TransparentTitle title="扫码充电" />
    <view class="w-554rpx h-473rpx relative mt-175rpx mx-auto">
      <!-- <OssImg :width="400" :height="400" src="/images/charging/starting.png" /> -->
      <!-- <LottieImg
        id="start-process"
        :width="680"
        :height="680"
        path="/images/charging/start-process.json"
      /> -->
      <StartLoading />
      <view class="absolute w-554rpx h-473rpx top-50% left-50% translate-x--50% translate-y--50%">
        <view class="flex-col-center w-full h-full text-text-primary">
          <view class="mt-11">
            <text class="text-20 leading-74rpx font-700 font-DIN">
              {{ nowData > 9 ? nowData : `0${nowData}` }}
            </text>
            <text class="text-primary-32">s</text>
          </view>
          <view class="charging-tip text-secondary-26 mt-2 text-text-sub">启动充电中</view>
        </view>
      </view>
    </view>
    <!-- <AppButton type="charging" className="mt-222rpx w-339rpx">结束充电</AppButton> -->
  </view>
</template>
<script lang="ts" setup>
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { ChargingOrdersService } from '@/service';
import StartLoading from './components/startLoading.vue';

const nowData = ref(90);
const timerNowData = ref();
const queryInterval = ref();
const orderId = ref('');
const countDown = () => {
  timerNowData.value = setInterval(() => {
    nowData.value--;
    if (nowData.value < 1) {
      clearIntervalData();
      nowData.value = 0;
    }
  }, 1000);
};
const queryOrderInfo = async () => {
  const [err, res] = await ChargingOrdersService.postOrderDetail({
    orderId: orderId.value,
  });
  if (err) {
    if (nowData.value < 5) {
      uni.redirectTo({
        url: `/pages/charging/startFail/index?failReason=88&orderId=${orderId.value}`,
      });
      clearIntervalData();
    }
    return;
  }
  if (res) {
    const { orderStatus } = res.data || {};
    if (nowData.value < 5 || res.data?.orderStatus === 'START_ERROR') {
      uni.redirectTo({
        url: `/pages/charging/startFail/index?failReason=88&orderId=${orderId.value}`,
      });
      clearIntervalData();
    } else if (orderStatus === 'CHARGING') {
      uni.redirectTo({
        url: `/pages/charging/isCharging/index?orderId=${orderId.value}`,
      });
      clearIntervalData();
    } else if (orderStatus === 'STARTING' || orderStatus === 'INIT') {
      console.log('继续轮询');
      // 继续轮巡
    } else {
      uni.redirectTo({
        // url: `/pages/charging/endCharging/index?orderId=${orderId.value}`,
        url: `/pages/order/charging/orderDetail?orderId=${orderId.value}`,
      });
      clearIntervalData();
    }
  }
};
const clearIntervalData = () => {
  clearInterval(timerNowData.value);
  timerNowData.value = null;
  clearInterval(queryInterval.value);
  queryInterval.value = null;
};
onLoad((options) => {
  orderId.value = options?.orderId || '';
  countDown();
  queryOrderInfo();
  queryInterval.value = setInterval(queryOrderInfo, 3000);
});
onUnload(() => {
  clearIntervalData();
});
</script>
<style lang="scss" scoped>
.charging-tip::after {
  content: '';
  animation: loading linear 3s infinite;
}

@keyframes loading {
  0% {
    content: '.';
  }

  50% {
    content: '..';
  }

  100% {
    content: '...';
  }
}
</style>
