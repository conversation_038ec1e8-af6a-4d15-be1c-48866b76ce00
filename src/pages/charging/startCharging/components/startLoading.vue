<template>
  <canvas class="w-full h-full" canvas-id="startLoading" id="startLoading" />
</template>

<script lang="ts" setup>
import { useGetSystemInfo } from '@/hooks/useGetSystemInfo';

const { rpx2px, systemInfo } = useGetSystemInfo();
const ctx = uni.createCanvasContext('startLoading', getCurrentInstance());
let progress = 0;
let timer: any = null;
const x = 277;
const y = 258;
const r = 215;
const lineWidth = 30;
const drawLoading = () => {
  ctx.clearRect(0, 0, 200, 200);

  // 设置线条末端为圆角
  ctx.setLineCap('round');

  // 绘制背景虚线圆弧 - 只绘制280度（1.6π）
  ctx.beginPath();
  ctx.setLineWidth(rpx2px(lineWidth));
  ctx.setStrokeStyle('rgba(86, 190, 102, 0.1)');
  ctx.arc(rpx2px(x), rpx2px(y), rpx2px(r), -1.25 * Math.PI, -1.25 * Math.PI + 1.5 * Math.PI);
  ctx.stroke();

  // 绘制进度圆弧 - 添加渐变色
  ctx.beginPath();
  ctx.setLineWidth(rpx2px(lineWidth));

  // // 创建渐变色
  // const gradient = ctx.createCircularGradient(rpx2px(x), rpx2px(y), rpx2px(r));
  // // 添加渐变色断点
  // gradient.addColorStop(0, 'rgba(86, 190, 102, 0.1318)'); // 起始颜色
  // gradient.addColorStop(1, '#6CD67C'); // 结束颜色
  // ctx.setStrokeStyle(gradient);
  ctx.setStrokeStyle('#6CD67C');
  const endAngle = -1.25 * Math.PI + 1.5 * Math.PI * progress;
  ctx.arc(rpx2px(x), rpx2px(y), rpx2px(r), -1.25 * Math.PI, endAngle);
  ctx.stroke();

  // 绘制末端装饰圆点
  if (progress > 0) {
    // 绘制外圈白色圆
    ctx.beginPath();
    ctx.setFillStyle('#FFFFFF');
    const dotX = rpx2px(x) + rpx2px(r) * Math.cos(endAngle);
    const dotY = rpx2px(y) + rpx2px(r) * Math.sin(endAngle);
    ctx.arc(dotX, dotY, rpx2px(lineWidth / 2), 0, Math.PI * 2);
    ctx.fill();

    // 绘制内部绿色圆
    ctx.beginPath();
    ctx.setFillStyle('#4CD964');
    ctx.arc(dotX, dotY, rpx2px(6), 0, Math.PI * 2);
    ctx.fill();
  }

  // 绘制放射线
  const rays = 13;
  for (let i = 0; i < rays; i++) {
    const angle = -1.25 * Math.PI + (i * Math.PI * 1.5) / (rays - 1);
    const startRadius = rpx2px(r + lineWidth + 12);
    const endRadius = rpx2px(r + lineWidth);

    const startX = rpx2px(x) + startRadius * Math.cos(angle);
    const startY = rpx2px(y) + startRadius * Math.sin(angle);
    const endX = rpx2px(x) + endRadius * Math.cos(angle);
    const endY = rpx2px(y) + endRadius * Math.sin(angle);

    ctx.beginPath();
    ctx.setLineWidth(2);
    ctx.setStrokeStyle('rgba(86, 190, 102, 0.3)');
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }

  ctx.draw();
  if (systemInfo.value.platform.toLowerCase() === 'ios') {
    progress += 0.0013;
  } else {
    progress += 0.00113;
  }
  if (progress <= 1) {
    timer = setTimeout(() => {
      drawLoading();
    }, 100);
  }
};

onMounted(() => {
  progress = 0;
  // setTimeout(() => {
  drawLoading();
  // }, 300);
});

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer);
  }
});
</script>
