<route lang="json5">
{
  style: {
    navigationBarTitleText: '扫码充电',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'always',
    },
  },
}
</route>
<template>
  <view class="h-screen flex-col-center justify-start common-page-bg">
    <TransparentTitle title="扫码充电" :keep-space="true" />
    <OssImg
      className="mt-175rpx"
      :width="566"
      :height="484"
      src="/images/charging/start-fail.png"
    />
    <view class="mt--62rpx w-702rpx mx-auto p-6 box-border mt-21">
      <!-- <view class="w-full text-center text-primary-32 text-primary">设备启动失败</view> -->
      <view class="w-full min-h-246rpx">
        <template>
          <view class="text-secondary-26 text-text-primary font-500">
            可能有以下原因导致启动失败：
          </view>
          <view class="text-secondary-24 text-text-sub font-400 mt-2">
            <template v-if="failMsg">{{ failMsg }}</template>
            <template v-else>
              1、充电枪未插好，请重新插拔充电枪；
              <br />
              2、设备故障，请查看充电桩故障灯是否亮起，若故障灯亮起请更换充电桩进行尝试。
            </template>
          </view>
        </template>
      </view>
    </view>
    <view
      class="fixed bg-white p-6 pb-safe-6 bottom-0 left-0 w-full flex justify-center box-border"
    >
      <view class="slider-1px absolute top-0 left-0 w-full"></view>
      <AppButton
        className="w-339rpx"
        type="brand"
        :plain="!hideSubBtnErr.includes(failReason)"
        @click="goHome"
      >
        返回首页
      </AppButton>
      <AppButton
        type="brand"
        className="ml-6 w-339rpx"
        v-if="!hideSubBtnErr.includes(failReason)"
        @click="clickConfirm"
      >
        {{ reScan.includes(failReason) ? '重新扫码' : '查看订单' }}
      </AppButton>
    </view>
  </view>
</template>
<script lang="ts" setup>
import AppButton from '@/components/AppButton/index.vue';
import OssImg from '@/components/OSSImg/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { chargeScanTap } from '@/utils/scanHandler';
import { failMsgTip } from './constants';

const failReason = ref('');
const orderId = ref('');
const failMsg = ref('');
onLoad((options) => {
  failReason.value = options?.failReason || '88';
  orderId.value = options?.orderId || '';
  failMsg.value =
    // @ts-ignore
    failReason.value && failMsgTip[failReason.value] ? failMsgTip[failReason.value] || '' : '';
});
const hideSubBtnErr = ['4', '9', '10', '11', '99'];
const reScan = ['12', '13', '88'];
const goHome = () => {
  uni.switchTab({ url: '/pages/home/<USER>' });
};
const clickConfirm = async () => {
  if (reScan.includes(failReason.value)) {
    // 重新扫码
    chargeScanTap();
  } else {
    uni.redirectTo({
      // url: `/pages/charging/endCharging/index?orderId=${orderId.value}`,
      url: `/pages/order/charging/orderDetail?orderId=${orderId.value}`,
    });
  }
};
</script>
<style lang="scss" scoped></style>
