<route lang="json5">
{
  style: {
    navigationBarTitleText: '扫码充电',
    'mp-weixin': {
      navigationStyle: 'custom',
    },
    'mp-alipay': {
      transparentTitle: 'auto',
    },
  },
}
</route>
<template>
  <view class="px-8 box-border pb-safe-170rpx charging-page-bg min-h-screen">
    <TransparentTitle title="扫码充电" :keep-space="true" />
    <!-- 提示信息 -->
    <view
      class="w-screen ml--8 bg-#FFF9ED rounded-2 py-4 px-6 flex of-hidden items-center box-border"
      v-if="chargingOrderInfo.errorMessage"
    >
      <OSSImg className="shrink-0" src="/images/charging/warn.png" :width="36" :height="36" />
      <view class="text-secondary-24 text-#FF5E31 truncate ml-3">
        充电异常信息提示：{{ chargingOrderInfo.errorMessage }}
      </view>
    </view>
    <!-- 充电信息 -->
    <view class="w-max mx-auto mt-24 flex justify-center text-text-primary relative">
      <view class="absolute left-0 translate-x--1/1 text-primary-32 font-400 bottom-0">soc:</view>
      <view class="text-113rpx leading-20 font-DIN font-bold">
        {{ Number(chargingOrderInfo.soc || 0) }}
      </view>
      <view class="absolute right-0 translate-x-1/1 font-400 bottom-0 flex items-end w-max">
        <view class="text-primary-32 shrink-0">%</view>
        <view
          class="px-3 py-1 rounded-2 bg-brand-lighter text-brand-primary text-5 leading-7 ml-3 shrink-0 border1 before:rounded-4 before:border-brand-primary"
          v-if="chargingOrderInfo.plateNo"
        >
          {{ chargingOrderInfo.plateNo }}
        </view>
      </view>
    </view>
    <view class="w-max mx-auto mt-2 text-brand-primary text-primary-28 font-400">充电中</view>
    <InfoAni :currentPowerSoc="Number(chargingOrderInfo.soc || 0)" />
    <!-- 信息卡片 -->
    <StatusCard :chargingOrderInfo="chargingOrderInfo" />
    <!-- 支付卡片 -->
    <PayCard
      :chargingOrderInfo="chargingOrderInfo"
      :showChangeCouponBox="showChangeCouponBox"
      @updateCouponType="updateCouponType"
    />
    <view
      class="fixed p-6 pb-safe-6 bg-white w-full box-border left-0 bottom-0 bordered-t-1-solid-divider-color"
    >
      <AppButton
        :disabled="isQuerying || !Object.keys(chargingOrderInfo).length"
        @click="() => (showStopConfirm = true)"
        type="brand"
      >
        结束充电
      </AppButton>
    </view>
  </view>
  <AMessageBox
    v-model:show="showStopConfirm"
    msg="确定停止充电吗"
    type="confirm"
    @confirm="stopCharging"
  />
  <AMessageBox
    v-model:show="stopError"
    :msg="`远程停止失败，请在充电桩上操作，\n如有疑问请咨询客服`"
    type="alert"
  >
    <template #footer>
      <view class="text-primary-28 font-400 text-text-sub w-max mx-auto mt-6" @click="handleHelp">
        咨询客服
      </view>
    </template>
  </AMessageBox>
</template>
<script lang="ts" setup>
import AMessageBox from '@/components/AMessageBox/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { ChargingService, PrizeListRequest, ProcessQueryRespVO, WalletService } from '@/service';
import { showSingleToast } from '@/utils/jsapi';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import InfoAni from './components/InfoAni.vue';
import PayCard from './components/PayCard.vue';
import StatusCard from './components/StatusCard.vue';
import { ChargingOrderInfo } from './interface';

dayjs.extend(duration);
const chargingOrderInfo = ref<ChargingOrderInfo>({});
const orderId = ref('');
const timer = ref();
const isQuerying = ref(false);
const updateCouponType = (couponType: ProcessQueryRespVO.couponType) => {
  chargingOrderInfo.value.couponType = couponType;
};
const showStopConfirm = ref(false);
const stopError = ref(false);
onLoad((options) => {
  orderId.value = options?.orderId;
  init();
  timer.value = setInterval(() => {
    init();
  }, 3000);
});
const clearTimer = () => {
  clearInterval(timer.value);
  timer.value = null;
};
const showChangeCouponBox = ref(false);
const queryCouponInfo = async () => {
  const [err, res] = await WalletService.postWalletPrizeQuery({
    page: 1,
    pageSize: 1,
    defrayStatus: PrizeListRequest.defrayStatus.GRANTED,
  });
  if (res?.data?.totalNum) {
    showChangeCouponBox.value = true;
  }
};
const init = async () => {
  const [err, res] = await ChargingService.postChargeProcess({ orderId: orderId.value });
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  if (res?.data?.status !== 'CHARGING') {
    if (isQuerying.value) {
      uni.hideLoading();
      isQuerying.value = false;
    }
    uni.redirectTo({
      // url: `/pages/charging/endCharging/index?orderId=${orderId.value}`,
      url: `/pages/order/charging/orderDetail?orderId=${orderId.value}`,
    });
    clearTimer();
  } else {
    // 首次请求判断是否展示切换优惠券模块
    if (!Object.keys(chargingOrderInfo.value).length) {
      if (res?.data.couponType === 'NONE') {
        queryCouponInfo();
      } else {
        showChangeCouponBox.value = true;
      }
    }
    chargingOrderInfo.value = res?.data || {};
    chargeTimeFormat();
  }
};
onUnload(() => {
  clearTimer();
});
const chargeTimeFormat = () => {
  if (!chargingOrderInfo.value.chargeTime) {
    return;
  }
  const [hour, minute, second] = dayjs
    .duration(chargingOrderInfo.value.chargeTime, 'seconds')
    .format('HH:mm:ss')
    .split(':');
  console.error(hour, minute, second);
  // @ts-ignore
  chargingOrderInfo.value = { ...chargingOrderInfo.value, hour, minute, second };
  console.error(chargingOrderInfo.value);
};
const stopCharging = async () => {
  // const result = await showModal({
  //   content: '确认是否停止充电？',
  // });
  // if (!result || isQuerying.value) return;
  if (isQuerying.value) return;
  isQuerying.value = true;
  const [err, res] = await ChargingService.postChargeStop({ orderId: orderId.value });
  if (res?.data?.failReason === '0') {
    uni.showLoading({
      title: '充电停止中',
      mask: true,
    });
    // const timer = setTimeout(() => {
    //   uni.hideLoading();
    //   isQuerying.value = false;
    //   clearTimeout(timer);
    // }, 5000);
  } else {
    // uni.showToast({
    //   title: '停止失败，请到充电桩上操作；如有疑问请联系客服',
    //   icon: 'none',
    // });
    stopError.value = true;
    isQuerying.value = false;
  }
};
const handleHelp = () => {
  uni.navigateTo({
    url: '/pages/mine-sub/service/index/index',
  });
  stopError.value = false;
};
</script>
<style lang="scss" scoped>
.charging-page-bg {
  background-image: linear-gradient(180deg, rgb(117 208 131 / 18%) 0%, rgb(117 208 131 / 0%) 49%),
    #fff;
  background-repeat: no-repeat;
  background-size: 100% 1624rpx;
}

// @keyframes progress {
//   0% {
//     width: 0;
//   }

//   100% {
//     width: 100%;
//   }
// }

// .is-charging-page {
//   // background-image: url('#{$oss-prefix}/images/charging/is-charging-bg.png');
//   // background-size: 100% 100%;

//   .error-tip {
//     background: linear-gradient(276deg, #fff -30%, rgb(255 255 255 / 0%) 74%, #fff 108%);
//   }

//   .car-outer {
//     transform: perspective(0.5em) rotateX(1.5deg);
//     transform-origin: bottom;
//   }

//   .car-inner {
//     background: linear-gradient(90deg, #25e2a6 34%, #30d9e5 99%);
//     animation: progress 4s linear infinite;

//     &.car-inner-20 {
//       background: linear-gradient(88deg, #ffa03a 25%, #fc5a2d 94%);
//     }

//     &.car-inner-60 {
//       background: linear-gradient(89deg, #fce02d 51%, #ffa03a 99%);
//     }
//   }
// }
</style>
