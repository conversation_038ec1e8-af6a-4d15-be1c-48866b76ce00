<template>
  <view class="w-full mt--13">
    <view class="flex-center">
      <view
        class="text-secondary-24 text-text-secondary px-8 py-3 rounded-218rpx bg-page-background"
      >
        <text class="text-text-sub">充电桩编号：</text>
        {{ chargingOrderInfo.pileNo || '--' }}_{{ chargingOrderInfo.gunNo || '--' }}
      </view>
    </view>
    <view class="flex-between flex-wrap mt-8">
      <view
        v-for="(item, index) in chargingInfo"
        :key="index"
        class="w-50 shrink-0 flex-col-center my-6"
      >
        <OSSImg :width="40" :height="40" :src="item.icon" />
        <view class="text-primary flex items-center mt-14rpx">
          <template v-if="typeof item.key === 'string'">
            <view class="text-10 leading-11 font-700 mt--2rpx font-DIN font-bold">
              {{ chargingOrderInfo[item.key] || item.default || '--' }}
            </view>
            <view class="ml-1 text-secondary-24">
              {{ item.unit }}
            </view>
          </template>
          <template v-else v-for="(keyItem, keyIndex) in item.key" :key="keyItem">
            <view class="text-10 leading-11 font-700 mt--2rpx font-DIN font-bold">
              {{ chargingOrderInfo[keyItem] || item.default || '--' }}
            </view>
            <view
              :class="['ml-1', 'text-secondary-24', keyIndex !== item.key.length - 1 ? 'mr-1' : '']"
            >
              {{ item.unit[keyIndex] }}
            </view>
          </template>
        </view>
        <view class="mt-14rpx text-secondary-24 text-text-secondary">
          {{ item.label }}
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import OSSImg from '@/components/OSSImg/index.vue';
import { chargingInfo } from '../constants';

const props = withDefaults(
  defineProps<{
    chargingOrderInfo: any;
  }>(),
  {
    chargingOrderInfo: {},
  },
);
</script>
