<template>
  <view class="common-card mt-8 !bg-page-background">
    <view class="text-primary-36 text-primary">
      {{ chargingOrderInfo.stationName || '--' }}
    </view>
    <view class="mt-6 flex-between">
      <text class="text-primary-28 font-400 text-text-sub">支付方式</text>
      <view class="flex-center text-secondary-24 text-text-secondary">
        <template v-if="chargingOrderInfo.payMode">
          <!-- <OssImg
            v-if="payModeInfo[chargingOrderInfo.payMode].icon"
            :width="30"
            :height="30"
            :src="payModeInfo[chargingOrderInfo.payMode].icon"
          /> -->
          <text>
            {{ payModeInfo[chargingOrderInfo.payMode].label }}
          </text>
          <text
            v-if="
              (chargingOrderInfo.payMode === OrderDetailQueryRespVO.payMode.PREPAY ||
                chargingOrderInfo.payMode === OrderDetailQueryRespVO.payMode.ENP_PERSONAL) &&
              chargingOrderInfo.rechargeAmount
            "
          >
            ￥{{ chargingOrderInfo.rechargeAmount }}
          </text>
        </template>
        <template v-else>--</template>
      </view>
    </view>
    <view class="mt-6 flex-between" v-if="showCouponInfo">
      <text class="text-primary-28 font-400 text-text-sub">优惠券核销</text>
      <view class="flex-center" @click="updateShowChangeCouponPop(true)">
        <text class="text-secondary-24 text-text-secondary">
          {{ couponTypeTip[chargingOrderInfo.couponType].label }}
        </text>
        <OssImg :width="20" :height="20" src="/images/arrow-right.png" />
      </view>
    </view>
    <ChangeCouponPop
      v-if="showChangeCouponBox && showChangeCouponPop && chargingOrderInfo.couponType"
      :orderId="chargingOrderInfo.orderId || ''"
      :currentCouponType="chargingOrderInfo.couponType"
      @close="closeChangeCouponPop"
    />
  </view>
</template>
<script lang="ts" setup>
import OssImg from '@/components/OSSImg/index.vue';
import { couponTypeTip } from '@/pages/charging/index/constants';
import { OrderDetailQueryRespVO, ProcessQueryRespVO } from '@/service';
import { ENP_PAY_MODE } from '../../constants';
import { payModeInfo } from '../constants';
import { ChargingOrderInfo } from '../interface';
import ChangeCouponPop from './ChangeCouponPop.vue';

const props = withDefaults(
  defineProps<{
    chargingOrderInfo: ChargingOrderInfo;
    showChangeCouponBox: boolean;
  }>(),
  {
    chargingOrderInfo: () => ({}),
    showChangeCouponBox: false,
  },
);
const showChangeCouponPop = ref(false);
const updateShowChangeCouponPop = (value: boolean) => {
  showChangeCouponPop.value = value;
};

/**
 * @description: 是否展示优惠券信息
 * @return {boolean}
 */
const showCouponInfo = computed(() => {
  return (
    props.showChangeCouponBox &&
    props.chargingOrderInfo.couponType &&
    couponTypeTip[props.chargingOrderInfo.couponType] &&
    !ENP_PAY_MODE.includes(props.chargingOrderInfo.payMode)
  );
});

const emits = defineEmits(['updateCouponType']);
const closeChangeCouponPop = (couponType: ProcessQueryRespVO.couponType) => {
  updateShowChangeCouponPop(false);
  if (couponType) {
    emits('updateCouponType', couponType);
  }
};
</script>
