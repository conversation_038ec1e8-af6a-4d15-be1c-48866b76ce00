<template>
  <view class="mt-18rpx text-center charging-info-ani">
    <!-- <view class="text-status-wait text-7">充电中</view>
    <view class="text-primary">
      <text class="text-8">soc：</text>
      <text class="text-20 font-DIN font-bold">
        {{ currentPowerSoc || '0' }}
      </text>
      <text class="text-8">%</text>
    </view> -->
    <!-- 动效模块 -->
    <view class="relative mt-11 w-750rpx ml--6">
      <view class="w-screen of-x-hidden relative">
        <OssImg className="ml--17" :width="887" :height="352" src="/images/charging/car.png" />
        <view class="absolute w-262rpx h-74rpx top-130rpx left-234rpx soc-bg">
          <view class="w-242rpx h-58rpx ml-9rpx mt--2rpx rounded-1 of-hidden car-box">
            <view class="h-full rounded-1 car-outer" :style="{ width: `${currentPowerSoc}%` }">
              <view class="h-full car-inner"></view>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="absolute w-56 h-69rpx left-63 top-138rpx rounded-1 overflow-hidden car-outer">
        <view
          :class="[
            'h-full rounded-1',
            'car-inner',
            currentPowerSoc < 20 && currentPowerSoc >= 0
              ? 'car-inner-20'
              : currentPowerSoc < 60 && currentPowerSoc >= 20
                ? 'car-inner-60'
                : '',
          ]"
        ></view>
      </view> -->
    </view>
  </view>
</template>
<script lang="ts" setup>
import OssImg from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    currentPowerSoc: number;
  }>(),
  {
    currentPowerSoc: 0,
  },
);
</script>
<style lang="scss" scoped>
@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

.charging-info-ani {
  .car-box {
    transform: perspective(0.5em) rotateX(1.2deg);
    transform-origin: bottom;
  }

  .car-outer {
    background-image: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);
    transform: perspective(0.5em) rotateX(1.2deg);
    transform-origin: bottom;
  }

  .car-inner {
    background-image: url('#{$oss-prefix}/images/charging/soc-light.png');
    background-repeat: no-repeat;
    background-position: right;
    background-size: 72rpx 100%;
    animation: progress 2s linear infinite;
  }
}

.soc-bg {
  background-image: url('#{$oss-prefix}/images/charging/soc-bg.png');
  background-size: 100% 100%;
}
</style>
