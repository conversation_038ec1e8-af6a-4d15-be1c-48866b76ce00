<template>
  <APopup
    title="切换优惠券抵扣方式"
    :center="true"
    @close="emits('close')"
    :safeAreaInsetBottom="true"
    :lockScroll="true"
  >
    <view class="w-full">
      <view
        :class="[
          'common-card',
          '!bg-page-background',
          'mb-4',
          'box-border',
          selectedCouponType == item.key ? 'selected' : '',
        ]"
        v-for="item in couponTypeList"
        :key="item.key"
        @click="changeSelected(item.key)"
      >
        <view class="text-primary-28 text-text-primary">{{ item?.labelDetail }}</view>
        <view class="mt-2 text-secondary-26 text-text-secondary">{{ item?.tip }}</view>
      </view>
    </view>
    <view class="my-6 slider-1px w-750rpx ml--6"></view>
    <AppButton className="mb-6" type="brand" @click="confirmChange">选择该方式</AppButton>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { couponTypeList } from '@/pages/charging/index/constants';
import { ChargingService, ProcessQueryRespVO } from '@/service';
import { showSingleToast } from '@/utils/jsapi';

const emits = defineEmits(['close']);
const props = withDefaults(
  defineProps<{
    currentCouponType: ProcessQueryRespVO.couponType;
    orderId: string;
  }>(),
  {
    currentCouponType: ProcessQueryRespVO.couponType.NONE,
    orderId: '',
  },
);
const selectedCouponType = ref(props.currentCouponType);
const changeSelected = (val: ProcessQueryRespVO.couponType) => {
  selectedCouponType.value = val;
};
const confirmChange = async () => {
  const [err, res] = await ChargingService.postChargeCouponType({
    orderId: props.orderId,
    couponType: selectedCouponType.value,
  });
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  emits('close', selectedCouponType.value);
};
</script>
<style lang="scss" scoped>
.selected {
  @apply bordered-2rpx-solid-brand-primary;
}
</style>
