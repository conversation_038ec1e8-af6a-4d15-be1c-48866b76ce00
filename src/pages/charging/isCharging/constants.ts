import { OrderDetailQueryRespVO } from '@/service';

export const chargingInfo = [
  {
    label: '已充电量',
    key: 'chargePq',
    unit: '度',
    icon: '/images/charging/charged-capacity.png',
  },
  {
    label: '已消费金额',
    key: 'price',
    unit: '元',
    icon: '/images/charging/consumed-amount.png',
  },
  {
    label: '充电时长',
    key: ['hour', 'minute', 'second'],
    unit: ['时', '分', '秒'],
    default: '00',
    icon: '/images/charging/charged-time.png',
  },
  {
    label: '电压',
    key: 'voltage',
    unit: 'V',
    icon: '/images/charging/voltage.png',
  },
  {
    label: '电流',
    key: 'electric',
    unit: 'A',
    icon: '/images/charging/electric.png',
  },
  {
    label: '功率',
    key: 'power',
    unit: 'KW',
    icon: '/images/charging/power.png',
  },
];
export const payModeInfo = {
  [OrderDetailQueryRespVO.payMode.ACC_BALANCE]: {
    icon: '/images/charging/acc-balance.png',
    label: '账户余额',
  },
  [OrderDetailQueryRespVO.payMode.PREPAY]: {
    icon: '/images/charging/prepay.png',
    label: '用户预充',
  },
  [OrderDetailQueryRespVO.payMode.ENP_CREDIT]: {
    icon: '/images/charging/enp.png',
    label: '企业授信',
  },
  [OrderDetailQueryRespVO.payMode.ENP_PERSONAL]: {
    icon: '/images/charging/prepay.png',
    label: '用户预充',
  },
  [OrderDetailQueryRespVO.payMode.ENP_PREPAID]: {
    icon: '/images/charging/enp.png',
    label: '企业预存',
  },
  [OrderDetailQueryRespVO.payMode.THIRD_PAY]: {
    icon: '',
    label: '三方平台',
  },
};
