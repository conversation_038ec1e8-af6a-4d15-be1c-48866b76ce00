<route lang="json5">
{
  layout: false,
  style: {
    navigationBarTitleText: '扫码充电',
  },
}
</route>
<template>
  <!-- #ifdef MP-WEIXIN -->
  <page-meta :page-style="'overflow:' + (!pageScroll ? 'hidden' : 'visible')"></page-meta>
  <!-- #endif -->
  <AEmpty className="mt-100" v-if="isLoading" type="loading" />
  <AEmpty className="mt-50" v-else-if="isError" type="net" :button="true" @buttonClick="init" />
  <view v-else class="p-6 pb-safe-170rpx box-border min-h-screen" @click="pageClick">
    <!-- 站点信息 -->
    <StationInfo :baseInfo="baseInfo">
      <!-- <PriceInfo v-if="currentUserType === EUserType.personal" :priceInfo="baseInfo.priceInfo" /> -->
      <BPriceInfo
        v-if="currentUserType === EUserType.personal && baseInfo.priceInfo"
        className="mt-6"
        :showType="false"
        :priceList="[baseInfo.priceInfo]"
      />
    </StationInfo>
    <!-- 充电车辆输入 -->
    <PlateNoInput
      ref="plateNoInputRef"
      :plateNo="plateNo"
      @updatePlateNo="updatePlateNo"
      @updatePageScroll="updatePageScroll"
    />
    <!-- 充电用户类型 -->
    <view class="mt-6 mb-6" v-if="baseInfo.enterprisePrices && baseInfo.enterprisePrices.length">
      <!-- <wd-segmented
        customClass="mt-6 mb-6"
        :options="chargeUserTabs"
        v-model:value="currentUserType"
        @change="changeUserType"
      >
        <template #label="{ option }">
          <view class="section-slot h-20">
            <view class="name h-full flex-center">
              {{ option.label }}
            </view>
          </view>
        </template>
      </wd-segmented> -->
      <ATabs :list="chargeUserTabs" :current="currentUserTypeIndex" @click="changeUserType" />
      <view
        v-if="currentUserType === EUserType.personal"
        class="text-alert-color text-center text-6 mt-6"
      >
        个人身份下将无法享受企业协议价优惠
      </view>
    </view>

    <!-- 企业用户卡片 -->
    <view class="pos-relative" v-if="currentUserType === EUserType.enterprise">
      <!-- <EnterpriseCard :showLeft="false" :cardInfo="currentEnterpriseCard" /> -->
      <EnterpriseCardInfo :showLeft="false" :cardInfo="currentEnterpriseCard">
        <template #headerRight>
          <view
            v-if="baseInfo.enterprisePrices && baseInfo.enterprisePrices.length > 1"
            @click="changeEnterpriseListPop"
            class="flex items-center text-text-sub text-secondary-26"
          >
            切换
            <OSSImg
              className="ml-1"
              :width="24"
              :height="24"
              src="/images/charging/arrow-right-gray.png"
            />
          </view>
        </template>
      </EnterpriseCardInfo>
    </view>
    <view v-if="isPersonalPay">
      <ChargingPay
        :className="currentUserType === EUserType.enterprise ? '!mt--6 !rounded-t-0' : ''"
        :accBalance="Number(baseInfo.accBalance)"
        :minStartAmt="Number(baseInfo.minStartAmount)"
        :payAmout="payAmount"
        @changePayAmount="changePayAmount"
        :payMode="payMode"
        @changePayMode="changePayMode"
      />
    </view>
    <!-- 优惠券选择 -->
    <view v-if="currentUserType === EUserType.personal">
      <CouponChoose
        :couponList="couponList"
        :couponTotalNum="couponTotalNum"
        :couponType="couponType"
        @changeCouponType="changeCouponType"
      />
    </view>
    <view
      class="fixed bottom-0 left-0 p-6 pb-safe-6 bg-white w-full box-border bordered-t-1-solid-divider-color"
    >
      <AppButton type="brand" :disabled="payBtnDisabled" @click="handleStartCharge">
        启动充电
      </AppButton>
    </view>
    <WaitPayPop v-if="hasWaitPayOrder" :orderId="baseInfo.orderId || ''" />

    <!-- 企业选择 -->
    <EnterpriseListPop
      v-if="showEnterpriseListPop"
      :enterpriseList="baseInfo.enterprisePrices"
      :currentEnterprise="currentEnterpriseCard"
      @selectEnterprise="selectEnterprise"
      @close="changeEnterpriseListPop"
    />
  </view>
</template>
<script lang="ts" setup>
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import AppButton from '@/components/AppButton/index.vue';
import ATabs from '@/components/ATabs/index.vue';
import BPriceInfo from '@/components/BPriceInfo/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import { checkLogin } from '@/hooks/useCheckLogin';
import { useSubscribe } from '@/hooks/useSubscribe';
import {
  ChargingService,
  UserCouponsService,
  EnterpriseChargePriceVO as EnterpriseChargePriceVOName,
  MarketingRightsService,
} from '@/service';
import type { EnterpriseChargePriceVO, QrcodeScanRespVO, RightsFlowDetailVO } from '@/service';
import { StartChargeReqVO } from '@/service/models/StartChargeReqVO';
import { showSingleToast } from '@/utils/jsapi';
import unifyPay from '@/utils/pay/unifyPay';
import { TEMPLATE_IDS } from '@/utils/subscribeTemplateIds';
import { isPlate } from '@/utils/validates';
import { ENP_PAY_MODE } from '../constants';
import ChargingPay from './components/ChargingPay.vue';
import CouponChoose from './components/CouponChoose.vue';
import EnterpriseCardInfo from './components/EnterpriseCardInfo.vue';
import EnterpriseListPop from './components/EnterpriseListPop.vue';
import PlateNoInput from './components/PlateNoInput.vue';
import StationInfo from './components/StationInfo.vue';
import WaitPayPop from './components/WaitPayPop.vue';
import { chargeUserTabs, EUserType, prePriceList, TUserType } from './constants';

const currentUserType = ref<TUserType>(EUserType.personal);
const currentUserTypeIndex = ref(0);
// 切换用户类型
const changeUserType = (index: number) => {
  currentUserTypeIndex.value = index;
  currentUserType.value = chargeUserTabs[index].value;
  if (currentUserType.value === EUserType.personal) {
    changePayMode(StartChargeReqVO.payMode.PREPAY);
  } else if (currentUserType.value === EUserType.enterprise) {
    const payMode = currentEnterpriseCard.value?.payMode as StartChargeReqVO.payMode;
    changePayMode(payMode);
  }
};

const baseInfo = ref<QrcodeScanRespVO>({});
// 预充金额
const payAmount = ref(prePriceList[0].num);
// 修改预充金额
const changePayAmount = (val: string) => {
  payAmount.value = val;
};
// 充值模式
const payMode = ref(StartChargeReqVO.payMode.PREPAY);
// 切换充值模式
const changePayMode = (value: StartChargeReqVO.payMode) => {
  console.log('changePayMode', value);
  payMode.value = value;
};
// 优惠券列表
const couponList = ref<RightsFlowDetailVO[]>([]);
// 优惠券优先使用模式 DISCOUNT_AMT 优先使用抵扣券 EXPIRE_TIME 优先使用过期券 NONE 不使用优惠券
const couponType = ref(StartChargeReqVO.couponType.DISCOUNT_AMT);
const qrcode = ref('');
const isStarting = ref(false);
const isLoading = ref(true);
const isError = ref(false);
const pageScroll = ref(true);
const plateNo = ref();
const plateNoInputRef = ref(null);
const updatePlateNo = (value: string) => {
  plateNo.value = value;
};
const updatePageScroll = (scroll: boolean) => {
  pageScroll.value = scroll;
};

const pageClick = () => {
  console.log('pageClick', plateNoInputRef.value);
  // @ts-ignore
  plateNoInputRef.value?.closeKeyboard?.();
};
const getPlateNo = () => {
  const plateNo = uni.getStorageSync('chargePlateNo');
  if (plateNo) {
    updatePlateNo(plateNo);
  } else if (baseInfo.value.plateRequired) {
    updatePlateNo('粤');
  }
};

// 是否是个人支付
const isPersonalPay = computed(() => {
  if (currentUserType.value === EUserType.personal) {
    return true;
  } else if (currentEnterpriseCard.value?.payMode === StartChargeReqVO.payMode.ENP_PERSONAL) {
    return true;
  } else {
    return false;
  }
});

/**
 * @description: 点击启动充电按钮 订阅 处理数据
 * @return {*}
 */
const handleStartCharge = async () => {
  if (plateNo.value && !isPlate(plateNo.value)) {
    showSingleToast('车牌号输入有误');
    return;
  }
  if (baseInfo.value.plateRequired && !plateNo.value) {
    showSingleToast('充电车辆必填');
    return;
  }
  if (plateNo.value) {
    uni.setStorageSync('chargePlateNo', plateNo.value);
  }
  // 条件校验
  if (currentUserType.value === EUserType.enterprise) {
    // 企业用户校验次数限制及余额
    const { enable } = currentEnterpriseCard.value as EnterpriseChargePriceVO;
    if (enable === 'CHARGE_LIMIT') {
      uni.showToast({ title: '用户单日次数已达上限', icon: 'none' });
      return;
    } else if (enable === 'BALANCE_LIMIT') {
      uni.showToast({ title: '企业/车队余额不足', icon: 'none' });
      return;
    }
    if (enable !== 'ENABLE') return;
  }
  if (isStarting.value) return;
  isStarting.value = true;
  await useSubscribe({
    ids: TEMPLATE_IDS.charging,
  });
  const params: StartChargeReqVO = {
    pileNo: baseInfo.value.pileNo || '',
    gunNo: baseInfo.value.gunNo || '',
    qrcode: qrcode.value,
    couponType: couponTotalNum.value ? couponType.value : StartChargeReqVO.couponType.NONE,
    payMode: payMode.value,
    prepayAmount: payAmount.value,
    plateNo: plateNo.value,
  };
  if (ENP_PAY_MODE.includes(payMode.value)) {
    // 企业相关添加企业idz字段
    params.enterpriseId = currentEnterpriseCard.value?.enterpriseId;
  }
  if (
    payMode.value === StartChargeReqVO.payMode.PREPAY ||
    payMode.value === StartChargeReqVO.payMode.ENP_PERSONAL
  ) {
    // 个人支付 企业个人支付
    await prepayAndStartCharge(params);
  } else {
    await startCharge(params);
  }
  isStarting.value = false;
};

/**
 * @description: 预付费
 * @param {*} params
 * @return {*}
 */
const prepayAndStartCharge = async (params: StartChargeReqVO) => {
  uni.showLoading();
  const [err, res] = await ChargingService.postChargePrepay(params);
  uni.hideLoading();
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  if (res?.data) {
    const { orderId = '', orderStr } = res.data;
    try {
      const { ...params } = JSON.parse(orderStr);
      // @ts-ignore
      const payResult = await unifyPay.dispatchPay({
        // #ifdef MP-WEIXIN
        ...params,
        // #endif
        // #ifdef MP-ALIPAY
        tradeNO: params.alipayTradeNo,
        // #endif
        orderStr,
      });
      if (payResult === 'success' && orderId) {
        goStartChargePage(orderId);
      }
    } catch (e) {
      showSingleToast('系统异常');
    }
  }
};

// 启动充电
const startCharge = async (params: StartChargeReqVO) => {
  uni.showLoading();
  const [err, res] = await ChargingService.postChargeStart(params);
  uni.hideLoading();
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  if (res?.data) {
    const { orderId = '', failReason } = res.data;
    if (failReason === '0' && orderId) {
      goStartChargePage(orderId);
    } else {
      uni.redirectTo({
        url: `/pages/charging/startFail/index?failReason=${failReason}`,
      });
    }
  }
};
const goStartChargePage = (orderId: string) => {
  uni.redirectTo({
    url: `/pages/charging/startCharging/index?orderId=${orderId}`,
  });
};

// 优惠券优先使用变更
const changeCouponType = (val: StartChargeReqVO.couponType) => {
  couponType.value = val;
};
// 是否有待支付订单
const hasWaitPayOrder = ref(false);
// 支付按钮是否可点击
const payBtnDisabled = computed(() => {
  if (isStarting.value || !Object.keys(baseInfo.value).length) {
    return true;
  }
  if (payMode.value === StartChargeReqVO.payMode.ACC_BALANCE) {
    // 余额支付
    if (baseInfo.value.accBalance && baseInfo.value.minStartAmount) {
      return Number(baseInfo.value.accBalance) < Number(baseInfo.value.minStartAmount);
    }
    return Number(baseInfo.value.accBalance || 0) > 0;
  } else if (
    payMode.value === StartChargeReqVO.payMode.PREPAY ||
    payMode.value === StartChargeReqVO.payMode.ENP_PERSONAL
  ) {
    // 预付费支付 企业个人支付
    if (payAmount.value && baseInfo.value.minStartAmount) {
      return Number(payAmount.value) < Number(baseInfo.value.minStartAmount);
    }
    return Number(payAmount.value || 0) <= 0;
  } else {
    return false;
  }
});
onLoad(async (options) => {
  await checkLogin(`/pages/charging/index/index?qrcode=${options?.qrcode}`);
  qrcode.value = decodeURIComponent(options?.qrcode);
  // qrcode.value =
  //   'https://zhtccd.aihuaining.cn:18001/apigateway/charging-server?pileNo=TESTZWJ0823001&gunNo=1';
  init();
});
const init = async () => {
  isLoading.value = true;
  isError.value = false;
  await getBaseInfo();
  getCouponList();
  getPlateNo();
};

// 当前选中企业用户
const currentEnterpriseCard = ref<EnterpriseChargePriceVO>();
const selectEnterprise = (item: EnterpriseChargePriceVO) => {
  currentEnterpriseCard.value = item;
  changePayMode(item.payMode as StartChargeReqVO.payMode);
  changeEnterpriseListPop();
};
const showEnterpriseListPop = ref(false);
const changeEnterpriseListPop = () => {
  showEnterpriseListPop.value = !showEnterpriseListPop.value;
};

// 充电桩信息查询
const getBaseInfo = async () => {
  const [err, res] = await ChargingService.postChargeQrcode({
    qrcode: qrcode.value,
  });
  isLoading.value = false;
  if (err) {
    isError.value = true;
    return;
  }
  if (res?.data) {
    const { orderId, chargeStatus, enterprisePrices = [] } = res.data;
    if (chargeStatus === 'CHARGING') {
      uni.redirectTo({
        url: `/pages/charging/isCharging/index?orderId=${orderId}`,
      });
      return;
    }
    if (enterprisePrices && enterprisePrices.length) {
      enterprisePrices.forEach((item) => {
        if (
          item.elecPriceMode === EnterpriseChargePriceVOName.elecPriceMode.RATE &&
          item.elecPriceValue &&
          item.servicePriceValue
        ) {
          // 如果是折扣价 计算折扣率
          item.priceDiscount = item.elecPriceValue / 10;
          item.serviceDiscount = item.servicePriceValue / 10;
        }
      });
      currentUserType.value = EUserType.enterprise;
      // @ts-ignore
      currentEnterpriseCard.value = enterprisePrices[0];
      payMode.value = enterprisePrices[0].payMode as StartChargeReqVO.payMode;
    }
    baseInfo.value = res.data;

    if (chargeStatus === 'WAIT_PAY') {
      hasWaitPayOrder.value = true;
    }
  }
};
// 优惠券查询
const couponTotalNum = ref(0);
const getCouponList = async () => {
  const [err, res] = await MarketingRightsService.postMkplatRightsRuleFilterPreview({
    defrayType: 'PAY',
    defrayWay: 'EXPIRE_TIME',
    stationId: baseInfo.value.stationId,
    chargeType: baseInfo.value.type,
  });
  if (res?.data?.length) {
    couponList.value = res.data;
    couponTotalNum.value = res.data.length;
  } else {
    couponList.value = [];
    couponTotalNum.value = 0;
  }
};
defineExpose({
  changePlateNo: updatePlateNo,
});
</script>
<style scoped>
.change-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 110rpx;
  height: 44rpx;
  font-size: 24rpx;
  color: #fff;
  background: linear-gradient(124deg, #ff790f 14%, #ff6b6d 49%, #ff48a1 83%);
  border-radius: 0rpx 16rpx;
}
</style>
