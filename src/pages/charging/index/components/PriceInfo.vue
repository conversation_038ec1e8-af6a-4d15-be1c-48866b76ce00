<template>
  <view class="price-info rounded-4 bg-#F6F7F9 px-41rpx pb-21rpx pt-11 box-border mt-6 relative">
    <view
      class="absolute left-0 top-0 w-102rpx h-8 rounded-tl-4 rounded-br-4 text-white flex-center text-auxiliary current-label"
    >
      当前时段
    </view>
    <PriceBaseInfo
      :priceInfoList="priceInfoList"
      :priceInfo="priceInfo"
      :showDiscount="showDiscount"
    >
      <template #time>
        {{
          priceInfo.beginTime && priceInfo.endTime
            ? `${priceInfo.beginTime}-${priceInfo.endTime}`
            : '--'
        }}
      </template>
      <template #price>
        <text class="mr-1 text-price-color">¥</text>
        <text class="text-price-color text-primary-32">{{ priceInfo.price || '--' }}</text>
      </template>
    </PriceBaseInfo>
  </view>
</template>
<script lang="ts" setup>
import { ChargePriceVO } from '@/service';
import dayjs from 'dayjs';
import { priceInfoList } from '../constants';
import PriceBaseInfo from './PriceBaseInfo.vue';

interface PriceInfo {
  beginTime: string;
  endTime: string;
  price: string;
  electPrice: string;
  servicePrice: string;
}
const props = withDefaults(
  defineProps<{
    priceInfo: ChargePrice;
    showDiscount: boolean;
  }>(),
  {
    priceInfo: () => ({
      beginTime: '',
      endTime: '',
      price: '',
      electPrice: '',
      servicePrice: '',
    }),
    showDiscount: false,
  },
);
const hourTimeFormat = (val: number | string) => {
  return dayjs(val).format('HH:mm');
};
</script>
<style lang="scss" scoped>
.price-info {
  .current-label {
    background-image: linear-gradient(118deg, #ff790f 16%, #ff6b6d 49%, #ff48a1 81%);
  }
}
</style>
