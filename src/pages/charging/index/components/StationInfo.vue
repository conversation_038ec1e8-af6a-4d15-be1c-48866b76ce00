<template>
  <view class="common-card station-info">
    <view class="flex items-center">
      <view class="text-primary-36 text-primary">{{ baseInfo.stationName }}</view>
    </view>
    <view class="flex mt-6 items-center">
      <view
        class="flex-center text-white px-5rpx text-auxiliary leading-8 rounded-2"
        v-if="baseInfo.type && gunTypeInfo[baseInfo.type]"
        :style="{
          backgroundColor: gunTypeInfo[baseInfo.type].style.bgColor,
        }"
      >
        {{ gunTypeInfo[baseInfo.type].label }}
      </view>
      <view class="ml-2 text-secondary-26 text-text-secondary">
        {{ baseInfo.pileNo || '--' }}_{{ baseInfo.gunNo || '--' }}
      </view>
      <view
        v-if="baseInfo.gunRunStatus && gunStatusInfo[baseInfo.gunRunStatus]"
        class="ml-2 px-3 py-1 text-auxiliary rounded-2"
        :style="{
          backgroundColor: gunStatusInfo[baseInfo.gunRunStatus].style.bgColor,
          color: gunStatusInfo[baseInfo.gunRunStatus].style.color,
        }"
      >
        {{ gunStatusInfo[baseInfo.gunRunStatus].label }}
      </view>
    </view>
    <!-- <view class="my-6 slider-1px"></view>
    <view class="text-secondary-26 flex-between text-text-secondary">
      <view>服务费：{{ baseInfo.servicePrice || '--' }}元/度</view>
      <view>当前电价：{{ baseInfo.electPrice || '--' }}元/度</view>
    </view> -->
    <!-- <PriceInfo :priceInfo="baseInfo" /> -->
    <slot></slot>
  </view>
</template>
<script lang="ts" setup>
import { QrcodeScanRespVO } from '@/service';
import { gunStatusInfo, gunTypeInfo } from '../constants';

const props = withDefaults(
  defineProps<{
    baseInfo: QrcodeScanRespVO;
  }>(),
  {
    baseInfo: () => ({}),
  },
);
</script>
<style lang="scss" scoped></style>
