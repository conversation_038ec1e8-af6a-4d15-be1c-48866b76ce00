<template>
  <APopup
    title="选择企业号"
    center
    @close="emits('close')"
    :safeAreaInsetBottom="false"
    customStyle="background: #F5F5F5;"
    :bottom="true"
    bottomClass="bg-white !pb-safe-6"
  >
    <scroll-view
      :scroll-y="true"
      class="max-h-60vh"
      :show-scrollbar="false"
      :enhanced="true"
      @scrolltolower="emits('getEnterpriseList')"
    >
      <view class="mb-5" v-for="(item, index) in enterpriseList" :key="index" @click="select(item)">
        <!-- <EnterpriseCard
          :cardInfo="item"
          :showLeft="true"
          :borderColor="item.enterpriseId === current.enterpriseId ? '#F33813' : ''"
        /> -->
        <EnterpriseCardInfo
          :cardInfo="item"
          :showLeft="true"
          :selected="item.enterpriseId === current.enterpriseId"
          :showPayTag="true"
        />
      </view>
    </scroll-view>
    <!-- <view class="slider-1px w-750rpx ml--6"></view> -->
    <template #bottom>
      <AppButton type="brand" @click="confirmSelect">选择该企业</AppButton>
    </template>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { EnterpriseChargePriceVO } from '@/service';
import EnterpriseCardInfo from './EnterpriseCardInfo.vue';

const emits = defineEmits(['close', 'getEnterpriseList', 'selectEnterprise', 'close']);
const props = withDefaults(
  defineProps<{
    enterpriseList: EnterpriseChargePriceVO[];
    currentEnterprise: EnterpriseChargePriceVO;
  }>(),
  {
    enterpriseList: () => [],
    currentEnterprise: () => ({}),
  },
);

const current = ref(props.currentEnterprise);

const select = (item: EnterpriseChargePriceVO) => {
  current.value = item;
};
const confirmSelect = () => {
  emits('selectEnterprise', current.value);
};
</script>
