<template>
  <view class="common-card mt-6">
    <view class="flex-between items-center text-primary">
      <view class="text-primary-32">优惠券核销</view>
      <view
        class="flex-center text-secondary-24"
        @click="couponTotalNum && changeShowCouponListPop(true)"
      >
        <view v-if="couponTotalNum">
          共
          <text class="text-alert-color">{{ couponTotalNum || 0 }}</text>
          张可用
        </view>
        <view v-else class="text-text-sub">暂无可用优惠券</view>
        <OssImg :width="20" :height="20" className="ml-2" src="/images/arrow-right.png" />
      </view>
    </view>
    <view class="mt-6 text-status-tip text-secondary-24" v-if="couponList.length">
      将会按选取的优惠券核销策略自动匹配最优的优惠券
    </view>
    <view class="flex mt-6" v-if="couponList.length">
      <template v-for="item in couponTypeList">
        <view
          v-if="item"
          :class="[
            'px-6',
            'py-3',
            'mr-6',
            'rounded-4',
            'text-secondary-26',
            couponType === item.key
              ? 'bg-#FFF2F0 text-#F33813'
              : 'bg-black/[0.05] text-text-secondary',
          ]"
          :key="item.key"
          @click="emits('changeCouponType', item.key)"
        >
          {{ item?.label }}
        </view>
      </template>
    </view>
    <!-- <view
      class="mt-6 text-status-tip text-secondary-24"
      v-if="couponList.length && couponType && couponTypeTip[couponType]"
    >
      {{ couponTypeTip[couponType].tip }}
    </view> -->
    <CouponListPop
      v-if="showCouponListPop"
      :couponList="couponList"
      @close="changeShowCouponListPop(false)"
    />
  </view>
</template>
<script lang="ts" setup>
import OssImg from '@/components/OSSImg/index.vue';
import { PrizeQueryRespVO } from '@/service';
import { couponTypeList, couponTypeTip } from '../constants';
import { COUPON_TYPE } from '../interface';
import CouponListPop from './CouponListPop.vue';

const props = withDefaults(
  defineProps<{
    couponList: PrizeQueryRespVO[];
    couponType: COUPON_TYPE;
    couponTotalNum: number;
  }>(),
  {
    couponList: () => [],
    couponType: 'DISCOUNT_AMT',
    couponTotalNum: 0,
  },
);
const emits = defineEmits(['changeCouponType']);
const showCouponListPop = ref(false);
const changeShowCouponListPop = (value: boolean) => {
  showCouponListPop.value = value;
};
</script>
