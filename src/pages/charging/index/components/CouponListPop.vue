<template>
  <APopup title="可用优惠券" :border="true" @close="emits('close')" :safeAreaInsetBottom="true">
    <scroll-view
      :scroll-y="true"
      :show-scrollbar="false"
      :enhanced="true"
      class="pt-6 pb-2 max-h-70vh"
    >
      <view v-for="(item, index) in couponList" :key="item.couponCode || index" class="mb-4">
        <BCouponItem :item="item" type="couponChoose">
          <template #right></template>
        </BCouponItem>
      </view>
    </scroll-view>
    <view class="slider-1px w-750rpx ml--6"></view>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import BCouponItem from '@/components/BCouponItem/index.vue';
import { PrizeQueryRespVO } from '@/service';

const emits = defineEmits(['close']);
const props = withDefaults(
  defineProps<{
    couponList: PrizeQueryRespVO[];
  }>(),
  {
    couponList: () => [],
  },
);
</script>
