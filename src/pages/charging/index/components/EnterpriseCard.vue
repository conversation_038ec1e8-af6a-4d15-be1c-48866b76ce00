<template>
  <view :class="`common-card pos-relative ${className}`" :style="style">
    <view
      v-if="showPayTag"
      :class="[
        'pos-absolute h-11 w-32 flex-center top-0 right-0 text-24rpx tag',
        `${cardInfo.payMode}-tag`,
      ]"
    >
      {{ pageInfoText }}
    </view>
    <view class="flex items-center w-110 overflow-hidden">
      <OssImg
        className="shrink-0"
        :width="56"
        :height="56"
        :src="`/images/charging/${cardInfo.enterpriseType}.png`"
      />
      <view class="ml-1 text-primary-36 text-text-primary grow-1 truncate">
        {{ cardInfo.enterpriseName }}
      </view>
    </view>
    <view class="slider-1px my-6"></view>
    <view
      :class="{
        relative: true,
        'price-info-top': showDiscountTip,
      }"
    >
      <view v-if="showDiscountTip" class="discount-tip pos-absolute">企业专享价</view>
      <PriceInfo :priceInfo="cardInfo" :showDiscount="false" />
    </view>
    <view v-if="showLeft && cardTipText" class="mt-6 text-secondary-24 text-status-tip">
      {{ cardTipText }}
    </view>
  </view>
</template>
<script lang="ts" setup>
import OssImg from '@/components/OSSImg/index.vue';
import { EnterpriseChargePriceVO, StartChargeReqVO } from '@/service';
import PriceInfo from './PriceInfo.vue';

const props = withDefaults(
  defineProps<{
    cardInfo: EnterpriseChargePriceVO;
    showLeft: boolean;
    showPayTag: boolean;
    borderColor: string;
    className: string;
  }>(),
  {
    cardInfo: () => ({}),
    showLeft: true,
    showPayTag: false,
    borderColor: '',
    className: '',
  },
);

const cardTipText = computed(() => {
  const { payMode, dayLimit, dayUsedDown } = props.cardInfo;
  if (dayLimit && dayUsedDown) {
    return `本日剩余次数${Number(dayLimit) - Number(dayUsedDown)}/${dayLimit}次`;
  } else if (
    payMode === StartChargeReqVO.payMode.ENP_PREPAID ||
    payMode === StartChargeReqVO.payMode.ENP_CREDIT
  ) {
    return '本次充电费用由您的企业号代为支付';
  } else {
    return '';
  }
});

const pageInfoText = computed(() => {
  const { payMode } = props.cardInfo;
  if (!payMode) return '';
  if (payMode === StartChargeReqVO.payMode.ENP_PERSONAL) {
    return '个人支付';
  } else {
    return '企业支付';
  }
});

// 未配置企业优惠时不展示标签
const showDiscountTip = computed(() => {
  return props.cardInfo.elecPriceMode !== EnterpriseChargePriceVO.elecPriceMode.NONE;
});

const style = computed(() => {
  return props.borderColor ? `border: 2rpx solid ${props.borderColor};` : '';
});
</script>

<style lang="scss" scoped>
.tag {
  color: #fff;
  background: linear-gradient(119deg, #ff790f 15%, #ff6b6d 49%, #ff48a1 82%);
  border-radius: 0rpx 16rpx 0;
}

.enterprise-tag {
  color: #fff;
  background: linear-gradient(119deg, #ff790f 15%, #ff6b6d 49%, #ff48a1 82%);
}

.ENP_PERSONAL-tag {
  color: #f33813;
  background: rgb(243 56 19 / 10%);
}

.discount-tip {
  top: 0rpx;
  right: 0;
  z-index: 0;
  width: 124rpx;
  height: 59rpx;
  font-size: 20rpx;
  font-weight: normal;
  line-height: 32rpx;
  color: #fff;
  text-align: center;
  background: linear-gradient(128deg, #ff790f 13%, #ff6b6d 49%, #ff48a1 84%);
  border-radius: 16rpx 16rpx 0 0;
}

.price-info-top {
  padding-top: 14rpx;
}
</style>
