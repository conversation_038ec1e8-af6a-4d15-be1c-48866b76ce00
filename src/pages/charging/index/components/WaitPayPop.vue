<template>
  <APopup title="订单待付款提醒" @close="closeTip" :safeAreaInsetBottom="true">
    <view
      class="text-primary-28 font-400 w-361rpx mt-142rpx mb-134rpx mx-auto text-center text-#333336"
    >
      您有1笔充电订单待付款，请先完成付款后再来充电
    </view>
    <AppButton type="brand" @click="goPay">去支付</AppButton>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';

const props = withDefaults(
  defineProps<{
    orderId: string;
  }>(),
  {
    orderId: '',
  },
);
const closeTip = () => {
  if (getCurrentPages()?.length > 1) {
    uni.navigateBack();
  } else {
    uni.reLaunch({
      url: '/pages/home/<USER>',
    });
  }
};
const goPay = () => {
  uni.redirectTo({
    // url: `/pages/charging/endCharging/index?orderId=${props.orderId}`,
    url: `/pages/order/charging/orderDetail?orderId=${props.orderId}`,
  });
};
</script>
