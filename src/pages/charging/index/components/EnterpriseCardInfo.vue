<template>
  <view
    class="common-card relative of-hidden box-border"
    :class="`${selected ? 'bordered-2rpx-solid-brand-primary' : ''} ${className}`"
  >
    <view class="flex items-center">
      <view class="flex-1 flex items-center truncate">
        <OSSImg
          className="shrink-0"
          :width="56"
          :height="56"
          :src="`/images/charging/${cardInfo.enterpriseType}.png`"
        />
        <view class="text-primary-32 text-text-primary ml-1 truncate">
          {{ cardInfo.enterpriseName }}
        </view>
        <view
          class="ml-3 px-3 py-1 rounded-2 bg-[rgba(255,49,65,0.1)] border1 before:rounded-4 before:border-status-fail text-status-fail text-auxiliary"
          v-if="showDiscountTip"
        >
          企业优惠
        </view>
      </view>
      <slot name="headerRight"></slot>
    </view>
    <BPriceInfo className="mt-6" :priceList="[cardInfo]" :showType="false" />
    <view
      v-if="showLeft && cardInfo.dayLimit && cardInfo.dayUsedDown"
      class="mt-3 text-secondary-24 text-status-tip w-full text-right"
    >
      <text class="text-status-tip text-secondary-24">本日剩余次数</text>
      <text class="text-text-primary text-primary-36 ml-3">
        {{ Number(cardInfo.dayLimit) - Number(cardInfo.dayUsedDown) }}
      </text>
      <text class="text-text-sub text-secondary-26 ml-1">/{{ Number(cardInfo.dayLimit) }}</text>
    </view>
    <view
      v-if="showPayTag && pageInfoText"
      class="absolute right-0 top-0 text-secondary-24 px-17rpx py-6rpx rounded-es-6"
      :class="`${cardInfo.payMode === StartChargeReqVO.payMode.ENP_PERSONAL ? 'personal' : 'enterprise'}`"
    >
      {{ pageInfoText }}
    </view>
  </view>
</template>
<script lang="ts" setup>
import BPriceInfo from '@/components/BPriceInfo/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import type { EnterpriseChargePriceVO as EnterpriseChargePriceVOType } from '@/service';
import { EnterpriseChargePriceVO, StartChargeReqVO } from '@/service';

const props = withDefaults(
  defineProps<{
    cardInfo: EnterpriseChargePriceVOType;
    showLeft: boolean;
    showPayTag: boolean;
    borderColor: string;
    className: string;
    selected: boolean;
  }>(),
  {
    cardInfo: () => ({}),
    showLeft: true,
    showPayTag: false,
    borderColor: '',
    className: '',
    selected: false,
  },
);
// 未配置企业优惠时不展示标签
const showDiscountTip = computed(() => {
  return props.cardInfo.elecPriceMode !== EnterpriseChargePriceVO.elecPriceMode.NONE;
});
const pageInfoText = computed(() => {
  const { payMode } = props.cardInfo;
  if (!payMode) return '';
  if (payMode === StartChargeReqVO.payMode.ENP_PERSONAL) {
    return '个人支付';
  } else {
    return '企业支付';
  }
});
</script>
<style lang="scss" scoped>
.enterprise {
  @apply text-white;

  background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);
}

.personal {
  @apply text-brand-primary;

  background: linear-gradient(270deg, rgb(94 187 108 / 10%) 0%, rgb(117 208 131 / 10%) 100%);
}
</style>
