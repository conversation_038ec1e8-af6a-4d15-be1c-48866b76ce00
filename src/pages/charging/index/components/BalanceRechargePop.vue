<template>
  <APopup
    title="帐户充值"
    @close="emits('closeBalanceRechargePop')"
    :border="true"
    :safeAreaInsetBottom="true"
  >
    <view class="text-primary-32 text-text-primary mt-6">当前账户余额</view>
    <view class="flex items-end text-text-primary text-primary-32 mt-6 font-DIN font-bold">
      <text>¥</text>
      <text class="ml-1 text-12 leading-10">{{ accBalance || '--' }}</text>
    </view>
    <view class="slider-1px mt-6"></view>
    <PayBox
      :cols="3"
      :priceList="balancePriceList"
      :payAmout="payAmount"
      @changePayAmount="changePayAmount"
      placeholder="请输入充值金额"
    />
    <AppButton className="mt-10 mb-20" type="brand" :disabled="!payAmount" @click="balanceCharge">
      立即充值
    </AppButton>
  </APopup>
</template>
<script lang="ts" setup>
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { WalletService, UpOrderReqVO } from '@/service';
import payment from '@/utils/pay/unifyPay';
import { balancePriceList, productCodeEnum, sourceChannelEnum } from '../constants';
import PayBox from './PayBox.vue';

const props = withDefaults(
  defineProps<{
    accBalance: number;
  }>(),
  {
    accBalance: 0,
  },
);

const payAmount = ref(balancePriceList[0].num);
const changePayAmount = (val: string) => {
  payAmount.value = val;
};
const emits = defineEmits(['closeBalanceRechargePop']);
const balanceCharge = async () => {
  console.log(process.env.UNI_PLATFORM);
  const [err, res] = await WalletService.postWalletRecharge({
    // @ts-ignore
    sourceChannel: sourceChannelEnum[process.env.UNI_PLATFORM],
    bizType: UpOrderReqVO.bizType.RECHARGE,
    // @ts-ignore
    productCode: productCodeEnum[process.env.UNI_PLATFORM],
    orderType: UpOrderReqVO.orderType.RECHARGE_ORDER,
    orderAmount: Number(payAmount.value) * 100,
    callbackUrl: '',
    merchantDiscountAmount: 0,
  });
  console.error('--------', err, res);

  if (res) {
    payment.dispatchPay(res.data);
  }
};
</script>
