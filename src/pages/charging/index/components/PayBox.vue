<template>
  <view>
    <view
      class="mt-6 grid"
      :style="{
        gridTemplateColumns: `repeat(${cols}, 1fr)`,
        gridRowGap: '24rpx',
        gridColumnGap: '24rpx',
      }"
    >
      <view
        :class="[
          'h-17',
          'flex-center',
          'rounded-4',
          index === currentTab
            ? 'bg-brand-primary text-white'
            : 'bg-brand-light text-brand-primary',
        ]"
        v-for="(item, index) in priceList"
        :key="index"
        @click="handleClick(item, index)"
      >
        <view v-if="item.unit" class="text-secondary-24 leading-34rpx font-500 w-6 text-center">
          {{ item.unit }}
        </view>
        <view class="text-primary-32">{{ item.num }}</view>
      </view>
    </view>
    <view class="mt-6 h-118rpx w-full flex items-center" v-if="currentTab === priceList.length - 1">
      <view class="text-primary-32 text-primary shrink-0">¥</view>
      <input
        class="ml-2 grow h-full"
        type="number"
        :placeholder="
          minAmt && minAmtPlaceholder
            ? `${minAmtPlaceholder.replace('#amt#', `${minAmt}`)}`
            : placeholder
        "
        v-model="payAmountInner"
        placeholder-style="color:rgba(153, 153, 153, 0.898)"
        @input="valueChange"
      />
      <view
        v-if="payAmount && minAmt && Number(payAmount) < minAmt && minAmtTip"
        class="text-secondary-24 text-status-tip"
      >
        {{ minAmtTip }}
      </view>
    </view>
    <view class="slider-1px" v-if="currentTab === priceList.length - 1"></view>
  </view>
</template>
<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    cols?: number;
    payAmount: string;
    minAmt: number;
    priceList: {
      num: string;
      unit: string;
    }[];
    placeholder?: string;
    minAmtPlaceholder?: string; // 金额需#amt#占位
    minAmtTip?: string;
  }>(),
  {
    cols: 4,
    payAmount: '',
    minAmt: 0,
    priceList: () => [],
    placeholder: '请输入预充金额',
    minAmtPlaceholder: '本场站最低起充金额为#amt#元',
    minAmtTip: '低于场站最低起充金额',
  },
);
const emits = defineEmits(['changePayAmount']);
const currentTab = ref(0);
const payAmountInner = ref('');
const handleClick = (item: { num: string; unit: string }, index: number) => {
  currentTab.value = index;
  if (index < props.priceList.length - 1) {
    emits('changePayAmount', item.num);
  } else {
    payAmountInner.value = '';
    emits('changePayAmount', '');
  }
};
const valueChange = (e: { detail: { value: string } }) => {
  const priceValue = e.detail.value.replace(/^(0+)|[^\d]+/g, '');
  payAmountInner.value = priceValue;
  emits('changePayAmount', priceValue);
};
</script>
