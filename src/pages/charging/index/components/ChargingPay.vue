<template>
  <view class="common-card mt-6" :class="className">
    <radio-group @change="changePayMode">
      <!-- <label class="flex justify-between items-center" key="ACC_BALANCE">
        <view>
          <view class="flex items-center text-primary">
            <view class="text-primary-32">账户余额</view>
            <view class="ml-4">
              <text class="text-primary-32">¥</text>
              <text class="ml-1 text-10 leading-12 font-DIN font-bold">
                {{ accBalance || '--' }}
              </text>
            </view>
            <view class="ml-4 flex items-center" @click.stop="changeShowBalanceRechargePop(true)">
              <view class="text-primary-28 text-#0398FD">去充值</view>
              <OssImg :width="32" :height="32" src="/images/charging/arrow-right-blue.png" />
            </view>
          </view>
          <view
            class="mt-4 text-secondary-24 text-brand-primary"
            v-if="minStartAmt && accBalance < minStartAmt"
          >
            余额低于{{ minStartAmt }}元，请先充值后再使用
          </view>
          <view class="mt-4 text-secondary-24 text-status-tip" v-else>
            使用账户余额，充满或余额低于1元自动停止
          </view>
        </view>
        <radio
          class="radio-item"
          color="#F33813"
          value="ACC_BALANCE"
          :checked="payMode === 'ACC_BALANCE'"
        ></radio>
      </label> -->
      <!-- <label class="flex justify-between items-center" key="VIN">
        <view class="flex items-center">
          <view class="text-primary-32 text-text-primary">先享后付</view>
          <view class="text-primary-28 font-400 text-status-tip ml-4">已开通</view>
        </view>
        <radio class="radio-item" color="#56BE66" value="VIN" :checked="payMode === 'VIN'"></radio>
      </label>
      <view class="slider-1px my-6"></view> -->
      <label class="flex justify-between items-center" key="PREPAY">
        <view class="text-primary-32 text-text-primary">预充金额</view>
        <!-- <radio
          class="radio-item"
          color="#56BE66"
          value="PREPAY"
          :checked="payMode === StartChargeReqVO.payMode.PREPAY"
        ></radio> -->
      </label>
      <view
        v-if="payMode === StartChargeReqVO.payMode.PREPAY"
        class="mt-4 text-secondary-24 text-status-tip"
      >
        预先支付，充满自停，余额自动返回
      </view>
      <PayBox
        v-if="
          payMode === StartChargeReqVO.payMode.PREPAY ||
          payMode === StartChargeReqVO.payMode.ENP_PERSONAL
        "
        :priceList="prePriceList"
        :payAmout="payAmount"
        :minAmt="minStartAmt"
        @changePayAmount="changePayAmount"
      />
    </radio-group>
    <BalanceRechargePop
      :accBalance="accBalance"
      v-if="showBalanceRechargePop"
      @closeBalanceRechargePop="changeShowBalanceRechargePop(false)"
    />
  </view>
</template>
<script lang="ts" setup>
import { StartChargeReqVO } from '@/service/models/StartChargeReqVO';
import { ref } from 'vue';
import { prePriceList } from '../constants';
import BalanceRechargePop from './BalanceRechargePop.vue';
import PayBox from './PayBox.vue';

const props = withDefaults(
  defineProps<{
    payAmount: string;
    minStartAmt: number;
    accBalance: number;
    payMode: StartChargeReqVO.payMode;
    className?: string;
  }>(),
  {
    payAmount: '',
    minStartAmt: 0,
    accBalance: 0,
    payMode: StartChargeReqVO.payMode.PREPAY,
    className: '',
  },
);
const emits = defineEmits(['changePayAmount', 'changePayMode']);
const changePayAmount = (value: string) => {
  emits('changePayAmount', value);
};
const changePayMode = (e: any) => {
  emits('changePayMode', e.detail.value);
  if (e.detail.value === StartChargeReqVO.payMode.PREPAY) {
    changePayAmount(prePriceList[0].num);
  }
};
const showBalanceRechargePop = ref(false);
const changeShowBalanceRechargePop = (value: boolean) => {
  showBalanceRechargePop.value = value;
};
</script>
