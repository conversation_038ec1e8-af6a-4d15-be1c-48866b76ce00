<template>
  <view class="common-card mt-6">
    <view class="flex items-center">
      <view class="text-primary-32 text-text-primary">充电车辆</view>
      <!-- <view
        class="text-price-color text-auxiliary px-2 py-1 ml-3 relative after:(content-[''] absolute w-2/1 h-2/1 left-0 top-0 rounded-2 bordered-1px-solid-price-color scale-50 origin-top-left box-border)"
      >
        可享停车优惠
      </view> -->
    </view>
    <view class="mt-6">
      <BPlateInput
        ref="plateNoInputInnerRef"
        :plateNo="props.plateNo"
        @updatePlateNo="updatePlateNo"
        @updatePageScroll="updatePageScroll"
      />
    </view>
    <view class="flex-center mt-10">
      <view class="text-brand-primary text-primary-28 font-400" @click="handleSelectPlateNo">
        选择已有车辆
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import BPlateInput from '@/components/BPlateInput/index.vue';

const props = withDefaults(
  defineProps<{
    plateNo: string;
  }>(),
  {
    plateNo: '',
  },
);
const emits = defineEmits(['updatePlateNo', 'updatePageScroll']);
const plateNoInputInnerRef = ref(null);
const updatePlateNo = (plateNo: string) => {
  emits('updatePlateNo', plateNo);
};
const updatePageScroll = (scroll: boolean) => {
  emits('updatePageScroll', scroll);
};
const closeKeyboard = () => {
  // @ts-ignore
  plateNoInputInnerRef.value?.onKeyCancelClick?.();
};
const handleSelectPlateNo = () => {
  uni.navigateTo({
    url: '/pages/mine-sub/car/index/index?type=select',
  });
};
defineExpose({
  closeKeyboard,
});
</script>
