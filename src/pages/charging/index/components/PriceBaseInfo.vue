<template>
  <view class="flex justify-between">
    <template v-for="(item, index) in priceInfoList">
      <view v-if="item" :key="index" class="flex flex-col justify-between">
        <view class="flex items-center h-9">
          <view class="text-primary text-secondary-24">{{ item.label }}</view>
          <view
            class="py-1 px-2 text-price-color rounded-1 border-1rpx border-solid border-price-color text-auxiliary ml-1 bg-[rgba(255,104,53,0.05)]"
            v-if="showDiscount && item.discountKey && priceInfo[item.discountKey]"
          >
            {{ priceInfo[item.discountKey] }}折
          </view>
        </view>
        <view class="mt-3 text-text-sub text-secondary-24" v-if="item.key">
          <slot v-if="item.slotName" :name="item.slotName"></slot>
          <template v-else-if="priceInfo[item.key]">
            <text class="mr-1" v-if="item.unitBefore">{{ item.unitBefore }}</text>
            <text>{{ item.format ? item.format(priceInfo[item.key]) : priceInfo[item.key] }}</text>
          </template>
          <view v-else>--</view>
        </view>
      </view>
    </template>
  </view>
</template>
<script lang="ts" setup>
interface infoItem {
  label: string;
  key: string;
  unitBefore?: string;
  slotName?: string;
  discountKey?: string;
  format?: (value: any) => void;
}
const props = withDefaults(
  defineProps<{
    priceInfoList: infoItem[];
    priceInfo: { [key: string]: any };
    showDiscount?: boolean;
  }>(),
  {
    priceInfoList: () => [],
    priceInfo: () => ({}),
    showDiscount: false,
  },
);
</script>
