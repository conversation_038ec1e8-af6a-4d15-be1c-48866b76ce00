// export enum gunStatusEnum {
//   /** 待机 */
//   STANDBY = '0',
//   /** 充电中 */
//   CHARGING = '1',
//   /** 故障 */
//   FAULT = '2',
//   /** 插枪 */
//   INSERT = '3',
//   /** 充电完成 */
//   CHARGE_FINISH = '4',
//   /** 离线 */
//   OFFLINE = '5',

import { ProcessQueryRespVO } from '@/service';

// }
export const gunStatusInfo = {
  STANDBY: {
    label: '空闲',
    disable: false,
    style: {
      bgColor: '#D9F0FF',
      color: '#0398FD',
    },
  },
  CHARGING: {
    label: '充电中',
    disable: true,
    style: {
      bgColor: '#DEF4F6',
      color: '#1FB3BE',
    },
  },
  FAULT: {
    label: '故障',
    disable: true,
    style: {
      bgColor: '#FFE2E0',
      color: '#FF3B30',
    },
  },
  CONNECT: {
    label: '插枪',
    disable: false,
    style: {
      bgColor: '#FFEFDF',
      color: '#FE9327',
    },
  },
  COMPLETE: {
    label: '充电完成',
    disable: true,
    style: {
      bgColor: '#F3FFF8',
      color: '#2CCB8E',
    },
  },
  OFFLINE: {
    label: '离线',
    disable: true,
    style: {
      bgColor: '#EEEEEE',
      color: '#999999',
    },
  },
};
// export enum gunTypeEnum {
//   SLOW = '01',
//   FAST = '02',
// }
export const gunTypeInfo = {
  AC: {
    label: '慢',
    style: {
      bgColor: '#00BBC8',
    },
  },
  DC: {
    label: '快',
    style: {
      bgColor: '#3161FF',
    },
  },
  ACDC: {
    label: '混',
    style: {
      bgColor: '#0398FD',
    },
  },
};
export const prePriceList = [
  {
    num: '50',
    unit: '¥',
  },
  {
    num: '80',
    unit: '¥',
  },
  {
    num: '100',
    unit: '¥',
  },
  {
    num: '其他金额',
    unit: '',
  },
];
export const balancePriceList = [
  {
    num: '50',
    unit: '¥',
  },
  {
    num: '80',
    unit: '¥',
  },
  {
    num: '100',
    unit: '¥',
  },
  {
    num: '200',
    unit: '¥',
  },
  {
    num: '500',
    unit: '¥',
  },
  {
    num: '其他金额',
    unit: '',
  },
];
export const couponTypeList: {
  label: string;
  labelDetail: string;
  key: ProcessQueryRespVO.couponType;
  tip: string;
}[] = [
  {
    label: '金额优先',
    labelDetail: '抵扣金额优先',
    key: ProcessQueryRespVO.couponType.DISCOUNT_AMT,
    tip: '充电结束自动抵扣，系统会优先使用优惠金额最大的优惠券。',
  },
  {
    label: '时间优先',
    labelDetail: '到期时间优先',
    key: ProcessQueryRespVO.couponType.EXPIRE_TIME,
    tip: '充电结束自动扣，系统会优先使用到期时间最近的券。到期时间相同时，优先使用优惠金额最大的券。',
  },
  {
    label: '不使用',
    labelDetail: '不使用优惠券',
    key: ProcessQueryRespVO.couponType.NONE,
    tip: '本次充电不使用优惠券。',
  },
];
export const couponTypeTip = {
  DISCOUNT_AMT: {
    label: '抵扣金额优先',
    tip: '充电结束自动抵扣，系统会优先使用优惠金额最大的优惠券。',
  },
  EXPIRE_TIME: {
    label: '到期时间优先',
    tip: '充电结束自动扣，系统会优先使用到期时间最近的券。到期时间相同时，优先使用优惠金额最大的券。',
  },
  NONE: {
    label: '不使用优惠',
    tip: '本次充电不使用优惠券。',
  },
};
export const priceInfoList = [
  {
    label: '充电时段',
    key: 'time',
    unitBefore: '',
    slotName: 'time',
  },
  {
    label: '充电价(元/度)',
    key: 'price',
    slotName: 'price',
  },
  {
    label: '=',
    key: '',
    unitBefore: '',
  },
  {
    label: '电费',
    key: 'electPrice',
    unitBefore: '¥',
    discountKey: 'priceDiscount',
  },
  {
    label: '+',
    key: '',
    unitBefore: '',
  },
  {
    label: '服务费',
    key: 'servicePrice',
    unitBefore: '¥',
    discountKey: 'serviceDiscount',
  },
];
export enum sourceChannelEnum {
  'mp-alipay' = 'ZFB',
  'mp-weixin' = 'WX',
  'app' = 'APP',
}

export enum productCodeEnum {
  'mp-alipay' = 'ALIPAY_FIXED_QRCODE',
  'mp-weixin' = 'WECHAT_LITE',
}

export enum EChargeUserType {
  'enterprise' = '企业用户', // 企业用户
  'personal' = '个人用户', // 个人用户
}
export enum EUserType {
  'enterprise' = 'enterprise',
  'personal' = 'personal',
}
export type TUserType = keyof typeof EChargeUserType;

// 用户类型选择tab
export const chargeUserTabs = [
  {
    value: EUserType.enterprise,
    label: EChargeUserType.enterprise,
  },
  {
    value: EUserType.personal,
    label: EChargeUserType.personal,
  },
];
