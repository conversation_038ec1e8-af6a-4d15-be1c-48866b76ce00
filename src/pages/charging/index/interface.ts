export type GUN_TYPE = 'AC' | 'DC' | 'ACDC';
export type CHARGE_STATUS = 'NORMAL' | 'CHARGING' | 'WAIT_PAY';
export type GUN_STATUS = 'STANDBY' | 'CHARGING' | 'FAULT' | 'CONNECT' | 'COMPLETE' | 'OFFLINE';
export type COUPON_TYPE = 'DISCOUNT_AMT' | 'EXPIRE_TIME' | 'NONE';
export type PAY_MODE = 'ACC_BALANCE' | 'PREPAY';

export interface BaseInfo {
  /**
   * 账户余额
   */
  accBalance: string;
  /**
   * 当前充电状态，    NORMAL("NORMAL", "正常"),
   * CHARGING("CHARGING", "充电中"),
   * WAIT_PAY("WAIT_PAY", "待支付"),
   */
  chargeStatus: CHARGE_STATUS;
  /**
   * 电流
   */
  electric: number;
  /**
   * 充电枪id
   */
  gunId: string;
  /**
   * 充电枪名称
   */
  gunName: string;
  /**
   * 充电枪编号
   */
  gunNo: string;
  /**
   * 枪状态，    STANDBY("STANDBY", "待机"),
   * CHARGING("CHARGING", "充电中"),
   * FAULT("FAULT", "故障"),
   * CONNECT("CONNECT", "插枪"),
   * COMPLETE("COMPLETE", "充电完成"),
   * OFFLINE("OFFLINE", "离线"),
   */
  gunStatus: GUN_STATUS;
  /**
   * 最低启动金额（分）
   */
  minStartAmount: string;
  /**
   * 充电订单ID，本人充电中返回
   */
  orderId: string;
  /**
   * 充电桩ID
   */
  pileId: string;
  /**
   * 充电站名称
   */
  pileName: string;
  /**
   * 充电桩编号
   */
  pileNo: string;
  /**
   * 功率
   */
  power: number;
  /**
   * 当前计费信息
   */
  priceInfo: ChargePrice;
  /**
   * 站点地址
   */
  stationAddress: string;
  /**
   * 站点ID
   */
  stationId: string;
  /**
   * 站点名称
   */
  stationName: string;
  /**
   * 站点编号
   */
  stationNo: string;
  /**
   * 充电枪类型，    AC("AC", "交流"),
   * DC("DC", "直流"),
   * ACDC("ACDC", "交直流混合"),
   */
  type: GUN_TYPE;
  /**
   * 电压
   */
  voltage: number;
  [property: string]: any;
}

/**
 * 当前计费信息
 *
 * ChargePrice
 */
export interface ChargePrice {
  /**
   * 电价计费开始时间，hh:mm
   */
  beginTime: string;
  /**
   * 电费单价：电费
   */
  electPrice: string;
  /**
   * 电价计费结束时间，hh:mm
   */
  endTime: string;
  /**
   * 电费单价：总价
   */
  price: string;
  /**
   * 电费单价：服务费
   */
  servicePrice: string;
  /**
   * 枪类型，    AC("AC", "交流"),
   * DC("DC", "直流"),
   * ACDC("ACDC", "交直流混合"),
   */
  type: GUN_TYPE;
}
