<template>
  <view class="px-5 absolute top-47rpx right--4 bg-white rounded-4 card-pop">
    <view
      class="absolute right-5 top--3 wh-6 bg-white rounded-lt-1 rotate-45 card-pop-arrow"
    ></view>
    <view
      class="text-brand-primary text-secondary-26"
      v-for="(item, index) in optionList"
      :key="index"
      @click="item.clickFun"
    >
      <view class="flex items-center w-max py-6">
        <OssImg :width="36" :height="36" :src="`/images/home/<USER>" />
        <view class="ml-4" :class="item.class || ''">{{ item.label }}</view>
      </view>
      <view class="slider-1px" v-if="index !== optionList.length - 1"></view>
    </view>
  </view>
</template>
<script setup lang="ts">
import OssImg from '@/components/OSSImg/index.vue';
import { MyCarInfoVO } from '@/parkService';

const props = withDefaults(
  defineProps<{
    plateNo: string;
    plateColor: string;
    userPlateList: MyCarInfoVO[];
  }>(),
  {
    plateNo: '',
    plateColor: '',
    userPlateList: () => [],
  },
);
const emits = defineEmits(['goBind', 'resetInput', 'changePlate']);
const optionList = computed<
  {
    icon: string;
    label: string;
    class?: string;
    clickFun: () => void;
  }[]
>(() => [
  {
    icon: 'car-gray',
    label: props.plateNo,
    class: 'text-text-sub',
    clickFun: () => {},
  },
  ...props.userPlateList
    .filter(
      (item) =>
        item.plateNo !== props.plateNo ||
        (props.plateColor && item.plateColor !== props.plateColor),
    )
    .map((item) => ({
      icon: 'car-green',
      label: item.plateNo,
      clickFun: () => emits('changePlate', { plateNo: item.plateNo, plateColor: item.plateColor }),
    })),
  {
    icon: 'add-green',
    label: '选/绑车牌',
    clickFun: () => emits('goBind'),
  },
  {
    icon: 'reset-green',
    label: '重置输入',
    clickFun: () => emits('resetInput'),
  },
]);
</script>
<style lang="scss" scoped>
.card-pop {
  z-index: 999;
  box-shadow: 0 5rpx 32rpx 0 rgb(51 51 51 / 25%);

  &-arrow {
    // box-shadow: -5rpx -5rpx 4rpx 0rpx rgba(51, 51, 51, 0.25);
  }
}
</style>
