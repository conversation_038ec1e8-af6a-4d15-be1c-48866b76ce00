<template>
  <view class="flex-center mt-6">
    <view class="w-54rpx h-2rpx footer-slider rotate-180 transform-origin-c rounded-1rpx"></view>
    <view class="text-secondary-26 text-text-weak mx-8">{{ desc }}</view>
    <view class="w-54rpx h-2rpx footer-slider rounded-1rpx"></view>
  </view>
</template>
<script setup lang="ts">
const desc = import.meta.env.VITE_APP_DESC;
</script>
<style lang="scss" scoped>
.footer-slider {
  background-image: linear-gradient(270deg, rgb(204 204 204 / 0%) 0%, #ccc 100%);
}
</style>
