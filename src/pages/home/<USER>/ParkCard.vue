<template>
  <view class="rounded-6 home-park-info-card mt-4">
    <view class="px-6 py-4 box-border flex justify-between items-center text-primary-32 text-white">
      <view class="flex items-center">
        <view>车牌缴费</view>
        <view
          class="ml-4 bg-status-tip rounded-101rpx text-white flex items-center px-3 border1 before:rounded-202rpx before:border-white"
          v-if="unPayCount"
          @click="goUnPay"
        >
          <view class="text-secondary-24">{{ unPayCount }}笔欠费</view>
          <OssImg :width="24" :height="24" src="/images/home/<USER>" />
        </view>
      </view>
      <view class="text-secondary-26">
        <view
          v-if="plateNo"
          class="flex items-center relative"
          @click.stop="showCardPop = !showCardPop"
        >
          <view class="mr-1">{{ plateNo }}</view>
          <view
            class="flex px-10rpx py-2rpx box-border border1 before:rounded-4 before:border-white text-white text-auxiliary ml-2 mr-1"
            v-if="plateIsCert"
          >
            已认证
          </view>
          <OssImg
            className="rotate-90"
            :width="28"
            :height="28"
            src="/images/home/<USER>"
          />
          <ParkCardPop
            v-if="showCardPop"
            :plate-no="plateNo"
            :userPlateList="userPlateList"
            :plateColor="plateColor"
            @changePlate="changePlate"
            @go-bind="goBind"
            @reset-input="resetInput"
          />
        </view>
        <view v-else class="flex items-center" @click="goBind">
          <view>选/绑车牌</view>
          <OssImg :width="28" :height="28" src="/images/home/<USER>" />
        </view>
      </view>
    </view>
    <view class="bg-white rounded-5 p-6 box-border">
      <ParkStatusInfo
        ref="parkStatusInfoRef"
        v-if="plateNo"
        :plate-no="plateNo"
        :plateColor="plateColor"
        :userPlateList="userPlateList"
        @updateUnPayCount="updateUnPayCount"
        @getPlateColor="getPlateColor"
        @resetInput="resetInput"
      />
      <template v-else>
        <BPlateInput
          ref="plateNoInputInnerRef"
          v-if="inputType === 'common'"
          :plate-no="plateNoInput"
          @updatePlateNo="updatePlateNoInput"
        />
        <view
          v-else
          class="rounded-2 bordered-2rpx-solid-brand-primary h-90rpx box-border px-5 flex items-center"
        >
          <wd-input
            v-model="plateNoInput"
            no-border
            customClass="w-full h-full flex items-center !bg-transparent"
            placeholder="请输入您的港澳车牌"
            placeholderClass="text-text-weak text-primary-32 font-400"
          />
        </view>
        <AppButton className="mt-8 w-413rpx h-77rpx mx-auto" @click="confirmPlateNo">
          查询
        </AppButton>
        <view
          class="text-secondary-26 text-center mt-6 text-brand-primary flex-center w-max mx-auto"
          @click="changeInputType"
        >
          <text class="mr-1">切换{{ inputType === 'common' ? '特殊' : '普通' }}车牌</text>
          <OssImg :width="28" :height="28" src="/images/home/<USER>" />
        </view>
      </template>
    </view>
    <BPlateColorChooseModal
      v-if="showPlateColorChooseModal"
      @changeShow="(val: boolean) => (showPlateColorChooseModal = val)"
      @confirmColor="confirmPlateColor"
    />
  </view>
</template>
<script setup lang="ts">
import AppButton from '@/components/AppButton/index.vue';
import BPlateColorChooseModal from '@/components/BPlateColorChooseModal/index.vue';
import BPlateInput from '@/components/BPlateInput/index.vue';
import OssImg from '@/components/OSSImg/index.vue';
import { checkLogin } from '@/hooks/useCheckLogin';
import { MyCarInfoVO, MyVehicleService, ParkingOrderService } from '@/parkService';
import { useUserStore } from '@/store';
import { showSingleToast } from '@/utils/jsapi';
import { isPlate } from '@/utils/validates';
import { storeToRefs } from 'pinia';
import ParkCardPop from './ParkCardPop.vue';
import ParkStatusInfo from './ParkStatusInfo.vue';

const parkStatusInfoRef = ref();
const plateNoInputInnerRef = ref();
const { isLogin } = storeToRefs(useUserStore());

const plateNo = ref('');
const plateColor = ref('');
const showPlateColorChooseModal = ref(false);
const plateNoInput = ref('粤');
const inputType = ref<'common' | 'special'>('common');
const showCardPop = ref(false);
const userPlateList = ref<MyCarInfoVO[]>([]);
const unPayCount = ref(0);
const isReseting = ref(false);
const initPlateNo = async (onlyRefreshList = false) => {
  const [err, res] = await MyVehicleService.getMycarList();
  if (res?.data?.length) {
    if (!onlyRefreshList) {
      plateNo.value = res.data[0].plateNo || '';
      plateColor.value = res.data[0].plateColor || '';
    }
    userPlateList.value = res.data;
  } else {
    userPlateList.value = [];
  }
};
const plateIsCert = computed(() => {
  const item = userPlateList.value.filter(
    (item) =>
      item.plateNo === plateNo.value && (!plateColor.value || item.plateColor === plateColor.value),
  );
  return item?.[0]?.status === 'CERT_SUCC';
});
const goUnPay = () => {
  if (unPayCount.value > 1) {
    uni.navigateTo({
      url: `/pages/order/park/pendingPayment?plateNo=${plateNo.value}&plateColor=${plateColor.value}`,
    });
  } else if (unPayCount.value === 1) {
    uni.navigateTo({
      url: `/pages/order/park/pendingPayDetail?plateNo=${plateNo.value}&plateColor=${plateColor.value}&status=PAY_WAIT`,
    });
  }
};
watch(
  () => isLogin.value,
  (val) => {
    if (val) {
      initPlateNo();
    }
  },
  { immediate: true },
);
const updateUnPayCount = (count: number) => {
  unPayCount.value = count;
};
watch(
  () => plateNo.value,
  (val) => {
    if (val) {
      // getUnpayBillCount();
      isReseting.value = false;
    } else {
      unPayCount.value = 0;
    }
  },
  { immediate: true },
);
const refresh = () => {
  if (!plateNo.value) {
    if (isLogin.value) {
      initPlateNo(isReseting.value);
    }
  } else {
    if (isLogin.value) {
      initPlateNo(true);
    }
    parkStatusInfoRef.value?.refresh?.();
  }
};
const goBind = async () => {
  await checkLogin();
  uni.navigateTo({
    url: '/pages/mine-sub/car/index/index?type=select',
  });
};
const updatePlateNoInput = (value: string) => {
  plateNoInput.value = value;
};
const getPlateColor = async () => {
  const [err, res] = await ParkingOrderService.postOrderListColorsByPlate(
    plateNoInput.value || plateNo.value,
  );
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  if (res?.data?.length && res?.data?.length > 1) {
    showPlateColorChooseModal.value = true;
    return;
  }
  if (res?.data?.length === 1) {
    plateColor.value = res.data[0] || '';
  }
  if (plateNoInput.value) {
    // 兼容刷新场景
    plateNo.value = plateNoInput.value;
    plateNoInput.value = '';
  } else {
    parkStatusInfoRef.value?.getPlateBill?.();
    parkStatusInfoRef.value?.getUnpayBillCount?.();
  }
};
const confirmPlateNo = async () => {
  clearPop();
  if (!plateNoInput.value) {
    showSingleToast('请输入车牌号');
    return;
  }
  if (inputType.value === 'common' && !isPlate(plateNoInput.value)) {
    showSingleToast('车牌号输入有误');
    return;
  }
  await getPlateColor();
};
const confirmPlateColor = (color: string) => {
  plateColor.value = color;
  if (plateNoInput.value) {
    // 兼容刷新场景
    plateNo.value = plateNoInput.value;
    plateNoInput.value = '';
  }
};
const changeInputType = () => {
  inputType.value = inputType.value === 'common' ? 'special' : 'common';
  plateNoInput.value = inputType.value === 'common' ? '粤' : '';
};
const resetInput = () => {
  plateNo.value = '';
  plateColor.value = '';
  inputType.value = 'common';
  plateNoInput.value = '粤';
  isReseting.value = true;
};
const clearPop = () => {
  showCardPop.value = false;
  plateNoInputInnerRef.value?.onKeyCancelClick?.();
};
const changePlate = (plateInfo: { plateNo: string; plateColor: string }) => {
  plateNo.value = plateInfo.plateNo;
  plateColor.value = plateInfo.plateColor;
};
defineExpose({
  clearPop,
  plateNo,
  plateColor,
  refresh,
});
</script>
<style lang="scss" scoped>
.home-park-info-card {
  background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);

  :deep(input) {
    @apply text-primary-32 font-400 text-text-primary;
  }
}
</style>
