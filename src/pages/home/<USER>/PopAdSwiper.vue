<template>
  <wd-popup v-model="show" custom-class="!bg-transparent">
    <view class="w-150 h-213">
      <view class="w-150 h-175 rounded-12 translate-y-0 relative" :class="className">
        <swiper class="w-full h-full rounded-12 of-hidden" autoplay circular @change="swiperChange">
          <swiper-item v-for="(item, index) in list" :key="index" @click="handleClick(item)">
            <image class="w-full h-full rounded-12" mode="aspectFill" :src="item.pic" />
          </swiper-item>
        </swiper>
        <view class="absolute bottom--13 left-1/2 -translate-x-1/2 flex" v-if="list.length > 1">
          <view
            class="h-3 rounded-3 not-first:ml-4"
            :class="index === current ? 'w-10 bg-white' : 'w-3 bg-[rgba(255,255,255,0.3)]'"
            v-for="(item, index) in list.length"
            :key="index"
          ></view>
        </view>
      </view>
      <view class="mt-23 flex-center">
        <OSSImg
          :width="60"
          :height="60"
          src="/images/common/popup/ad-close.png"
          @click="() => emits('close')"
        />
      </view>
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { BannerElementVO } from '@/service';
import { goToLinkType } from '@/utils/route';
import OSSImg from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    show: boolean;
    list: BannerElementVO[];
    className?: string;
  }>(),
  {
    show: false,
    list: () => [],
    className: '',
  },
);
const emits = defineEmits(['close']);
const current = ref(0);

const swiperChange = (e: any) => {
  current.value = e.detail.current;
};
const handleClick = (item: BannerElementVO) => {
  goToLinkType(item.linkType, item.linkUrl);
};
</script>
