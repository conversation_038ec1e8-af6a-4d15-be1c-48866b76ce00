<template>
  <view
    class="px-5 py-3 border1 before:rounded-58 before:border-brand-primary flex-center w-max mt-6 mx-auto box-border"
    @click="emits('click')"
  >
    <view class="text-brand-primary text-secondary-24">查看更多场站</view>
    <OSSImg :width="24" :height="24" className="ml-2" src="/images/home/<USER>" />
  </view>
</template>
<script setup lang="ts">
import OSSImg from '@/components/OSSImg/index.vue';

const emits = defineEmits(['click']);
</script>
