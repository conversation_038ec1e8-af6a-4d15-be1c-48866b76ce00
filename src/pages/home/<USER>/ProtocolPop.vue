<template>
  <APopup
    v-model:show="show"
    :safe-area-inset-bottom="true"
    :title="popUpContent.title"
    :closable="false"
    :center="true"
    titleClass="text-primary-32"
    :bottom="true"
    :bottom-border="false"
  >
    <view class="text-secondary-26 text-text-primary-lighter pb-10">
      <text>{{ popUpContent.before }}</text>
      <text
        v-for="(item, index) in popUpContent.protocols"
        :key="item.code"
        class="text-brand-primary"
        @click="onProtocolClick(item)"
      >
        《{{ item.name }}》
        <text
          v-if="popUpContent.protocols && index < popUpContent.protocols.length - 1"
          :key="item.code"
          class="text-text-primary-lighter"
        >
          和
        </text>
      </text>
      <text>{{ popUpContent.after }}</text>
    </view>
    <template #bottom>
      <view class="flex-between">
        <AppButton className="w-339rpx" type="brand" plain @click="handleRefuseClick">
          拒绝
        </AppButton>
        <AppButton className="w-339rpx" @click="handleAgreeClick(popUpContent.protocols || [])">
          同意
        </AppButton>
      </view>
    </template>
  </APopup>
</template>
<script setup lang="ts">
import APopup from '@/components/APopup/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import { useProtocol } from '@/hooks/useProtocol';
import { ProtocolQueryRespVO } from '@/service';
import { isEmpty } from '@/utils/is';
import { onProtocolItemClick } from '@/utils/protocolClick';

enum EShowType {
  'FIRST',
  'UPDATE',
}
const PROTOCOL_CONTENT_FOR_POPUP = {
  all: {
    title: '用户隐私保护提示',
    content:
      '为了更好地保障你的个人权益，在你使用本产品服务前，请你仔细阅读#protocolList#，并确认了解我们的服务内容。我们将在条款说明的范围内收集使用你的个人信息。如果你不同意，我们将不能继续为你提供服务。\n若你点击“同意”按钮，表示你已理解并同意以上协议全部内容。',
  },
  user_agreement: {
    title: '用户协议更新',
    content:
      '为了确保您在本平台的功能的顺利使用，根据最新法律法规要求，我们更新了用户协议，请您仔细阅读并确认#protocolList#，我们将严格按照协议内容为您提供更好的服务，感谢您的信任。',
  },
  privacy_policy: {
    title: '隐私政策更新',
    content:
      '为了加强对您个人信息的保护，根据最新法律法规要求，我们更新了隐私政策，请您仔细阅读并确认#protocolList#，我们将严格按照政策内容使用和保护您的个人信息，为您提供更好的服务，感谢您的信任。',
  },
};
const emits = defineEmits(['finished']);
const show = ref(false);
const finishProtocol = ref(false);
const showType = ref<EShowType>(EShowType.FIRST);
const currentProtocolIndex = ref(0);
const protocolList = ref<ProtocolQueryRespVO[]>([]);
const { protocolList: protocolListSource, fetchProtocolList } = useProtocol([
  'user_agreement',
  'privacy_policy',
]);
// onMounted(() => {
//   // #ifdef MP-ALIPAY
//   // 支付宝小程序需要单独调用 组件内的onShow事件不触发
//   fetchProtocolList();
//   // #endif
// });
watch(
  () => show.value,
  (val) => {
    if (val) {
      uni.hideTabBar();
    } else {
      const timer = setTimeout(() => {
        uni.showTabBar();
        clearTimeout(timer);
      }, 300);
    }
  },
);
/**
 * 获取协议内容，判断缓存版本号提示是否需要进行更新
 */
const initProtocol = async () => {
  if (protocolListSource.value.length === 0) {
    finishProtocol.value = true;
    return;
  }
  // 获取本地缓存的协议列表
  const localProtocolMap = uni.getStorageSync('localProtocolList') || {};
  if (isEmpty(localProtocolMap)) {
    // 本地没有缓存协议记录，展示全部协议
    showType.value = EShowType.FIRST;
    protocolList.value = protocolListSource.value;
  } else {
    // 版本号判断
    const protocolListUpdate = protocolListSource.value.filter((item) => {
      const localItem = localProtocolMap[item.code];
      return !localItem || (item?.version && item?.version > localItem);
    });
    showType.value = EShowType.UPDATE;
    protocolList.value = protocolListUpdate;
  }
  if (protocolList.value.length > 0) {
    show.value = true;
  } else {
    finishProtocol.value = true;
  }
};
watch(
  () => protocolListSource.value,
  () => {
    initProtocol();
  },
);
const popUpContent = computed(() => {
  if (protocolList.value.length === 0) return {};
  if (showType.value === EShowType.FIRST) {
    // 首次弹窗
    const { title, content } = PROTOCOL_CONTENT_FOR_POPUP.all;
    const [before, after] = content.split('#protocolList#');
    return {
      title,
      before,
      after,
      protocols: protocolList.value,
    };
  } else {
    // 更新弹窗
    const currentProtocol = protocolList.value[currentProtocolIndex.value];
    const { title, content } =
      PROTOCOL_CONTENT_FOR_POPUP[currentProtocol.code as keyof typeof PROTOCOL_CONTENT_FOR_POPUP];
    const [before, after] = content.split('#protocolList#');
    return {
      title,
      before,
      after,
      protocols: [currentProtocol],
    };
  }
});
// 拒绝，退出小程序
const handleRefuseClick = () => {
  uni.exitMiniProgram();
};
// 将各个协议的版本号写入缓存
const updateLocalProtocol = (protocolList: any[]) => {
  const localProtocolMap = uni.getStorageSync('localProtocolList') || {};
  protocolList.forEach((item) => {
    localProtocolMap[item.code] = item.version;
  });
  uni.setStorageSync('localProtocolList', localProtocolMap);
};
// 同意，更新本地协议版本并关闭弹窗
const handleAgreeClick = (currentProtocolList: any[]) => {
  // @ts-ignore
  show.value = false;
  updateLocalProtocol(currentProtocolList);
  if (showType.value === EShowType.FIRST) {
    const timer = setTimeout(() => {
      finishProtocol.value = true;
      clearTimeout(timer);
    }, 300);
  } else if (currentProtocolIndex.value + 1 < protocolList.value.length) {
    const timer = setTimeout(() => {
      currentProtocolIndex.value++;
      show.value = true;
      clearTimeout(timer);
    }, 500);
  } else {
    const timer = setTimeout(() => {
      finishProtocol.value = true;
      clearTimeout(timer);
    }, 300);
  }
};
const onProtocolClick = (item: any) => {
  onProtocolItemClick(item);
};
defineExpose({
  finishProtocol,
});
</script>
