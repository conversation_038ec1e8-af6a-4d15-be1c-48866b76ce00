<template>
  <view v-if="userLoc">
    <ATabs
      className="mt-6 !pl-0 !relative"
      :list="tabs"
      :current="nearbyTypeIndex"
      @click="changeType"
    />
    <ATabs
      className="!pl-0 mt-6 !relative"
      :list="innerTabs[tabs[nearbyTypeIndex].value]"
      :current="nearbyInnerTypeIndex"
      @click="changeInnerType"
      type="capsule"
    />
    <template v-if="tabs[nearbyTypeIndex].value === 'park' && nearbyParkList.length">
      <template v-for="item in nearbyParkList" :key="item.parkId">
        <BParkInfoCard :park-info="item" />
      </template>
      <MoreStation @click="seeMore" />
    </template>
    <template v-else-if="tabs[nearbyTypeIndex].value === 'charge' && nearbyChargeList.length">
      <template v-for="item in nearbyChargeList" :key="item.stationId">
        <BChargeInfoCard :charge-info="item" />
      </template>
      <MoreStation @click="seeMore" />
    </template>
    <template v-else-if="tabs[nearbyTypeIndex].value === 'bike'">
      <BikeMap
        :mapCenterLoc="mapCenterLoc"
        :bikeList="bikeList"
        @updateMapCenterLoc="updateMapCenterLoc"
      />
    </template>
    <template
      v-else-if="
        tabs[nearbyTypeIndex].value === 'collect' &&
        ((collectParkList.length && innerTabs.collect[nearbyInnerTypeIndex].value !== 'charge') ||
          (collectChargeList.length && innerTabs.collect[nearbyInnerTypeIndex].value === 'charge'))
      "
    >
      <template
        v-if="
          innerTabs.collect[nearbyInnerTypeIndex].value === 'charge' && collectChargeList.length
        "
      >
        <template v-for="item in collectChargeList" :key="item.stationId">
          <BChargeInfoCard :charge-info="item" :show-collect-remark="true" />
        </template>
      </template>
      <template
        v-else-if="
          innerTabs.collect[nearbyInnerTypeIndex].value !== 'charge' && collectParkList.length
        "
      >
        <template v-for="item in collectParkList" :key="item.parkId">
          <BParkInfoCard :park-info="item" :show-collect-remark="true" />
        </template>
      </template>
    </template>
    <view class="pt-6" v-else>
      <AEmpty v-if="isQuerying" type="loading" />
      <AEmpty v-else :button="true" buttonText="刷新" @buttonClick="refresh" />
    </view>
  </view>
</template>
<script setup lang="ts">
import AEmpty from '@/components/AEmpty/AEmpty.vue';
import ATabs from '@/components/ATabs/index.vue';
import BChargeInfoCard from '@/components/BChargeInfoCard/index.vue';
import BParkInfoCard from '@/components/BParkInfoCard/index.vue';
import {
  BaseParkVO,
  ParkingLotInquiryService,
  SearchParkingResponse,
  UserCollectionYardService,
} from '@/parkService';
import {
  BicycleService,
  StationCollectService,
  StationCollectVO,
  StationInfoVO,
  StationListQueryReqVO,
  StationService,
} from '@/service';
import BikeMap from './BikeMap.vue';
import MoreStation from './MoreStation.vue';

const props = withDefaults(
  defineProps<{
    userLoc: { latitude: number; longitude: number } | null;
  }>(),
  { userLoc: null },
);
const bikeList = ref<any[]>([]);
const mapCenterLoc = ref();
const nearbyTypeIndex = ref(0);
const nearbyInnerTypeIndex = ref(0);
const nearbyParkList = ref<BaseParkVO[]>([]);
const nearbyChargeList = ref<StationInfoVO[]>([]);
const collectParkList = ref<SearchParkingResponse[]>([]);
const collectChargeList = ref<StationCollectVO[]>([]);
const isQuerying = ref(false);
const showCharge = !import.meta.env.VITE_HIDE_CHARGE;
const tabs = ref([
  { label: '附近停车场', value: 'park' },
  ...(showCharge ? [{ label: '附近充电站', value: 'charge' }] : []),
  // { label: '附近单车', value: 'bike' },
  { label: '收藏场站', value: 'collect' },
]);
const innerTabs = ref({
  park: [
    { label: '路边', value: 1 },
    { label: '停车场', value: 0 },
  ],
  charge: [
    { label: '快充', value: 'DC' },
    { label: '慢充', value: 'AC' },
  ],
  bike: [
    { label: '全部', value: null },
    { label: '青桔', value: 'qingju' },
    { label: '美团', value: 'meituan' },
    { label: '喵走', value: 'miaozou' },
    { label: '小黄驴', value: 'xhlbike' },
    { label: '人民出行', value: 'peoplego' },
    { label: '哈啰', value: 'haluo' },
  ],
  collect: [
    { label: '路边停车', value: 1 },
    { label: '停车场停车', value: 0 },
    ...(showCharge ? [{ label: '充电', value: 'charge' }] : []),
  ],
});
const seeMore = () => {
  uni.setStorageSync('nearbyType', tabs.value[nearbyTypeIndex.value].value);
  uni.switchTab({
    url: `/pages/nearby/index`,
  });
};
const queryNearbyPark = async () => {
  nearbyParkList.value = [];
  const [err, res] = await ParkingLotInquiryService.postParkNearList({
    latitude: props.userLoc?.latitude,
    longitude: props.userLoc?.longitude,
    // queryDistance: 3,
    pageSize: 3,
    baseParkType: innerTabs.value.park[nearbyInnerTypeIndex.value].value,
  });
  nearbyParkList.value = res?.data || [];
};
const queryNearbyCharge = async () => {
  nearbyChargeList.value = [];
  const [err, res] = await StationService.postStationList({
    lat: props.userLoc?.latitude || null,
    lon: props.userLoc?.longitude || null,
    // distance: 3,
    pageSize: 2,
    chargeType: innerTabs.value.charge[nearbyInnerTypeIndex.value]
      .value as StationListQueryReqVO.chargeType,
  });
  nearbyChargeList.value = res?.data?.dataList || [];
};
const queryNearbyBike = async () => {
  const [err, res] = await BicycleService.postBikeQueryNearBikeInfo({
    bicycleType: innerTabs.value.bike[nearbyInnerTypeIndex.value].value,
    latitude: mapCenterLoc.value.latitude,
    longitude: mapCenterLoc.value.longitude,
  });
  bikeList.value = res?.data?.bikePointsInfoList || [];
};
const updateMapCenterLoc = (loc: { latitude: number; longitude: number }) => {
  mapCenterLoc.value = loc;
  queryNearbyBike();
};
const queryCollectPark = async () => {
  collectParkList.value = [];
  const [err, res] = await UserCollectionYardService.postCollectionGetUserNearCollection({
    latitude: props.userLoc?.latitude,
    longitude: props.userLoc?.longitude,
    // queryDistance: 3,
    pageSize: 2,
    baseParkType: innerTabs.value.collect[nearbyInnerTypeIndex.value].value as number,
  });
  collectParkList.value = res?.data?.dataList || [];
};
const queryCollectCharge = async () => {
  collectChargeList.value = [];
  const [err, res] = await StationCollectService.postCollectStationList({
    lat: props.userLoc?.latitude,
    lon: props.userLoc?.longitude,
    pageSize: 2,
  });
  collectChargeList.value = res?.data?.dataList || [];
};
const getNearbyList = async () => {
  isQuerying.value = true;
  switch (tabs.value[nearbyTypeIndex.value].value) {
    case 'park':
      await queryNearbyPark();
      break;
    case 'charge':
      await queryNearbyCharge();
      break;
    case 'bike':
      if (!mapCenterLoc.value) {
        mapCenterLoc.value = props.userLoc;
      }
      await queryNearbyBike();
      break;
    case 'collect':
      if (innerTabs.value.collect[nearbyInnerTypeIndex.value].value === 'charge') {
        await queryCollectCharge();
      } else {
        await queryCollectPark();
      }
      break;
    default:
      break;
  }
  isQuerying.value = false;
};
watch(
  () => [nearbyTypeIndex.value, nearbyInnerTypeIndex.value, props.userLoc],
  () => {
    if (props.userLoc) {
      getNearbyList();
    }
  },
  { immediate: true },
);
const refresh = () => {
  getNearbyList();
};
const changeType = (index: number) => {
  nearbyTypeIndex.value = index;
  nearbyInnerTypeIndex.value = 0;
};
const changeInnerType = (index: number) => {
  nearbyInnerTypeIndex.value = index;
};
defineExpose({
  refresh,
});
</script>
<style lang="scss" scoped></style>
