import BaseMarker from '@/utils/mapMarker/baseMarker';

const URL_PREFIX = import.meta.env.VITE_ALI_OSS_URL_PREFIX;

const bikeMarkerCfg = {
  meituan: '/images/home/<USER>/mt.png', // 美团
  haluo: '/images/home/<USER>/hl.png', // 哈啰
  qingju: '/images/home/<USER>/qj.png', // 青桔
  peoplego: '/images/home/<USER>/rmcx.png', // 人民出行
  xhlbike: '/images/home/<USER>/xhl.png', // 小黄驴
  miaozou: '/images/home/<USER>/mz.png', // 喵走
};
export default class BikeMarker extends BaseMarker {
  constructor(options: any) {
    super(options);
    this.iconPath = `${URL_PREFIX}${bikeMarkerCfg[options.type as keyof typeof bikeMarkerCfg]}`;
    this.width = 24;
    this.height = 24;
  }
}
