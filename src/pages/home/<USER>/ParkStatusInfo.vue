<template>
  <view class="flex flex-col items-center min-h-50">
    <view class="w-full h-50 flex-center" v-if="isLoading">
      <wd-loading :color="Colors['brand-primary']" />
    </view>
    <template v-else-if="!parkBillInfo.parkId">
      <view class="text-primary-32 text-text-primary mt-7">未查询到入场信息</view>
      <AppButton class-name="mt-46rpx w-413rpx h-77rpx" @click="refresh">刷新</AppButton>
      <view class="text-secondary-26 text-brand-primary mt-6" @click="emits('resetInput')">
        重新输入
      </view>
    </template>
    <template v-else-if="parkBillInfo.isPrepay === 1 && parkBillInfo.freeLeave">
      <view class="flex items-center">
        <OSSImg :width="40" :height="40" src="/images/home/<USER>" />
        <view class="text-secondary-26 font-500 text-brand-primary ml-3">待离场</view>
      </view>
      <view class="text-primary-32 text-text-primary mt-10">
        已缴费成功，请{{
          parkBillInfo.freeDepartureTime ? `在${parkBillInfo.freeDepartureTime}分钟内` : ''
        }}尽快离场
      </view>
    </template>
    <template v-else>
      <view class="flex-center w-full">
        <view class="text-primary-32 text-text-primary truncate mr-10">
          {{ showParkName ? parkBillInfo.parkName || '汕头智行停车场' : '汕头智行停车场' }}
        </view>
        <view class="flex-center shrink-0">
          <!-- <view class="wh-10 relative flex-center">
            <view class="loading-box"></view>
            <OSSImg :width="12" :height="16" src="/images/home/<USER>" />
          </view> -->
          <OSSImg :width="40" :height="40" src="/images/home/<USER>" />
          <view class="text-brand-primary ml-4 text-secondary-26 font-500">停车中</view>
        </view>
      </view>
      <view class="text-secondary-26 mt-4">
        <text class="text-text-sub">进场时间：</text>
        <text class="text-text-secondary ml-3">{{ parkBillInfo.parkTime || '--' }}</text>
      </view>
      <AppButton class-name="mt-6 w-413rpx h-77rpx" @click="goPay">去缴费</AppButton>
    </template>
    <AMessageBox
      v-model:show="showConfirm"
      type="confirm"
      title="提示"
      msg="非绑定车牌，是否继续缴费"
      @confirm="navigatePay"
    />
  </view>
</template>
<script setup lang="ts">
import AMessageBox from '@/components/AMessageBox/index.vue';
import AppButton from '@/components/AppButton/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';
import { MyCarInfoVO, MyVehicleService, ParkBillVO, ParkingOrderService } from '@/parkService';
import { Colors } from '../../../../config/colors';

const props = withDefaults(
  defineProps<{
    plateNo: string;
    plateColor: string;
    userPlateList: MyCarInfoVO[];
  }>(),
  {
    plateNo: '',
    plateColor: '',
    userPlateList: () => [],
  },
);
const emits = defineEmits(['updateUnPayCount', 'getPlateColor', 'resetInput']);
const isLoading = ref(false);
const parkBillInfo = ref<ParkBillVO>({});
const showConfirm = ref(false);
const showParkName = ref(false);
const getPlateBill = async () => {
  isLoading.value = true;
  const [err, res] = await ParkingOrderService.postOrderQueryParkBill({
    plateNo: props.plateNo,
    plateColor: props.plateColor || undefined,
    queryFeeMethod: 1,
  });
  if (res?.data) {
    if (res.data.parkId) {
      getPlatePrivacy();
    }
    parkBillInfo.value = res.data;
    // emits('updateUnPayCount', res.data.unPayCount || 0);
  } else {
    parkBillInfo.value = {};
    // emits('updateUnPayCount', 0);
  }
  isLoading.value = false;
};
const getUnpayBillCount = async () => {
  const [err, res] = await ParkingOrderService.postOrderQueryUnPayBills({
    plateNo: props.plateNo,
    plateColor: props.plateColor || undefined,
  });
  if (res?.data) {
    emits('updateUnPayCount', res.data.unPayCount || 0);
  } else {
    emits('updateUnPayCount', 0);
  }
};
const getPlatePrivacy = async () => {
  showParkName.value = false;
  const [err, res] = await MyVehicleService.getMycarPrivacyGet(props.plateNo, props.plateColor);
  if (res?.data) {
    showParkName.value = res.data.isCurrentCert || res.data.showParkName || false;
  }
};
watch(
  () => [props.plateNo, props.plateColor],
  () => {
    if (props.plateNo) {
      getPlateBill();
      getUnpayBillCount();
    }
  },
  {
    immediate: true,
  },
);
const isBindPlate = computed(() => {
  return !!props.userPlateList.filter(
    (item) => item.plateNo === props.plateNo && item.plateColor === props.plateColor,
  ).length;
});
const goPay = () => {
  if (isBindPlate.value) {
    navigatePay();
  } else {
    showConfirm.value = true;
  }
};
const navigatePay = () => {
  uni.navigateTo({
    url: `/pages/order/park/pendingPayDetail?plateNo=${props.plateNo}&status=PARKING`,
  });
};
const refresh = () => {
  if (props.plateColor) {
    getPlateBill();
    getUnpayBillCount();
  } else {
    emits('getPlateColor');
  }
};
defineExpose({
  refresh,
  getPlateBill,
  getUnpayBillCount,
});
</script>
<style lang="scss" scoped></style>
