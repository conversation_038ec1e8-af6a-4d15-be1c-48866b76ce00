import { isAliPay } from '@/utils/isPlatform';

const ready = '';
const ping = 'ping';

class WsRequest {
  private status: any;

  private lockReconnect: boolean; // 避免重复连接

  private url: string;

  private isConnected: boolean;

  private closeByUser: boolean;

  private timeout: number; // 多少秒执行检测

  private timeoutTask: any; // 检测服务器端是否还活着

  private reconnectTimeoutTask: any; // 重连之后多久再次重连

  private socket: any;

  private messageCallback: any;

  private reconnectCallback: any; // 重连回调函数

  constructor(callback: any) {
    this.status = null;
    this.lockReconnect = false;
    this.url = '';
    this.isConnected = false;
    this.closeByUser = false;
    this.timeout = 5000;
    this.timeoutTask = null;
    this.reconnectTimeoutTask = null;
    this.socket = null;
    this.messageCallback = null;
    this.reconnectCallback = callback; // 添加回调函数参数
  }

  public init(url: string): void {
    this.url = url;
    if (this.url) {
      if (isAliPay) {
        // 支付宝小程序环境下的 WebSocket 连接
        // 确保之前的连接已关闭
        // 移除所有事件监听
        my.offSocketMessage();
        my.offSocketOpen();
        my.offSocketError();
        my.offSocketClose();
        my.closeSocket();
        setTimeout(() => {
          my.connectSocket({
            url: this.url,
            complete: () => {
              // 清除重连定时器
              if (this.reconnectTimeoutTask) {
                clearTimeout(this.reconnectTimeoutTask);
              }
              // 开启检测
              this.reset();
            },
            fail: (result: any) => {
              console.log('llll', result);
            },
            success(res: any) {},
          });
          my.onSocketOpen((res: any) => {
            this.isConnected = true;
            // 告知服务器准备就绪
            this.send(ping);
          });
          my.onSocketMessage((event: any) => {
            const { data } = event;
            if (data !== 'pong') {
              const sendInfo = JSON.parse(data);
              // 真正的消息类型
              this.onmessage(sendInfo);
            }
            if (this.closeByUser) {
              return;
            }
            // 接受任何消息都说明当前连接是正常的
            this.reset();
          });
          my.onSocketError((res: any) => {
            if (!this.closeByUser) {
              this.isConnected = false;
              this.reconnect();
            }
          });
          my.onSocketClose((res: any) => {
            this.isConnected = false;
            this.reconnect();
            // // 调用重连成功的回调函数
            // if (typeof this.reconnectCallback === 'function') {
            //   this.reconnectCallback();
            // }
          });
        }, 3000);
      } else {
        // 确保之前的连接已关闭
        try {
          if (this.isConnected) {
            uni.closeSocket();
          }
        } catch (e) {
          console.log('关闭之前连接失败', e);
        }
        setTimeout(() => {
          this.socket = uni.connectSocket({
            url: this.url,
            complete: () => {
              // 清除重连定时器
              if (this.reconnectTimeoutTask) {
                clearTimeout(this.reconnectTimeoutTask);
              }
              // 开启检测
              this.reset();
            },
            fail: (result: any) => {},
            success(res: any) {},
          });
          this.socket.onOpen((res: any) => {
            // 告知服务器准备就绪
            this.send(ping);
          });
          this.socket.onMessage((event: any) => {
            const { data } = event;
            if (data !== 'pong') {
              const sendInfo = JSON.parse(data);
              // 真正的消息类型
              this.onmessage(sendInfo);
            }
            if (this.closeByUser) {
              return;
            }
            // 接受任何消息都说明当前连接是正常的
            this.reset();
          });
          this.socket.onClose((res: any) => {
            if (!this.closeByUser) {
              this.isConnected = false;
              this.reconnect();
            }
          });
          this.socket.onError((res: any) => {
            this.isConnected = false;
            this.reconnect();
            // // 调用重连成功的回调函数
            // if (typeof this.reconnectCallback === 'function') {
            //   this.reconnectCallback();
            // }
          });
        }, 500);
      }
    } else {
      console.error('请提供链接地址...');
    }
  }

  /**

   发送状态
   @param value */
  public send(value: any): void {
    if (isAliPay) {
      my.sendSocketMessage({
        data: value,
        success: () => {},
        fail: () => {
          this.reset();
          this.reconnect();
        },
      });
    } else if (this.socket) {
      uni.sendSocketMessage({
        data: value,
        success: () => {},
        fail: () => {
          this.reset();
          this.reconnect();
        },
      });
    }
  }

  /**

   发送真正的聊天消息
   @param message 消息 */
  public sendMessage(message: any): void {
    this.send(JSON.stringify(message));
  }

  /**

   收到消息，这里会被重写覆盖
   @param message 消息 */
  public onmessage(message: any): void {}

  /**

   reset和start方法主要用来控制心跳的定时。 */
  private reset(): void {
    if (this.timeoutTask) {
      clearTimeout(this.timeoutTask);
    }
    this.timeoutTask = setTimeout(() => {
      this.send(ping);
    }, this.timeout);
  }

  // 重连
  private reconnect(): void {
    if (this.lockReconnect) {
      return;
    }

    this.lockReconnect = true;

    this.reconnectTimeoutTask = setTimeout(() => {
      if (!this.isConnected) {
        this.close();
      }
      this.init(this.url);
      this.lockReconnect = false;
    }, 2000);
    // 调用重连成功的回调函数
    if (typeof this.reconnectCallback === 'function') {
      this.reconnectCallback();
    }
  }

  // 手动关闭
  public close(): void {
    if (this.timeoutTask) {
      clearTimeout(this.timeoutTask);
    }
    if (this.reconnectTimeoutTask) {
      clearTimeout(this.reconnectTimeoutTask);
    }
    this.closeByUser = true;

    if (isAliPay) {
      // 移除所有事件监听
      my.offSocketMessage();
      my.offSocketOpen();
      my.offSocketError();
      my.offSocketClose();
      my.closeSocket();
    } else if (this.socket) {
      uni.closeSocket();
    }
  }
}

export default WsRequest;
