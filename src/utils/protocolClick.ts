import { ProtocolRelatedService } from '@/service';
import { useRichTextStore } from '@/store';
import { showSingleToast } from './jsapi';
import { goTo } from './route';

const { setContent } = useRichTextStore();
const getProtocolDetail = async (protocolId: string) => {
  uni.showLoading();
  const [err, res] = await ProtocolRelatedService.getProtocolDetail(protocolId);
  uni.hideLoading();
  if (res?.data) {
    return {
      success: true,
      content: res.data.content || '',
    };
  } else {
    return {
      success: false,
    };
  }
};
export const onProtocolItemClick = async (item: any) => {
  if (item.type === 'TEXT') {
    let detail = item.content;
    if (!detail) {
      const { success, content } = await getProtocolDetail(item.id);
      if (success) {
        detail = content;
      } else {
        showSingleToast('获取协议详情失败');
        return;
      }
    }
    setContent({
      TEXT: {
        content: detail,
        name: item.name,
      },
    });
    uni.navigateTo({
      url: `/pages/common/richText/index?key=TEXT`,
    });
  } else if (item.type === 'LINK_URL') {
    goTo('/pages/common/webview/index', {
      title: item.name,
      url: item.linkUrl,
    });
  }
};
