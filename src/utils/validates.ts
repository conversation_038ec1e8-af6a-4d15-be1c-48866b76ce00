// 邮箱正则校验
export const isValidEmail = (email: string) => {
  const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailReg.test(email);
};

// 税号正则校验
export const isValidTax = (tax: string) => {
  // const taxReg = /^(?=.*[A-Z])(?=.*\d)[A-Z\d]{6,20}$/;  // (只能同时包含大写字母和数字,6-20位)
  const taxReg = /^.{6,20}$/; // 6-20位
  return taxReg.test(tax);
};
export const isPlate = (str: string) => {
  // const plateReg = /(^[A-HJ-NOP-Z0-9\u4e00-\u9fa5]{3,9}$)/;
  const plateReg =
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
  return plateReg.test(str);
};
