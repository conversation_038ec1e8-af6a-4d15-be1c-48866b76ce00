let timer: NodeJS.Timeout | null = null;

export function debounce(fn: () => void, delay: number = 500): void {
  if (timer !== null) {
    clearTimeout(timer);
    timer = null;
  }
  timer = setTimeout(fn, delay);
}

let isThrottled = false;

export function throttle(fn: () => void, delay: number = 500): () => void {
  return function () {
    if (!isThrottled) {
      fn();
      isThrottled = true;
      setTimeout(() => {
        isThrottled = false;
      }, delay);
    }
  };
}
