import { useUserStore } from '@/store';
import { FLAG, OS_TYPE } from '@/utils/platform';
import axios from 'axios';

let initRefreshTokenFlag = false;
let refreshing = false;
let refreshPromiseList: any = [];

const customAdapter = (config: any) => {
  return new Promise((resolve, reject) => {
    const { baseURL, url, headers, data, params } = config;
    const requestUrl = url.indexOf('http') > -1 ? url : `${baseURL || ''}${url}`;
    const uniConfig = {
      ...config,
      url: requestUrl,
      header: {
        ...config.headers,
      },
    };
    delete uniConfig.responseType;
    if (data || params) {
      try {
        uniConfig.data = JSON.parse(data || params);
      } catch (e) {
        uniConfig.data = data || params;
      }
    }
    uni.request({
      ...uniConfig,
      success(res: any) {
        resolve({
          ...res,
          status: res.statusCode,
          statusText: res.errMsg,
          config,
        });
      },
      fail(err: any) {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject({
          ...err,
          status: err.statusCode,
          statusText: err.errMsg,
          config,
        });
      },
    });
  });
};
const instance = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 300000,
  headers: {
    'os-type': OS_TYPE,
    'device-id': '11111',
    'Content-Type': 'application/json;charset=UTF-8',
    flag: FLAG,
  },
  // @ts-ignore
  adapter: customAdapter,
});
instance.interceptors.request.use(
  async (config) => {
    if (config.configExtra?.forceSilent) {
      return config;
    }
    const userStore = useUserStore();
    // @ts-ignore
    config.headers = {
      ...config.headers,
      Authorization: userStore.token || '',
    };
    if (config.url === '/app-server/api/user/refresh/token' || initRefreshTokenFlag) {
      console.log('initRefreshTokenFlag', initRefreshTokenFlag);
    } else if (refreshing) {
      // 正在刷新的场景
      if (config.url !== '/app-server/api/user/refresh/token') {
        return new Promise((resolve) => {
          refreshPromiseList.push((token: string) => {
            config.headers.Authorization = token;
            resolve(config);
          });
        });
      }
    } else {
      // 开始刷新
      refreshing = true;
      userStore.refreshToken().then(() => {
        initRefreshTokenFlag = true;
        console.log('刷新token');
        refreshing = false;
        // @ts-ignore
        refreshPromiseList.forEach((resolve: any) => {
          console.log('刷新token之后 foreach', userStore.token);
          resolve(userStore.token || '');
        });
        refreshPromiseList = [];
      });
      if (config.url !== '/user-center/user/refresh/token') {
        return new Promise((resolve) => {
          refreshPromiseList.push((token: string) => {
            config.headers.Authorization = token;
            resolve(config);
          });
        });
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);
instance.interceptors.response.use(
  // @ts-ignore
  (response) => {
    const { code, subMsg, msg, subCode } = response.data;
    if (code !== '10000') {
      if (subCode === 'token-time-expires' && useUserStore().userInfo.refreshToken) {
        const { config } = response;
        if (refreshing) {
          return new Promise((resolve) => {
            refreshPromiseList.push((token: string) => {
              config.headers.Authorization = token;
              resolve(instance(config));
            });
          });
        } else {
          const userStore = useUserStore();
          refreshing = true;
          userStore.refreshToken().then(() => {
            initRefreshTokenFlag = true;
            console.log('刷新token');
            refreshing = false;
            // @ts-ignore
            refreshPromiseList.forEach((resolve: any) => {
              console.log('刷新token之后 foreach', userStore.token);
              resolve(userStore.token || '');
            });
            refreshPromiseList = [];
          });
          // 把当前失败的接口也存入（！！！存入执行比上方遍历执行早）
          return new Promise((resolve) => {
            refreshPromiseList.push((token: string) => {
              config.headers.Authorization = token;
              resolve(instance(config));
            });
          });
        }
      }
      // showSingleToast(subMsg || msg || '系统异常');
      return Promise.resolve([
        {
          errorType: 'businessError',
          ...response.data,
        },
        undefined,
      ]);
    }
    return Promise.resolve([undefined, response.data]);
  },
  (error: any) => {
    return Promise.resolve([
      {
        errorType: 'businessError',
        ...error,
      },
      undefined,
    ]);
  },
);

export { instance };
