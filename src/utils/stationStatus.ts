export type SpaceStatus = 'FREE' | 'NORMAL' | 'DANGER' | 'FULL';
export const spaceStatusMap: Record<string, SpaceStatus> = {
  '03': 'FULL',
  '02': 'FREE',
  '01': 'NORMAL',
  '00': 'DANGER',
};
export const getProgressStatus = (spaceStatus: string): SpaceStatus => {
  return spaceStatusMap[spaceStatus as keyof typeof spaceStatusMap];
};
export const statusMap = {
  FREE: {
    color: '#00C82F',
    text: '空闲',
  },
  NORMAL: {
    color: '#FF9500',
    text: '一般',
  },
  DANGER: {
    color: '#FF3141',
    text: '紧张',
  },
  FULL: {
    color: '#FF3141',
    text: '已满',
  },
};
