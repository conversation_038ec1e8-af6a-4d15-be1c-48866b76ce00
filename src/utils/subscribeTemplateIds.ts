// 当前运行平台
// 维护运行环境类型
export enum EPlatform {
  'mp-alipay' = 'mp-alipay',
  'mp-weixin' = 'mp-weixin',
}
export type TPlatform = keyof typeof EPlatform;
// eslint-disable-next-line
export const UNI_PLATFORM = process.env.UNI_PLATFORM as TPlatform;

const subscribeTemplateIds = {
  [EPlatform['mp-alipay']]: {
    charging: [
      'd328a90c5c1047fd95e77f2428d45064', // 充电开始
      'd3f60cb1f82b4d28af08e7cb35d7c51b', // 充电结束
      '38460727bafb4bc98fb0584c25b9a0f1', // 充电异常结束
    ],
    parkWarning: ['5f843bac67324d8eab1ea23b0442b930'], // 泊位预警
    accountCancel: ['d86d17e1b37f4bffb3e0e6b4308cbb41'], // 账户注销
    plateParkPay: ['8b4f956d833a4f08a4e0fb0cca00a975'], // 有牌车缴费出场
    noPlateParkPay: ['f957230130bd4a01b7f8718fd72becd5'], // 无牌车缴费出场
    carCertifyOrAppeal: ['6f56a8164e014e0d913c8f1547f0bae7'], // 车辆认证/申诉
  },
  [EPlatform['mp-weixin']]: {
    charging: [
      '-8268yJJnmGzI63f6OH8hdwbSUhCm172vOzxAlsdEDU', // 充电开始
      'dHg41TZBh3vEDH77GP1EZiToLlcoW7o_GBER5rWhCgs', // 充电结束
      'HwrNCKeZKqAiWxM3By0NjExgkeZVtKaahFzfJmgrg3A', // 充电异常结束
    ],
    parkWarning: ['PIemahu0YSm6Ag1ItnvPmDB4RpcHlppa5EqMgBi1pu8'], // 泊位预警
    accountCancel: ['uXCCqjQditRFg2QKFNlzZdnfc_3gs2AiZj0JB3bXWCo'], // 账户注销
    plateParkPay: ['GtYo5IVCqERMdauwMvWmQAgvzh1w1LnI4TKuqLA8Uw0'], // 有牌车缴费出场
    noPlateParkPay: ['SgBCjSQajZihj-r8CvUasOF4dwkWlQLN6siCO2ifLKc'], // 无牌车缴费出场
    carCertifyOrAppeal: ['DzxbF9_WyvrtUS_SDMw7VTS196bNB0nYCep4ksCUF5Y'], // 车辆认证/申诉
  },
};

export const TEMPLATE_IDS = subscribeTemplateIds[UNI_PLATFORM as EPlatform];
