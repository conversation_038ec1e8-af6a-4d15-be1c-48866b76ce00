import dayjs from 'dayjs';

const timeToDayjs = (time: string) => {
  const [hour, minute] = time.split(':');
  return dayjs().hour(Number(hour)).minute(Number(minute));
};
// 目前站点列表使用，后续其他地方使用，可能还需要完善，
// 目前解析的是 00:00 格式的时间
export const inTimeBetween = (
  startTime: string | null | undefined,
  endTime: string | null | undefined,
  type: 'hour' | 'minute' | 'day' = 'hour',
) => {
  if (!startTime || !endTime) return false;
  return dayjs().isBetween(timeToDayjs(startTime), timeToDayjs(endTime), type, '[)');
};

export const sleep = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

/**
 * @description: 相对时间处理 x天x小时x分
 * @param {number} baseDate
 * @param {string} date
 * @return {*}
 */
export function diffDateString(date: number): string {
  const baseDate = Date.now();
  const diffSecond = Math.floor((date - baseDate) / 1000);
  if (diffSecond > 0) {
    const totalH = Math.floor(diffSecond / 3600);
    const d = Math.floor(totalH / 24);
    const h = totalH % 24;
    const m = Math.ceil((diffSecond % 3600) / 60);
    return `${d ? `${d}天` : ''} ${h ? `${h}小时` : ''}${m ? `${m}分钟` : ''} `;
  } else {
    return '';
  }
}
