export default class BaseMarker {
  public id: number;

  public longitude: number;

  public latitude: number;

  public iconPath: string;

  public width = 40;

  public height = 40;

  public iconLayout: any;

  public customCallout: any;

  public extraInfo: any;

  constructor(options: any) {
    this.id = options.id;
    this.longitude = options.longitude;
    this.latitude = options.latitude;
    this.iconPath = options.iconPath;
    this.width = options.width || this.width;
    this.height = options.height || this.height;
    this.iconLayout = options.iconLayout || undefined;
    this.customCallout = options.customCallout || undefined;
    this.extraInfo = { ...options };
  }

  valueOf() {
    return {
      id: this.id,
      latitude: this.latitude,
      longitude: this.longitude,
      width: this.width,
      height: this.height,
      iconPath: this.iconPath,
      iconLayout: this.iconLayout,
      customCallout: this.customCallout,
    };
  }
}
