export function lock(fn: (args: any) => Promise<any>): (...args: any[]) => Promise<any>;
// eslint-disable-next-line no-redeclare
export function lock(
  time: number,
  fn?: (args: any) => Promise<any>,
): (...args: any[]) => Promise<any>;
// eslint-disable-next-line no-redeclare
export function lock(fnOrTime: any, fnOrTime2?: any) {
  let time: number;
  let fn: () => Promise<any>;

  if (typeof fnOrTime === 'function') {
    fn = fnOrTime;
    time = fnOrTime2 || 200;
  } else {
    time = fnOrTime;
    fn = fnOrTime2 || (() => Promise.resolve());
  }

  let lock = false;
  return async (...args: any[]) => {
    if (lock) return;
    lock = true;
    try {
      const data = await Promise.all([
        await fn(...args),
        new Promise((resolve) => {
          setTimeout(resolve, time);
        }),
      ]);
      lock = false;
      return data[0];
    } catch (e) {
      lock = false;
    }
  };
}
