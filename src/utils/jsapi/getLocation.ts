import { DEFAULTGEO } from '@/config/DEFAULTGEO';
import { Colors } from '../../../config/colors';
import { showModal } from './showModal';

let canGetLoc = true;
export const updateCanGetLoc = (val: boolean) => {
  canGetLoc = val;
};
export const getLocation = async (
  denyTipType: 'TOAST' | 'MODAL' | '' = '',
): Promise<{
  latitude: number;
  longitude: number;
  isDefault: boolean;
}> => {
  return new Promise((resolve) => {
    if (canGetLoc) {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          resolve({ ...res, isDefault: false });
        },
        fail: (error) => {
          updateCanGetLoc(false);
          resolve(DEFAULTGEO);
          if (denyTipType) {
            locationErrHandle(error.errMsg, denyTipType);
          }
        },
      });
    } else {
      resolve(DEFAULTGEO);
    }
  });
};

export function locationErrHandle(errMsg: string, denyTipType: 'TOAST' | 'MODAL') {
  // 获取定位信息异常
  // 支付宝环境拒绝授权，返回errMsg：getLocation:fail 用户不允许授权，授权弹窗每次都会再次弹出，不用特殊处理
  // 微信环境拒绝授权，返回errMsg：getLocation:fail auth deny，授权弹窗仅弹出一次，需要此处判断非首次拒绝再次弹出弹窗提示开启
  if (errMsg === 'getLocation:fail auth deny') {
    if (denyTipType === 'MODAL') {
      const notFirstGetLocation = uni.getStorageSync('NOT_FIRST_GET_LOCATION');
      uni.setStorage({ key: 'NOT_FIRST_GET_LOCATION', data: true });
      if (notFirstGetLocation) {
        // storageKeyNotFirstGetLocation存在时，说明不是首次进入小程序，弹出第二次位置授权弹窗
        showModal({
          title: '',
          content: '检测到您没打开获取地理位置权限，是否去设置打开？',
          showCancel: true,
          confirmColor: Colors['brand-primary'],
          cancelColor: Colors['text-sub'],
          success: (res: any) => {
            if (res.confirm) {
              uni.openSetting({
                success: (res) => {
                  console.log(res);
                  if (res.authSetting['scope.userLocation']) {
                    // 开启了
                    console.error('开启了');
                    uni.$emit('openLocationSuccess');
                  }
                },
              });
            }
          },
        });
      }
    } else {
      // 未授权
      uni.showToast({
        title: '无法定位，请重新获取位置信息',
        icon: 'none',
      });
    }
  }
  // 支付宝环境下，手机定位未开启 getLocation:fail 请确认定位相关权限已开启
  // 微信环境下，手机定位未开启 getLocation:fail system permission denied
  if (
    errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF' ||
    errMsg === 'getLocation:fail system permission denied' ||
    errMsg === 'getLocation:fail 请确认定位相关权限已开启'
  ) {
    showModal({
      title: '',
      content: '请开启手机定位服务',
      showCancel: false,
    });
  }
}
