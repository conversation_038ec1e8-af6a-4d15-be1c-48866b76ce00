import { Colors } from '../../../config/colors';

export const showModal = (config: ModalConfig = {}) => {
  return new Promise<boolean>((resolve) => {
    if (process.env.UNI_PLATFORM === 'mp-alipay') {
      my.showModal({
        title: '提示',
        confirmColor: Colors['brand-primary'],
        cancelColor: Colors['text-sub'],
        success: ({ confirm }: { confirm: boolean }) => {
          if (confirm) {
            // 用户点击了确定按钮
            resolve(true);
          } else {
            // 用户点击了取消按钮
            resolve(false);
          }
        },
        fail: () => {
          resolve(false);
        },
        ...config,
      });
    } else {
      uni.showModal({
        title: '提示',
        confirmColor: Colors['brand-primary'],
        cancelColor: Colors['text-sub'],
        success: async ({ confirm }) => {
          if (confirm) {
            // 用户点击了确定按钮
            resolve(true);
          } else {
            // 用户点击了取消按钮
            resolve(false);
          }
        },
        fail: () => {
          resolve(false);
        },
        ...config,
      });
    }
  });
};
