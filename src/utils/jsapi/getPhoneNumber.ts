/**
 * @description: 小程序获取手机号授权 解析返回参数
 * @param {any} e Button 组件的点击事件
 * @return {*}
 */
export async function getAlipayPhoneNumber(e: any) {
  let phoneEncrypt;
  return new Promise((resolve) => {
    my.getPhoneNumber({
      success: async (res: any) => {
        const encryptedDataStr = res.response || '{}';
        try {
          phoneEncrypt = JSON.parse(encryptedDataStr);
          resolve({ mobile: phoneEncrypt });
        } catch (error) {
          console.log('encryptedData JSON.stringify error');
          resolve(undefined);
        }
      },
      fail: () => {
        resolve(undefined);
      },
    });
  });
}
export async function getWechatPhoneNumber(e: any) {
  const { code } = e.detail;
  const phoneEncrypt = e.detail.encryptedData;
  const params = { code, phoneEncrypt };
  return params;
}
