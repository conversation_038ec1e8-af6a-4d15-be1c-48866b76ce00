/**
 * @description: 解析小程序url
 * @param {string} url
 * @return {*}
 */
export function parseQueryString(url: string): Record<string, string> {
  const regUrl = /^[^\?]+\?([\w\W]+)$/;
  const regPara = /([^&=]+)=([\w\W]*?)(&|$)/g;
  const arrUrl = regUrl.exec(url);
  if (!arrUrl || !arrUrl[1]) {
    return {};
  }
  const strPara = arrUrl[1];
  const ret: Record<string, string> = {};
  let match;
  // eslint-disable-next-line no-cond-assign
  for (; (match = regPara.exec(strPara)); ) {
    const key = match[1].trim();
    ret[key] = match[2];
  }
  return ret;
}
