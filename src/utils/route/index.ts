import { BannerElementVO } from '@/service';
import { parseQueryString } from './utils';

type Nullable<T> = T | null | undefined;
const isTabBar = (url: string) => {
  const tabBarPages = [
    '/pages/nearby/index',
    '/pages/home/<USER>',
    '/pages/travelServices/index',
    '/pages/order/index',
    '/pages/mine/index',
  ];
  const newUrl = url.split('?')[0];
  if (newUrl.startsWith('/')) {
    newUrl.slice(1);
  }
  return tabBarPages.includes(newUrl);
};

type Primitive = string | number | boolean | undefined | null;
type PrimitiveValues = { [key: string]: Primitive };

/**
 * 获取链接上的指定参数
 * @param {string} key 获取参数的key
 * @param {string} [paramString] 获取参数的链接，默认当前location.href
 * @param {boolean} [matchFirst] 参数获取是否匹配第一个，默认匹配最后一个
 * @returns String
 */
export function getQueryString(key: string, paramString?: string, matchFirst?: boolean): string {
  // let paramStr = paramString || window.location.search.substr(1) || window.location.hash.split("?")[1];
  const paramStr = paramString || window.location.href;
  const regExp = new RegExp(`(^|\\?|&)?${key}=([^&|#]*)(\\s|&|#|$)`, 'g');
  const matchArr = paramStr.match(regExp) || [];
  const matchLen = matchArr.length;
  if (matchLen > 0) {
    return regExp.test(matchArr[matchFirst ? 0 : matchLen - 1]) ? RegExp.$2 : '';
  } else {
    return '';
  }
}

export const goTo = (url: string, params: PrimitiveValues = {}, type = 'navigate') => {
  const queryString = Object.keys(params)
    .map((key) => `${key}=${params[key]}`)
    .join('&');

  const fullUrl = queryString ? `${url}?${queryString}` : url;
  const isTab = isTabBar(url);
  if (isTab) {
    if (url.startsWith('/pages/nearby/index')) {
      const nearbyType = getQueryString('type', url);
      if (nearbyType) {
        uni.setStorageSync('nearbyType', nearbyType);
      }
    }
    uni.switchTab({
      url: fullUrl,
    });
  } else if (type === 'navigate') {
    uni.navigateTo({
      url: fullUrl,
    });
  } else {
    uni.redirectTo({
      url: fullUrl,
    });
  }
};

export function goToH5(url: string) {
  uni.navigateTo({
    url: `/pages/common/webview/index?url=${url}`,
  });
}

/**
 * @description: 跳转微信小程序
 * @param {string} url
 * @return {*}
 */
export function goToWxMiniApp(url: string) {
  const { appid, path, query } = parseQueryString(url);
  if (!appid || !path) return;
  wx.navigateToMiniProgram({
    appId: appid,
    path: `${path}${query ? `?${query}` : ''}`,
  });
}

// 怀宁只有小程序，这里应该都是 inner 渠道，后续再完善,todo
export const goToLinkType = (
  linkType: Nullable<BannerElementVO.linkType>,
  link: Nullable<string>,
) => {
  if (!link) return;
  switch (linkType) {
    case BannerElementVO.linkType.INNER:
      goTo(link);
      break;
    case BannerElementVO.linkType.WX:
      goToWxMiniApp(link);
      break;
    case BannerElementVO.linkType.ZFB:
      goTo(link);
      break;
    case BannerElementVO.linkType.APP:
      goTo(link);
      break;
    case BannerElementVO.linkType.OUTER:
      goToH5(link);
      break;
    default:
      goTo(link);
      break;
  }
};
