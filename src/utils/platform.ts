// 当前运行平台
// 维护运行环境类型
export enum EPlatform {
  'mp-alipay' = 'mp-alipay',
  'mp-weixin' = 'mp-weixin',
}
export type TPlatform = keyof typeof EPlatform;
// eslint-disable-next-line
export const UNI_PLATFORM = process.env.UNI_PLATFORM as TPlatform;

const platformInfo = {
  [EPlatform['mp-alipay']]: {
    osType: 'ALIPAY_MINI',
    channelCode: import.meta.env.VITE_APP_ALIPAY_APP_ID,
    flag: 'ZFB',
  },
  [EPlatform['mp-weixin']]: {
    osType: 'WECHAT_MINI',
    channelCode: import.meta.env.VITE_WEIXIN_APP_ID,
    flag: 'WX',
  },
};

export const CHANNEL_CODE: string = platformInfo[UNI_PLATFORM as EPlatform]?.channelCode || '';
export const FLAG: string = platformInfo[UNI_PLATFORM as EPlatform]?.flag || '';
export const OS_TYPE: string = platformInfo[UNI_PLATFORM as EPlatform]?.osType || '';
