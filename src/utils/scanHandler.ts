import { checkLogin } from '@/hooks/useCheckLogin';
import { ChargingService } from '@/service';
import { scan } from '@/utils/scanJsapi';
import { showSingleToast } from './jsapi';
import { getQueryString } from './route';

export const scanGo = (url: string, entry: 'pageScan' | 'commonScan' = 'pageScan') => {
  if (entry === 'pageScan') {
    uni.navigateTo({ url });
  } else {
    uni.redirectTo({ url });
  }
};

export async function scanHandler(qrCode: string) {
  if (qrCode.includes('pileNo') && qrCode.includes('gunNo')) {
    // 充电的入口
    chargingScan(qrCode, 'commonScan');
  } else if (qrCode.includes('type')) {
    const type = getQueryString('type', qrCode);
    // 无牌车扫码
    if (['entry', 'exit'].includes(type)) {
      handleNoPlate(qrCode, 'commonScan');
    } else if (['roadPay', 'channelPay', 'platePay'].includes(type)) {
      // 路边停车缴费、渠道缴费、场内码缴费
      handlePay(qrCode, 'commonScan');
    } else {
      // 其他情况（非停车场扫码缴费）
      showSingleToast('请扫正确的二维码');
      uni.switchTab({ url: `/pages/home/<USER>
    }
  } else if (qrCode.includes('filingNo')) {
    // 备案信息
    handleFiling(qrCode, 'commonScan');
  } else {
    showSingleToast('请扫正确的二维码');
    uni.switchTab({ url: `/pages/home/<USER>
  }
}
export async function handleFiling(qrcode: string, entry: 'pageScan' | 'commonScan' = 'pageScan') {
  const queryString = qrcode.split('?')[1]; // 获取问号后面的部分
  const params = parseQueryString(queryString);
  const url = `/pages/noticeBoard/parkRegister/detail/index?filingNo=${params?.filingNo}`;
  // await checkLogin(url);
  scanGo(url, entry);
}
export async function chargingScan(qrcode: string, entry: 'pageScan' | 'commonScan' = 'pageScan') {
  console.log('qrcode', qrcode);
  if (!(qrcode.includes('pileNo') && qrcode.includes('gunNo'))) {
    showSingleToast('二维码错误，请确认后再试');
    return;
  }
  const url = `/pages/charging/index/index?qrcode=${encodeURIComponent(qrcode)}`;
  await checkLogin(url);
  const [err, res] = await ChargingService.postChargeQrcode({ qrcode });
  console.log('Charge', err, res);
  if (err) {
    showSingleToast(err.subMsg || '系统异常');
    return;
  }
  const { chargeStatus, orderId } = res?.data || {};
  if (chargeStatus === 'NORMAL' || chargeStatus === 'WAIT_PAY') {
    // 正常跳转
    scanGo(`/pages/charging/index/index?qrcode=${encodeURIComponent(qrcode)}`, entry);
  } else if (chargeStatus === 'CHARGING') {
    scanGo(`/pages/charging/isCharging/index?orderId=${orderId}`, entry);
  }
}

export async function scanTap() {
  const qrcode = await scan();
  if (qrcode) {
    scanHandler(qrcode);
  }
}
// 充电桩扫码通用（出行扫码充电，站点详情扫码充电）
export async function chargeScanTap() {
  await checkLogin();
  const qrcode = await scan();
  if (qrcode) {
    chargingScan(qrcode);
  }
}
// 微信支付宝扫一扫处理入口
export async function commonScan(options: any) {
  console.error('commonScan', options);
  // 微信支付宝扫一扫打开小程序逻辑处理
  // #ifdef MP-WEIXIN
  console.log('微信直接扫码传参', options);
  if (options?.query?.q) {
    scanHandler(decodeURIComponent(options.query.q)); // 获取到微信二维码原始链接内容
  }
  // #endif
  // #ifdef MP-ALIPAY
  console.log('支付宝直接扫码传参', options);
  if (options?.query?.qrCode) {
    scanHandler(decodeURIComponent(options.query.qrCode)); // 获取到支付宝二维码原始链接内容
  }
  // #endif
}

// 无牌车扫码
export async function scanNoPlate() {
  await checkLogin('/pages/travelServices/index');
  const qrcode = await scan();
  if (qrcode) {
    handleNoPlate(qrcode);
  }
}

// 无车牌扫码处理
export async function handleNoPlate(qrcode, entry: 'pageScan' | 'commonScan' = 'pageScan') {
  const queryString = qrcode.split('?')[1]; // 获取问号后面的部分
  const params = parseQueryString(queryString);
  const url = `/pages/travelServices/loadingPage/index?scanType=noPlate&type=${params?.type}&outParkId=${params?.outParkId}&outChannelId=${params?.outChannelId}`;
  await checkLogin(url);
  scanGo(url, entry);
}

function parseQueryString(queryString) {
  if (!queryString) return {};
  return queryString.split('&').reduce((params, param) => {
    const [key, value] = param.split('=');
    params[decodeURIComponent(key)] = decodeURIComponent(value);
    return params;
  }, {});
}

// 扫码缴费
export async function scanPay() {
  const qrcode = await scan();
  if (qrcode) {
    handlePay(qrcode);
  }
}

export async function handlePay(qrcode: string, entry: 'pageScan' | 'commonScan' = 'pageScan') {
  const queryString = qrcode.split('?')[1]; // 获取问号后面的部分
  const params = parseQueryString(queryString);
  // 场内扫码跳转首页, 路边、通道码跳转支付详情页
  if (params.type === 'platePay') {
    const pages = getCurrentPages();
    console.error(pages[pages.length - 1].route);
    if (!(pages.length && pages[pages.length - 1].route === 'pages/home/<USER>')) {
      uni.switchTab({ url: `/pages/home/<USER>
    }
  } else {
    const url = `/pages/travelServices/loadingPage/index?scanType=${params?.type}&type=${params?.type}&outParkingId=${params?.outParkingId || ''}&extBusinessId=${params?.extBusinessId || ''}&outChannelId=${params?.outChannelId || ''}`;
    scanGo(url, entry);
  }
}
