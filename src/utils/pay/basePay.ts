// 支付渠道名称
export enum payChannelEnum {
  /** 微信小程序 */
  WEIXIN_MP = 'mp-weixin',
  /** 支付宝小程序 */
  ALIPAY_MP = 'mp-alipay',
  /** H5 */
  Pay_H5 = 'H5',
}

type PayChannel = `${payChannelEnum}`;

/**
 * @description: 支付方法基础类
 * @return {*}
 */
class BasePay {
  platform: PayChannel | '' = '';

  constructor() {
    this.getPlatform();
  }

  // 获取当前平台
  getPlatform() {
    this.platform = (process.env.UNI_PLATFORM as PayChannel) || '';
  }

  // 支付成功处理
  success(): any {
    console.log('支付成功');
  }

  // 支付失败处理
  fail(): any {
    return uni.showToast({ title: '支付失败/取消', icon: 'none' });
  }

  /**
   * 未找到支付渠道处理方法
   */
  unFind() {
    const msg = '未找到支付渠道';
    uni.showToast({ title: msg, icon: 'none' });
  }
}

export default BasePay;
