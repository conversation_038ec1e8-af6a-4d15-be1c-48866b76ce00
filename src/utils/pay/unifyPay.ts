import BasePay, { payChannelEnum } from './basePay';
import {
  wxPay,
  alipay,
  H5Pay,
  IAlipayParams,
  IWxParams,
  payStatusEnum,
  TPayStatus,
} from './payMethods';

class UnifyPay extends BasePay {
  /**
   * @description: 支付调度
   * @param {IAlipayParams | IWxParams} params
   * @return {*}
   */
  async dispatchPay(params: IAlipayParams | IWxParams) {
    console.log('dispatchPay', this.platform);
    let res: TPayStatus | '';
    if (this.platform === payChannelEnum.WEIXIN_MP) {
      res = await wxPay(params as IWxParams);
    } else if (this.platform === payChannelEnum.ALIPAY_MP) {
      res = await alipay(params as IAlipayParams);
    } else if (this.platform === payChannelEnum.Pay_H5) {
      // H5支付请自行跳转到支付页面
      res = await H5Pay(params);
    } else {
      this.unFind();
      return;
    }
    this.handlePayResult(res);
    return res;
  }

  handlePayResult(res: TPayStatus) {
    if (res === payStatusEnum.cancel) {
      this.fail();
    } else if (res === payStatusEnum.fail) {
      this.fail();
    }
  }
}

const unifyPay = new UnifyPay();

export default unifyPay;
