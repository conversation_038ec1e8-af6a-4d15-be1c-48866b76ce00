export interface IWxParams {
  timeStamp: string; // 时间戳
  nonceStr: string; // 随机字符串
  package: string; // 统一下单接口返回的订单
  signType?: 'MD5' | 'RSA' | 'HMAC-SHA256';
  paySign: string;
  orderStr?: string;
}

export interface IAlipayParams {
  // 支付宝交易号
  tradeNO: string;
}

export enum payStatusEnum {
  success = 'success',
  fail = 'fail',
  cancel = 'cancel',
}

export type TPayStatus = keyof typeof payStatusEnum;

export function wxPay(params: IWxParams): Promise<TPayStatus> {
  if (!params.package) {
    params.package = params.orderStr || '';
  }
  return new Promise((resolve, reject) => {
    wx.requestPayment({
      ...params,
      success: (res) => {
        if (res.errMsg === 'requestPayment:ok') {
          resolve(payStatusEnum.success);
        } else if (res.errMsg === 'requestPayment:fail cancel') {
          resolve(payStatusEnum.cancel);
        } else {
          resolve(payStatusEnum.cancel);
        }
      },
      fail: (res) => {
        if (res.errMsg === 'requestPayment:fail cancel') {
          resolve(payStatusEnum.cancel);
        }
        resolve(payStatusEnum.fail);
      },
    });
  });
}

/**
 * @description: 支付宝支付
 * @param {IPayParams} params
 * @return {*}
 */
export function alipay(params: IAlipayParams): Promise<TPayStatus> {
  const { tradeNO } = params;
  return new Promise((resolve, reject) => {
    my.tradePay({
      tradeNO,
      success: (res: any) => {
        if (res.resultCode === '6001') {
          resolve(payStatusEnum.cancel);
        } else if (res.resultCode === '9000') {
          resolve(payStatusEnum.success);
        } else {
          resolve(payStatusEnum.fail);
        }
      },
      fail: () => {
        resolve(payStatusEnum.fail);
      },
    });
  });
}

// #ifdef H5
const bridge = window.dsBridge?.call.bind(window.dsBridge);
// #endif

export async function H5Pay(par: any): Promise<TPayStatus> {
  // #ifdef H5
  const res = await bridge('bangdao.pay.bd_appPay', par);
  const result = JSON.parse(res);
  if (result?.code === '9999') {
    return payStatusEnum.success;
  } else {
    return payStatusEnum.fail;
  }
  // #endif
}
