function t(t) {
  return t && t.__esModule && Object.prototype.hasOwnProperty.call(t, 'default') ? t.default : t;
}
function e(t, e) {
  return t((e = { exports: {} }), e.exports), e.exports;
}
const r = e(function (t) {
  const e = (t.exports =
    typeof window !== 'undefined' && window.Math == Math
      ? window
      : typeof self !== 'undefined' && self.Math == Math
        ? self
        : Function('return this')());
  typeof __g === 'number' && (__g = e);
});
const n = e(function (t) {
  const e = (t.exports = { version: '2.6.9' });
  typeof __e === 'number' && (__e = e);
});
const o =
  (n.version,
  function (t) {
    if (typeof t !== 'function') throw TypeError(`${t} is not a function!`);
    return t;
  });
const i = function (t, e, r) {
  if ((o(t), void 0 === e)) return t;
  switch (r) {
    case 1:
      return function (r) {
        return t.call(e, r);
      };
    case 2:
      return function (r, n) {
        return t.call(e, r, n);
      };
    case 3:
      return function (r, n, o) {
        return t.call(e, r, n, o);
      };
  }
  return function () {
    return t.apply(e, arguments);
  };
};
const c = function (t) {
  return typeof t === 'object' ? t !== null : typeof t === 'function';
};
const u = function (t) {
  if (!c(t)) throw TypeError(`${t} is not an object!`);
  return t;
};
const a = function (t) {
  try {
    return !!t();
  } catch (t) {
    return !0;
  }
};
const s = !a(function () {
  return (
    Object.defineProperty({}, 'a', {
      get() {
        return 7;
      },
    }).a != 7
  );
});
const f = r.document;
const l = c(f) && c(f.createElement);
const h = function (t) {
  return l ? f.createElement(t) : {};
};
const p =
  !s &&
  !a(function () {
    return (
      Object.defineProperty(h('div'), 'a', {
        get() {
          return 7;
        },
      }).a != 7
    );
  });
const v = Object.defineProperty;
const d = {
  f: s
    ? Object.defineProperty
    : function (t, e, r) {
        if (
          (u(t),
          (e = (function (t, e) {
            if (!c(t)) return t;
            let r;
            let n;
            if (e && typeof (r = t.toString) === 'function' && !c((n = r.call(t)))) return n;
            if (typeof (r = t.valueOf) === 'function' && !c((n = r.call(t)))) return n;
            if (!e && typeof (r = t.toString) === 'function' && !c((n = r.call(t)))) return n;
            throw TypeError("Can't convert object to primitive value");
          })(e, !0)),
          u(r),
          p)
        )
          try {
            return v(t, e, r);
          } catch (t) {}
        if ('get' in r || 'set' in r) throw TypeError('Accessors not supported!');
        return 'value' in r && (t[e] = r.value), t;
      },
};
const y = function (t, e) {
  return { enumerable: !(1 & t), configurable: !(2 & t), writable: !(4 & t), value: e };
};
const m = s
  ? function (t, e, r) {
      return d.f(t, e, y(1, r));
    }
  : function (t, e, r) {
      return (t[e] = r), t;
    };
const g = {}.hasOwnProperty;
const w = function (t, e) {
  return g.call(t, e);
};
const _ = function (t, e, o) {
  let c;
  let u;
  let a;
  const s = t & _.F;
  const f = t & _.G;
  const l = t & _.S;
  const h = t & _.P;
  const p = t & _.B;
  const v = t & _.W;
  const d = f ? n : n[e] || (n[e] = {});
  const y = d.prototype;
  const g = f ? r : l ? r[e] : (r[e] || {}).prototype;
  for (c in (f && (o = e), o))
    ((u = !s && g && void 0 !== g[c]) && w(d, c)) ||
      ((a = u ? g[c] : o[c]),
      (d[c] =
        f && typeof g[c] !== 'function'
          ? o[c]
          : p && u
            ? i(a, r)
            : v && g[c] == a
              ? (function (t) {
                  const e = function (e, r, n) {
                    if (this instanceof t) {
                      switch (arguments.length) {
                        case 0:
                          return new t();
                        case 1:
                          return new t(e);
                        case 2:
                          return new t(e, r);
                      }
                      return new t(e, r, n);
                    }
                    return t.apply(this, arguments);
                  };
                  return (e.prototype = t.prototype), e;
                })(a)
              : h && typeof a === 'function'
                ? i(Function.call, a)
                : a),
      h && (((d.virtual || (d.virtual = {}))[c] = a), t & _.R && y && !y[c] && m(y, c, a)));
};
(_.F = 1), (_.G = 2), (_.S = 4), (_.P = 8), (_.B = 16), (_.W = 32), (_.U = 64), (_.R = 128);
let b;
const O = _;
const x = {}.toString;
const L = function (t) {
  return x.call(t).slice(8, -1);
};
const j = Object('z').propertyIsEnumerable(0)
  ? Object
  : function (t) {
      return L(t) == 'String' ? t.split('') : Object(t);
    };
const P = function (t) {
  if (t == null) throw TypeError(`Can't call method on  ${t}`);
  return t;
};
const S = function (t) {
  return j(P(t));
};
const E = Math.ceil;
const T = Math.floor;
const M = function (t) {
  return isNaN((t = +t)) ? 0 : (t > 0 ? T : E)(t);
};
const k = Math.min;
const R = function (t) {
  return t > 0 ? k(M(t), 9007199254740991) : 0;
};
const N = Math.max;
const A = Math.min;
const F = e(function (t) {
  const e = '__core-js_shared__';
  const o = r[e] || (r[e] = {});
  (t.exports = function (t, e) {
    return o[t] || (o[t] = void 0 !== e ? e : {});
  })('versions', []).push({
    version: n.version,
    mode: 'pure',
    copyright: '© 2019 Denis Pushkarev (zloirock.ru)',
  });
});
let C = 0;
const G = Math.random();
const I = function (t) {
  return 'Symbol('.concat(void 0 === t ? '' : t, ')_', (++C + G).toString(36));
};
const D = F('keys');
const V = function (t) {
  return D[t] || (D[t] = I(t));
};
const q =
  ((b = !1),
  function (t, e, r) {
    let n;
    const o = S(t);
    const i = R(o.length);
    let c = (function (t, e) {
      return (t = M(t)) < 0 ? N(t + e, 0) : A(t, e);
    })(r, i);
    if (b && e != e) {
      for (; i > c; ) if ((n = o[c++]) != n) return !0;
    } else for (; i > c; c++) if ((b || c in o) && o[c] === e) return b || c || 0;
    return !b && -1;
  });
const B = V('IE_PROTO');
const H =
  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'.split(
    ',',
  );
const W =
  Object.keys ||
  function (t) {
    return (function (t, e) {
      let r;
      const n = S(t);
      let o = 0;
      const i = [];
      for (r in n) r != B && w(n, r) && i.push(r);
      for (; e.length > o; ) w(n, (r = e[o++])) && (~q(i, r) || i.push(r));
      return i;
    })(t, H);
  };
const U = { f: Object.getOwnPropertySymbols };
const z = { f: {}.propertyIsEnumerable };
const K = function (t) {
  return Object(P(t));
};
const Y = Object.assign;
const J =
  !Y ||
  a(function () {
    const t = {};
    const e = {};
    const r = Symbol();
    const n = 'abcdefghijklmnopqrst';
    return (
      (t[r] = 7),
      n.split('').forEach(function (t) {
        e[t] = t;
      }),
      { ...t }[r] != 7 || Object.keys({ ...e }).join('') != n
    );
  })
    ? function (t, e) {
        for (var r = K(t), n = arguments.length, o = 1, i = U.f, c = z.f; n > o; )
          for (
            var u, a = j(arguments[o++]), f = i ? W(a).concat(i(a)) : W(a), l = f.length, h = 0;
            l > h;

          )
            (u = f[h++]), (s && !c.call(a, u)) || (r[u] = a[u]);
        return r;
      }
    : Y;
O(O.S + O.F, 'Object', { assign: J });
const Q = n.Object.assign;
const X = e(function (t) {
  t.exports = { default: Q, __esModule: !0 };
});
t(X);
const Z = e(function (t, e) {
  e.__esModule = !0;
  let r;
  const n = (r = X) && r.__esModule ? r : { default: r };
  e.default =
    n.default ||
    function (t) {
      for (let e = 1; e < arguments.length; e++) {
        const r = arguments[e];
        for (const n in r) Object.prototype.hasOwnProperty.call(r, n) && (t[n] = r[n]);
      }
      return t;
    };
});
const $ = t(Z);
const tt = m;
const et = {};
const rt = s
  ? Object.defineProperties
  : function (t, e) {
      u(t);
      for (var r, n = W(e), o = n.length, i = 0; o > i; ) d.f(t, (r = n[i++]), e[r]);
      return t;
    };
const nt = r.document;
const ot = nt && nt.documentElement;
const it = V('IE_PROTO');
const ct = function () {};
let ut = function () {
  let t;
  const e = h('iframe');
  let r = H.length;
  for (
    e.style.display = 'none',
      ot.appendChild(e),
      e.src = 'javascript:',
      (t = e.contentWindow.document).open(),
      t.write('<script>document.F=Object</script>'),
      t.close(),
      ut = t.F;
    r--;

  )
    delete ut.prototype[H[r]];
  return ut();
};
const at =
  Object.create ||
  function (t, e) {
    let r;
    return (
      t !== null
        ? ((ct.prototype = u(t)), (r = new ct()), (ct.prototype = null), (r[it] = t))
        : (r = ut()),
      void 0 === e ? r : rt(r, e)
    );
  };
const st = e(function (t) {
  const e = F('wks');
  const n = r.Symbol;
  const o = typeof n === 'function';
  (t.exports = function (t) {
    return e[t] || (e[t] = (o && n[t]) || (o ? n : I)(`Symbol.${t}`));
  }).store = e;
});
const ft = d.f;
const lt = st('toStringTag');
const ht = function (t, e, r) {
  t && !w((t = r ? t : t.prototype), lt) && ft(t, lt, { configurable: !0, value: e });
};
const pt = {};
m(pt, st('iterator'), function () {
  return this;
});
let vt;
const dt = function (t, e, r) {
  (t.prototype = at(pt, { next: y(1, r) })), ht(t, `${e} Iterator`);
};
const yt = V('IE_PROTO');
const mt = Object.prototype;
const gt =
  Object.getPrototypeOf ||
  function (t) {
    return (
      (t = K(t)),
      w(t, yt)
        ? t[yt]
        : typeof t.constructor === 'function' && t instanceof t.constructor
          ? t.constructor.prototype
          : t instanceof Object
            ? mt
            : null
    );
  };
const wt = st('iterator');
const _t = !([].keys && 'next' in [].keys());
const bt = function () {
  return this;
};
const Ot = function (t, e, r, n, o, i, c) {
  dt(r, e, n);
  let u;
  let a;
  let s;
  const f = function (t) {
    if (!_t && t in v) return v[t];
    switch (t) {
      case 'keys':
      case 'values':
        return function () {
          return new r(this, t);
        };
    }
    return function () {
      return new r(this, t);
    };
  };
  const l = `${e} Iterator`;
  const h = o == 'values';
  let p = !1;
  var v = t.prototype;
  const d = v[wt] || v['@@iterator'] || (o && v[o]);
  let y = d || f(o);
  const g = o ? (h ? f('entries') : y) : void 0;
  const w = (e == 'Array' && v.entries) || d;
  if (
    (w && (s = gt(w.call(new t()))) !== Object.prototype && s.next && ht(s, l, !0),
    h &&
      d &&
      d.name !== 'values' &&
      ((p = !0),
      (y = function () {
        return d.call(this);
      })),
    c && (_t || p || !v[wt]) && m(v, wt, y),
    (et[e] = y),
    (et[l] = bt),
    o)
  )
    if (((u = { values: h ? y : f('values'), keys: i ? y : f('keys'), entries: g }), c))
      for (a in u) a in v || tt(v, a, u[a]);
    else O(O.P + O.F * (_t || p), e, u);
  return u;
};
const xt =
  ((vt = !0),
  function (t, e) {
    let r;
    let n;
    const o = String(P(t));
    const i = M(e);
    const c = o.length;
    return i < 0 || i >= c
      ? vt
        ? ''
        : void 0
      : (r = o.charCodeAt(i)) < 55296 ||
          r > 56319 ||
          i + 1 === c ||
          (n = o.charCodeAt(i + 1)) < 56320 ||
          n > 57343
        ? vt
          ? o.charAt(i)
          : r
        : vt
          ? o.slice(i, i + 2)
          : n - 56320 + ((r - 55296) << 10) + 65536;
  });
Ot(
  String,
  'String',
  function (t) {
    (this._t = String(t)), (this._i = 0);
  },
  function () {
    let t;
    const e = this._t;
    const r = this._i;
    return r >= e.length
      ? { value: void 0, done: !0 }
      : ((t = xt(e, r)), (this._i += t.length), { value: t, done: !1 });
  },
);
const Lt = function (t, e) {
  return { value: e, done: !!t };
};
Ot(
  Array,
  'Array',
  function (t, e) {
    (this._t = S(t)), (this._i = 0), (this._k = e);
  },
  function () {
    const t = this._t;
    const e = this._k;
    const r = this._i++;
    return !t || r >= t.length
      ? ((this._t = void 0), Lt(1))
      : Lt(0, e == 'keys' ? r : e == 'values' ? t[r] : [r, t[r]]);
  },
  'values',
);
et.Arguments = et.Array;
for (
  let jt = st('toStringTag'),
    Pt =
      'CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList'.split(
        ',',
      ),
    St = 0;
  St < Pt.length;
  St++
) {
  const Et = Pt[St];
  const Tt = r[Et];
  const Mt = Tt && Tt.prototype;
  Mt && !Mt[jt] && m(Mt, jt, Et), (et[Et] = et.Array);
}
let kt;
let Rt;
let Nt;
const At = st('toStringTag');
const Ft =
  L(
    (function () {
      return arguments;
    })(),
  ) == 'Arguments';
const Ct = function (t) {
  let e;
  let r;
  let n;
  return void 0 === t
    ? 'Undefined'
    : t === null
      ? 'Null'
      : typeof (r = (function (t, e) {
            try {
              return t[e];
            } catch (t) {}
          })((e = Object(t)), At)) === 'string'
        ? r
        : Ft
          ? L(e)
          : (n = L(e)) == 'Object' && typeof e.callee === 'function'
            ? 'Arguments'
            : n;
};
const Gt = function (t, e, r, n) {
  try {
    return n ? e(u(r)[0], r[1]) : e(r);
  } catch (e) {
    const o = t.return;
    throw (void 0 !== o && u(o.call(t)), e);
  }
};
const It = st('iterator');
const Dt = Array.prototype;
const Vt = st('iterator');
const qt = (n.getIteratorMethod = function (t) {
  if (t != null) return t[Vt] || t['@@iterator'] || et[Ct(t)];
});
const Bt = e(function (t) {
  const e = {};
  const r = {};
  const n = (t.exports = function (t, n, o, c, a) {
    let s;
    let f;
    let l;
    let h;
    let p;
    const v = a
      ? function () {
          return t;
        }
      : qt(t);
    const d = i(o, c, n ? 2 : 1);
    let y = 0;
    if (typeof v !== 'function') throw TypeError(`${t} is not iterable!`);
    if (void 0 === (p = v) || (et.Array !== p && Dt[It] !== p)) {
      for (l = v.call(t); !(f = l.next()).done; )
        if ((h = Gt(l, d, f.value, n)) === e || h === r) return h;
    } else
      for (s = R(t.length); s > y; y++)
        if ((h = n ? d(u((f = t[y]))[0], f[1]) : d(t[y])) === e || h === r) return h;
  });
  (n.BREAK = e), (n.RETURN = r);
});
const Ht = st('species');
const Wt = function (t, e) {
  let r;
  const n = u(t).constructor;
  return void 0 === n || (r = u(n)[Ht]) == null ? e : o(r);
};
const Ut = function (t, e, r) {
  const n = void 0 === r;
  switch (e.length) {
    case 0:
      return n ? t() : t.call(r);
    case 1:
      return n ? t(e[0]) : t.call(r, e[0]);
    case 2:
      return n ? t(e[0], e[1]) : t.call(r, e[0], e[1]);
    case 3:
      return n ? t(e[0], e[1], e[2]) : t.call(r, e[0], e[1], e[2]);
    case 4:
      return n ? t(e[0], e[1], e[2], e[3]) : t.call(r, e[0], e[1], e[2], e[3]);
  }
  return t.apply(r, e);
};
const zt = r.process;
let Kt = r.setImmediate;
let Yt = r.clearImmediate;
const Jt = r.MessageChannel;
const Qt = r.Dispatch;
let Xt = 0;
const Zt = {};
const $t = function () {
  const t = +this;
  if (Zt.hasOwnProperty(t)) {
    const e = Zt[t];
    delete Zt[t], e();
  }
};
const te = function (t) {
  $t.call(t.data);
};
(Kt && Yt) ||
  ((Kt = function (t) {
    for (var e = [], r = 1; arguments.length > r; ) e.push(arguments[r++]);
    return (
      (Zt[++Xt] = function () {
        Ut(typeof t === 'function' ? t : Function(t), e);
      }),
      kt(Xt),
      Xt
    );
  }),
  (Yt = function (t) {
    delete Zt[t];
  }),
  L(zt) == 'process'
    ? (kt = function (t) {
        zt.nextTick(i($t, t, 1));
      })
    : Qt && Qt.now
      ? (kt = function (t) {
          Qt.now(i($t, t, 1));
        })
      : Jt
        ? ((Nt = (Rt = new Jt()).port2), (Rt.port1.onmessage = te), (kt = i(Nt.postMessage, Nt, 1)))
        : r.addEventListener && typeof postMessage === 'function' && !r.importScripts
          ? ((kt = function (t) {
              r.postMessage(`${t}`, '*');
            }),
            r.addEventListener('message', te, !1))
          : (kt =
              'onreadystatechange' in h('script')
                ? function (t) {
                    ot.appendChild(h('script')).onreadystatechange = function () {
                      ot.removeChild(this), $t.call(t);
                    };
                  }
                : function (t) {
                    setTimeout(i($t, t, 1), 0);
                  }));
const ee = { set: Kt, clear: Yt };
const re = ee.set;
const ne = r.MutationObserver || r.WebKitMutationObserver;
const oe = r.process;
const ie = r.Promise;
const ce = L(oe) == 'process';
function ue(t) {
  let e;
  let r;
  (this.promise = new t(function (t, n) {
    if (void 0 !== e || void 0 !== r) throw TypeError('Bad Promise constructor');
    (e = t), (r = n);
  })),
    (this.resolve = o(e)),
    (this.reject = o(r));
}
const ae = {
  f(t) {
    return new ue(t);
  },
};
const se = function (t) {
  try {
    return { e: !1, v: t() };
  } catch (t) {
    return { e: !0, v: t };
  }
};
const fe = r.navigator;
const le = (fe && fe.userAgent) || '';
const he = function (t, e) {
  if ((u(t), c(e) && e.constructor === t)) return e;
  const r = ae.f(t);
  return (0, r.resolve)(e), r.promise;
};
const pe = st('species');
const ve = st('iterator');
let de = !1;
try {
  [7][ve]().return = function () {
    de = !0;
  };
} catch (t) {}
let ye;
let me;
let ge;
let we;
let _e;
let be;
const Oe = ee.set;
const xe = (function () {
  let t;
  let e;
  let n;
  const o = function () {
    let r;
    let o;
    for (ce && (r = oe.domain) && r.exit(); t; ) {
      (o = t.fn), (t = t.next);
      try {
        o();
      } catch (r) {
        throw (t ? n() : (e = void 0), r);
      }
    }
    (e = void 0), r && r.enter();
  };
  if (ce)
    n = function () {
      oe.nextTick(o);
    };
  else if (!ne || (r.navigator && r.navigator.standalone))
    if (ie && ie.resolve) {
      const i = ie.resolve(void 0);
      n = function () {
        i.then(o);
      };
    } else
      n = function () {
        re.call(r, o);
      };
  else {
    let c = !0;
    const u = document.createTextNode('');
    new ne(o).observe(u, { characterData: !0 }),
      (n = function () {
        u.data = c = !c;
      });
  }
  return function (r) {
    const o = { fn: r, next: void 0 };
    e && (e.next = o), t || ((t = o), n()), (e = o);
  };
})();
const Le = r.TypeError;
const je = r.process;
const Pe = je && je.versions;
const Se = (Pe && Pe.v8) || '';
let Ee = r.Promise;
const Te = Ct(je) == 'process';
const Me = function () {};
let ke = (me = ae.f);
const Re = !!(function () {
  try {
    const t = Ee.resolve(1);
    const e = ((t.constructor = {})[st('species')] = function (t) {
      t(Me, Me);
    });
    return (
      (Te || typeof PromiseRejectionEvent === 'function') &&
      t.then(Me) instanceof e &&
      Se.indexOf('6.6') !== 0 &&
      le.indexOf('Chrome/66') === -1
    );
  } catch (t) {}
})();
const Ne = function (t) {
  let e;
  return !(!c(t) || typeof (e = t.then) !== 'function') && e;
};
const Ae = function (t, e) {
  if (!t._n) {
    t._n = !0;
    const r = t._c;
    xe(function () {
      for (
        var n = t._v,
          o = t._s == 1,
          i = 0,
          c = function (e) {
            let r;
            let i;
            let c;
            const u = o ? e.ok : e.fail;
            const a = e.resolve;
            const s = e.reject;
            const f = e.domain;
            try {
              u
                ? (o || (t._h == 2 && Ge(t), (t._h = 1)),
                  !0 === u ? (r = n) : (f && f.enter(), (r = u(n)), f && (f.exit(), (c = !0))),
                  r === e.promise
                    ? s(Le('Promise-chain cycle'))
                    : (i = Ne(r))
                      ? i.call(r, a, s)
                      : a(r))
                : s(n);
            } catch (t) {
              f && !c && f.exit(), s(t);
            }
          };
        r.length > i;

      )
        c(r[i++]);
      (t._c = []), (t._n = !1), e && !t._h && Fe(t);
    });
  }
};
var Fe = function (t) {
  Oe.call(r, function () {
    let e;
    let n;
    let o;
    const i = t._v;
    const c = Ce(t);
    if (
      (c &&
        ((e = se(function () {
          Te
            ? je.emit('unhandledRejection', i, t)
            : (n = r.onunhandledrejection)
              ? n({ promise: t, reason: i })
              : (o = r.console) && o.error && o.error('Unhandled promise rejection', i);
        })),
        (t._h = Te || Ce(t) ? 2 : 1)),
      (t._a = void 0),
      c && e.e)
    )
      throw e.v;
  });
};
var Ce = function (t) {
  return t._h !== 1 && (t._a || t._c).length === 0;
};
var Ge = function (t) {
  Oe.call(r, function () {
    let e;
    Te
      ? je.emit('rejectionHandled', t)
      : (e = r.onrejectionhandled) && e({ promise: t, reason: t._v });
  });
};
const Ie = function (t) {
  let e = this;
  e._d ||
    ((e._d = !0), ((e = e._w || e)._v = t), (e._s = 2), e._a || (e._a = e._c.slice()), Ae(e, !0));
};
const De = function (t) {
  let e;
  let r = this;
  if (!r._d) {
    (r._d = !0), (r = r._w || r);
    try {
      if (r === t) throw Le("Promise can't be resolved itself");
      (e = Ne(t))
        ? xe(function () {
            const n = { _w: r, _d: !1 };
            try {
              e.call(t, i(De, n, 1), i(Ie, n, 1));
            } catch (t) {
              Ie.call(n, t);
            }
          })
        : ((r._v = t), (r._s = 1), Ae(r, !1));
    } catch (t) {
      Ie.call({ _w: r, _d: !1 }, t);
    }
  }
};
Re ||
  ((Ee = function (t) {
    !(function (t, e, r, n) {
      if (!(t instanceof e) || (void 0 !== n && n in t))
        throw TypeError(`${r}: incorrect invocation!`);
    })(this, Ee, 'Promise', '_h'),
      o(t),
      ye.call(this);
    try {
      t(i(De, this, 1), i(Ie, this, 1));
    } catch (t) {
      Ie.call(this, t);
    }
  }),
  ((ye = function (t) {
    (this._c = []),
      (this._a = void 0),
      (this._s = 0),
      (this._d = !1),
      (this._v = void 0),
      (this._h = 0),
      (this._n = !1);
  }).prototype = (function (t, e, r) {
    for (const n in e) r && t[n] ? (t[n] = e[n]) : m(t, n, e[n]);
    return t;
  })(Ee.prototype, {
    then(t, e) {
      const r = ke(Wt(this, Ee));
      return (
        (r.ok = typeof t !== 'function' || t),
        (r.fail = typeof e === 'function' && e),
        (r.domain = Te ? je.domain : void 0),
        this._c.push(r),
        this._a && this._a.push(r),
        this._s && Ae(this, !1),
        r.promise
      );
    },
    catch(t) {
      return this.then(void 0, t);
    },
  })),
  (ge = function () {
    const t = new ye();
    (this.promise = t), (this.resolve = i(De, t, 1)), (this.reject = i(Ie, t, 1));
  }),
  (ae.f = ke =
    function (t) {
      return t === Ee || t === we ? new ge(t) : me(t);
    })),
  O(O.G + O.W + O.F * !Re, { Promise: Ee }),
  ht(Ee, 'Promise'),
  (be = typeof n[(_e = 'Promise')] === 'function' ? n[_e] : r[_e]),
  s &&
    be &&
    !be[pe] &&
    d.f(be, pe, {
      configurable: !0,
      get() {
        return this;
      },
    }),
  (we = n.Promise),
  O(O.S + O.F * !Re, 'Promise', {
    reject(t) {
      const e = ke(this);
      return (0, e.reject)(t), e.promise;
    },
  }),
  O(O.S + true * O.F, 'Promise', {
    resolve(t) {
      return he(this === we ? Ee : this, t);
    },
  }),
  O(
    O.S +
      O.F *
        !(
          Re &&
          (function (t, e) {
            if (!e && !de) return !1;
            let r = !1;
            try {
              const n = [7];
              const o = n[ve]();
              (o.next = function () {
                return { done: (r = !0) };
              }),
                (n[ve] = function () {
                  return o;
                }),
                t(n);
            } catch (t) {}
            return r;
          })(function (t) {
            Ee.all(t).catch(Me);
          })
        ),
    'Promise',
    {
      all(t) {
        const e = this;
        const r = ke(e);
        const n = r.resolve;
        const o = r.reject;
        const i = se(function () {
          const r = [];
          let i = 0;
          let c = 1;
          Bt(t, !1, function (t) {
            const u = i++;
            let a = !1;
            r.push(void 0),
              c++,
              e.resolve(t).then(function (t) {
                a || ((a = !0), (r[u] = t), --c || n(r));
              }, o);
          }),
            --c || n(r);
        });
        return i.e && o(i.v), r.promise;
      },
      race(t) {
        const e = this;
        const r = ke(e);
        const n = r.reject;
        const o = se(function () {
          Bt(t, !1, function (t) {
            e.resolve(t).then(r.resolve, n);
          });
        });
        return o.e && n(o.v), r.promise;
      },
    },
  ),
  O(O.P + O.R, 'Promise', {
    finally(t) {
      const e = Wt(this, n.Promise || r.Promise);
      const o = typeof t === 'function';
      return this.then(
        o
          ? function (r) {
              return he(e, t()).then(function () {
                return r;
              });
            }
          : t,
        o
          ? function (r) {
              return he(e, t()).then(function () {
                throw r;
              });
            }
          : t,
      );
    },
  }),
  O(O.S, 'Promise', {
    try(t) {
      const e = ae.f(this);
      const r = se(t);
      return (r.e ? e.reject : e.resolve)(r.v), e.promise;
    },
  });
const Ve = n.Promise;
const qe = e(function (t) {
  t.exports = { default: Ve, __esModule: !0 };
});
const Be = t(qe);
function He() {
  return typeof my !== 'undefined';
}
function We() {
  return typeof wx !== 'undefined';
}
const Ue = e(function (t) {
  !(function (e) {
    let r;
    const n = Object.prototype;
    const o = n.hasOwnProperty;
    const i = typeof Symbol === 'function' ? Symbol : {};
    const c = i.iterator || '@@iterator';
    const u = i.asyncIterator || '@@asyncIterator';
    const a = i.toStringTag || '@@toStringTag';
    let s = e.regeneratorRuntime;
    if (s) t.exports = s;
    else {
      (s = e.regeneratorRuntime = t.exports).wrap = w;
      var f = 'suspendedStart';
      var l = 'suspendedYield';
      var h = 'executing';
      var p = 'completed';
      var v = {};
      let d = {};
      d[c] = function () {
        return this;
      };
      const y = Object.getPrototypeOf;
      const m = y && y(y(M([])));
      m && m !== n && o.call(m, c) && (d = m);
      const g = (x.prototype = b.prototype = Object.create(d));
      (O.prototype = g.constructor = x),
        (x.constructor = O),
        (x[a] = O.displayName = 'GeneratorFunction'),
        (s.isGeneratorFunction = function (t) {
          const e = typeof t === 'function' && t.constructor;
          return !!e && (e === O || (e.displayName || e.name) === 'GeneratorFunction');
        }),
        (s.mark = function (t) {
          return (
            Object.setPrototypeOf
              ? Object.setPrototypeOf(t, x)
              : ((t.__proto__ = x), a in t || (t[a] = 'GeneratorFunction')),
            (t.prototype = Object.create(g)),
            t
          );
        }),
        (s.awrap = function (t) {
          return { __await: t };
        }),
        L(j.prototype),
        (j.prototype[u] = function () {
          return this;
        }),
        (s.AsyncIterator = j),
        (s.async = function (t, e, r, n) {
          const o = new j(w(t, e, r, n));
          return s.isGeneratorFunction(e)
            ? o
            : o.next().then(function (t) {
                return t.done ? t.value : o.next();
              });
        }),
        L(g),
        (g[a] = 'Generator'),
        (g[c] = function () {
          return this;
        }),
        (g.toString = function () {
          return '[object Generator]';
        }),
        (s.keys = function (t) {
          const e = [];
          for (const r in t) e.push(r);
          return (
            e.reverse(),
            function r() {
              for (; e.length; ) {
                const n = e.pop();
                if (n in t) return (r.value = n), (r.done = !1), r;
              }
              return (r.done = !0), r;
            }
          );
        }),
        (s.values = M),
        (T.prototype = {
          constructor: T,
          reset(t) {
            if (
              ((this.prev = 0),
              (this.next = 0),
              (this.sent = this._sent = r),
              (this.done = !1),
              (this.delegate = null),
              (this.method = 'next'),
              (this.arg = r),
              this.tryEntries.forEach(E),
              !t)
            )
              for (const e in this)
                e.charAt(0) === 't' && o.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = r);
          },
          stop() {
            this.done = !0;
            const t = this.tryEntries[0].completion;
            if (t.type === 'throw') throw t.arg;
            return this.rval;
          },
          dispatchException(t) {
            if (this.done) throw t;
            const e = this;
            function n(n, o) {
              return (
                (u.type = 'throw'),
                (u.arg = t),
                (e.next = n),
                o && ((e.method = 'next'), (e.arg = r)),
                !!o
              );
            }
            for (let i = this.tryEntries.length - 1; i >= 0; --i) {
              const c = this.tryEntries[i];
              var u = c.completion;
              if (c.tryLoc === 'root') return n('end');
              if (c.tryLoc <= this.prev) {
                const a = o.call(c, 'catchLoc');
                const s = o.call(c, 'finallyLoc');
                if (a && s) {
                  if (this.prev < c.catchLoc) return n(c.catchLoc, !0);
                  if (this.prev < c.finallyLoc) return n(c.finallyLoc);
                } else if (a) {
                  if (this.prev < c.catchLoc) return n(c.catchLoc, !0);
                } else {
                  if (!s) throw new Error('try statement without catch or finally');
                  if (this.prev < c.finallyLoc) return n(c.finallyLoc);
                }
              }
            }
          },
          abrupt(t, e) {
            for (let r = this.tryEntries.length - 1; r >= 0; --r) {
              const n = this.tryEntries[r];
              if (n.tryLoc <= this.prev && o.call(n, 'finallyLoc') && this.prev < n.finallyLoc) {
                var i = n;
                break;
              }
            }
            i &&
              (t === 'break' || t === 'continue') &&
              i.tryLoc <= e &&
              e <= i.finallyLoc &&
              (i = null);
            const c = i ? i.completion : {};
            return (
              (c.type = t),
              (c.arg = e),
              i ? ((this.method = 'next'), (this.next = i.finallyLoc), v) : this.complete(c)
            );
          },
          complete(t, e) {
            if (t.type === 'throw') throw t.arg;
            return (
              t.type === 'break' || t.type === 'continue'
                ? (this.next = t.arg)
                : t.type === 'return'
                  ? ((this.rval = this.arg = t.arg), (this.method = 'return'), (this.next = 'end'))
                  : t.type === 'normal' && e && (this.next = e),
              v
            );
          },
          finish(t) {
            for (let e = this.tryEntries.length - 1; e >= 0; --e) {
              const r = this.tryEntries[e];
              if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), E(r), v;
            }
          },
          catch(t) {
            for (let e = this.tryEntries.length - 1; e >= 0; --e) {
              const r = this.tryEntries[e];
              if (r.tryLoc === t) {
                const n = r.completion;
                if (n.type === 'throw') {
                  var o = n.arg;
                  E(r);
                }
                return o;
              }
            }
            throw new Error('illegal catch attempt');
          },
          delegateYield(t, e, n) {
            return (
              (this.delegate = { iterator: M(t), resultName: e, nextLoc: n }),
              this.method === 'next' && (this.arg = r),
              v
            );
          },
        });
    }
    function w(t, e, r, n) {
      const o = e && e.prototype instanceof b ? e : b;
      const i = Object.create(o.prototype);
      const c = new T(n || []);
      return (
        (i._invoke = (function (t, e, r) {
          let n = f;
          return function (o, i) {
            if (n === h) throw new Error('Generator is already running');
            if (n === p) {
              if (o === 'throw') throw i;
              return k();
            }
            for (r.method = o, r.arg = i; ; ) {
              const c = r.delegate;
              if (c) {
                const u = P(c, r);
                if (u) {
                  if (u === v) continue;
                  return u;
                }
              }
              if (r.method === 'next') r.sent = r._sent = r.arg;
              else if (r.method === 'throw') {
                if (n === f) throw ((n = p), r.arg);
                r.dispatchException(r.arg);
              } else r.method === 'return' && r.abrupt('return', r.arg);
              n = h;
              const a = _(t, e, r);
              if (a.type === 'normal') {
                if (((n = r.done ? p : l), a.arg === v)) continue;
                return { value: a.arg, done: r.done };
              }
              a.type === 'throw' && ((n = p), (r.method = 'throw'), (r.arg = a.arg));
            }
          };
        })(t, r, c)),
        i
      );
    }
    function _(t, e, r) {
      try {
        return { type: 'normal', arg: t.call(e, r) };
      } catch (t) {
        return { type: 'throw', arg: t };
      }
    }
    function b() {}
    function O() {}
    function x() {}
    function L(t) {
      ['next', 'throw', 'return'].forEach(function (e) {
        t[e] = function (t) {
          return this._invoke(e, t);
        };
      });
    }
    function j(t) {
      function e(r, n, i, c) {
        const u = _(t[r], t, n);
        if (u.type !== 'throw') {
          const a = u.arg;
          const s = a.value;
          return s && typeof s === 'object' && o.call(s, '__await')
            ? Promise.resolve(s.__await).then(
                function (t) {
                  e('next', t, i, c);
                },
                function (t) {
                  e('throw', t, i, c);
                },
              )
            : Promise.resolve(s).then(function (t) {
                (a.value = t), i(a);
              }, c);
        }
        c(u.arg);
      }
      let r;
      this._invoke = function (t, n) {
        function o() {
          return new Promise(function (r, o) {
            e(t, n, r, o);
          });
        }
        return (r = r ? r.then(o, o) : o());
      };
    }
    function P(t, e) {
      const n = t.iterator[e.method];
      if (n === r) {
        if (((e.delegate = null), e.method === 'throw')) {
          if (
            t.iterator.return &&
            ((e.method = 'return'), (e.arg = r), P(t, e), e.method === 'throw')
          )
            return v;
          (e.method = 'throw'),
            (e.arg = new TypeError("The iterator does not provide a 'throw' method"));
        }
        return v;
      }
      const o = _(n, t.iterator, e.arg);
      if (o.type === 'throw') return (e.method = 'throw'), (e.arg = o.arg), (e.delegate = null), v;
      const i = o.arg;
      return i
        ? i.done
          ? ((e[t.resultName] = i.value),
            (e.next = t.nextLoc),
            e.method !== 'return' && ((e.method = 'next'), (e.arg = r)),
            (e.delegate = null),
            v)
          : i
        : ((e.method = 'throw'),
          (e.arg = new TypeError('iterator result is not an object')),
          (e.delegate = null),
          v);
    }
    function S(t) {
      const e = { tryLoc: t[0] };
      1 in t && (e.catchLoc = t[1]),
        2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
        this.tryEntries.push(e);
    }
    function E(t) {
      const e = t.completion || {};
      (e.type = 'normal'), delete e.arg, (t.completion = e);
    }
    function T(t) {
      (this.tryEntries = [{ tryLoc: 'root' }]), t.forEach(S, this), this.reset(!0);
    }
    function M(t) {
      if (t) {
        const e = t[c];
        if (e) return e.call(t);
        if (typeof t.next === 'function') return t;
        if (!isNaN(t.length)) {
          let n = -1;
          const i = function e() {
            for (; ++n < t.length; ) if (o.call(t, n)) return (e.value = t[n]), (e.done = !1), e;
            return (e.value = r), (e.done = !0), e;
          };
          return (i.next = i);
        }
      }
      return { next: k };
    }
    function k() {
      return { value: r, done: !0 };
    }
  })(
    (function () {
      return this;
    })() || Function('return this')(),
  );
});
const ze =
  (function () {
    return this;
  })() || Function('return this')();
const Ke =
  ze.regeneratorRuntime && Object.getOwnPropertyNames(ze).indexOf('regeneratorRuntime') >= 0;
const Ye = Ke && ze.regeneratorRuntime;
ze.regeneratorRuntime = void 0;
const Je = Ue;
if (Ke) ze.regeneratorRuntime = Ye;
else
  try {
    delete ze.regeneratorRuntime;
  } catch (t) {
    ze.regeneratorRuntime = void 0;
  }
let Qe;
const Xe = Je;
const Ze = t(
  e(function (t, e) {
    e.__esModule = !0;
    let r;
    const n = (r = qe) && r.__esModule ? r : { default: r };
    e.default = function (t) {
      return function () {
        const e = t.apply(this, arguments);
        return new n.default(function (t, r) {
          return (function o(i, c) {
            try {
              var u = e[i](c);
              var a = u.value;
            } catch (t) {
              return void r(t);
            }
            if (!u.done)
              return n.default.resolve(a).then(
                function (t) {
                  o('next', t);
                },
                function (t) {
                  o('throw', t);
                },
              );
            t(a);
          })('next');
        });
      };
    };
  }),
);
const $e =
  ((Qe = Ze(
    Xe.mark(function t(e) {
      return Xe.wrap(
        function (t) {
          for (;;)
            switch ((t.prev = t.next)) {
              case 0:
                return t.abrupt(
                  'return',
                  new Be(function (t) {
                    (He() ? my : We() ? wx : {}).request(
                      $({}, e, {
                        timeout: 2e4,
                        success(e) {
                          const r = e.data;
                          const n = void 0 === r ? {} : r;
                          n && n.rtnCode === '9999'
                            ? t([void 0, n.data])
                            : t([n.rtnMessage, void 0]);
                        },
                        fail(e) {
                          t([e, void 0]);
                        },
                      }),
                    );
                  }),
                );
              case 1:
              case 'end':
                return t.stop();
            }
        },
        t,
        void 0,
      );
    }),
  )),
  function (t) {
    return Qe.apply(this, arguments);
  });
const tr = function (t) {
  const e = t.thirdOrderNo;
  const r = t.merchantCode;
  const n = t.token;
  const o = t.extraParams;
  try {
    $e({
      url: 'http://fusion-platform-alitest.leo.bangdao-tech.com/fusion-platform/payResult/queryPayOrder',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', Authorization: n },
      method: 'post',
      data: { thirdOrderNo: e, merchantCode: r },
    });
  } catch (t) {
    console.log('上报接口异常');
  }
  return new Be(function (t) {
    try {
      const e = o.orderStr;
      e || t([void 0, new Error('参数不正确')]);
      const r = JSON.parse(e);
      He()
        ? my.tradePay({
            tradeNO: r.alipayTradeNo,
            success(e) {
              t([e, void 0]);
            },
            fail(e) {
              t([void 0, e]);
            },
          })
        : We
          ? wx.requestPayment(
              $({}, r, {
                success(e) {
                  t([e, void 0]);
                },
                fail(e) {
                  t([void 0, e]);
                },
              }),
            )
          : t([void 0, new Error('获取不到当前环境')]);
    } catch (e) {
      t([void 0, e]);
    }
  });
};
export { tr as handleTradePay };
