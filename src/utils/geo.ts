function Rad(d: number) {
  // 根据经纬度判断距离
  return (d * Math.PI) / 180.0;
}

// 计算两个点之间的距离，单位是 km
interface Geo {
  longitude: number;
  latitude: number;
}
export function getDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
  const radLat1 = Rad(lat1);
  const radLat2 = Rad(lat2);
  const a = radLat1 - radLat2;
  const b = Rad(lon1) - Rad(lon2);
  let s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.sin(a / 2) ** 2 + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(b / 2) ** 2,
      ),
    );
  s *= 6378.137;
  s = Math.round(s * 10000) / 10000;
  s = parseFloat(s.toFixed(2)); // 保留两位小数
  console.log(`经纬度计算的距离:${s}`);
  return s;
}
