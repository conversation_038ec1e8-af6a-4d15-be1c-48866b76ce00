// const OpenAPI = require('../');
const OpenAPI = require('./serviceCode.js');
// const OpenAPI = require('service-codegen');
// const OpenAPI = require('openapi-typescript-codegen');
const tagServer = {
  '【岳阳】充电平台C端服务': '/charging-server',
  'yy-app-server': '/app-server',
};
const generate = async (input, output) => {
  await OpenAPI.generate({
    input,
    output,
    httpClient: OpenAPI.HttpClient.AXIOSCUSTOM,
    useOptions: false,
    useUnionTypes: false,
    exportCore: true,
    exportSchemas: false,
    exportModels: true,
    exportServices: true,
    functionNameFormatter: (name) => {
      return name.replace('/api', '');
    },
    functionFixPath: (path, tag) => {
      // @ts-ignore
      let base = tag ? tagServer[tag.split('/')[0]] || '' : '';
      return `${base}${path}`;
    },
    axiosInstancePath: '@/utils/http',
  });
};

const main = async () => {
  let json = await OpenAPI.getJson(
    4274375,
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTkwMzI3NywidHMiOiI3ZThiYjgyNGE5Y2FhMDk1IiwiaWF0IjoxNzA3MDM1MDA5MzI4fQ.icDN-orbZb6DnpTRcK_FyuV5AGILbUZ0YZzjVWGcgyY',
    2,
    tagServer,
  );
  await generate(json, 'src/service');
};

main();
