/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
import type { OcQuestionVO } from './OcQuestionVO';
export type PaginationResultOcQuestionVO = {
  /**
   * 成功标志
   */
  success?: boolean;
  /**
   * 响应码
   */
  code?: string;
  /**
   * 返回给前端的提示消息
   */
  msg?: string;
  /**
   * 响应数据
   */
  data?: Array<OcQuestionVO>;
  /**
   * 链路追踪
   */
  traceId?: string;
  /**
   * 页码
   */
  pageNum?: number;
  /**
   * 页面大小
   */
  pageSize?: number;
  /**
   * 查询总数，-1：标识不查询总数
   */
  total?: number;
};
