/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
export type WebSocketMessage = {
  /**
   * 前端发送消息临时标识
   */
  tmpId?: string;
  /**
   * 消息ID
   */
  recordDetailId?: string;
  /**
   * 会话唯一表示，代表用户和客服之间的一次的聊天记录汇总
   */
  recordId?: string;
  /**
   * ContentTypeEnum  内容类别
   */
  contentType?: string;
  /**
   * 消息：文本，图片URL
   */
  content?: string;
  /**
   * 接受消息的人
   */
  toUser?: string;
  /**
   * MessageTypeEnum  消息类型
   */
  messageType?: string;
  /**
   * 谁发送的
   */
  fromUser?: string;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 头像
   */
  head?: string;
  /**
   * 客服在线状态
   */
  status?: string;
  /**
   * 是否首个消息
   */
  isFirstMessage?: string;
  isRobot?: string;
};
