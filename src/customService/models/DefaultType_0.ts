/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/* generated using @soeasy/service-codegen -- do not edit */
import type { UnknownFieldSet } from './UnknownFieldSet';
export type DefaultType_0 = {
  varint?: Array<number>;
  fixed32?: Array<number>;
  fixed64?: Array<number>;
  lengthDelimited?: Array<Array<number>>;
  group?: Array<UnknownFieldSet>;
};
