/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { OcQuestionDTO } from '../models/OcQuestionDTO';
import type { OnlineEvaluateDTO } from '../models/OnlineEvaluateDTO';
import type { PaginationResultOcQuestionVO } from '../models/PaginationResultOcQuestionVO';
import type { QuestionEvaluateDTO } from '../models/QuestionEvaluateDTO';
import type { RestResultCustomerUserVO } from '../models/RestResultCustomerUserVO';
import type { RestResultEmpty } from '../models/RestResultEmpty';
import type { RestResultListDictDataVO } from '../models/RestResultListDictDataVO';
import type { RestResultListOnlineChatRecordVO } from '../models/RestResultListOnlineChatRecordVO';
import type { RestResultLong } from '../models/RestResultLong';
import type { RestResultOcEvaluateSet } from '../models/RestResultOcEvaluateSet';
import type { RestResultString } from '../models/RestResultString';
import type { WebSocketMessage } from '../models/WebSocketMessage';
import type { WechatOnlineRecordListDTO } from '../models/WechatOnlineRecordListDTO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class CTerminalOnlineCustomerServiceRelatedInterfacesService {
  /**
   * user
   * 这是一个测试
   * @returns RestResultCustomerUserVO
   * @throws ApiError
   */
  public static getCustomerOnlineUser(
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultCustomerUserVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/customer-service/customer/online/user',
      },
    );
  }

  /**
   * 获取客服系统-联系电话
   * @returns RestResultString
   * @throws ApiError
   */
  public static getOpenOnlineGetAppPhone(
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/customer-service/open/online/getAppPhone',
      },
    );
  }

  /**
   * chatRecord
   * @param requestBody
   * @returns RestResultListOnlineChatRecordVO
   * @throws ApiError
   */
  public static postCustomerOnlineChatRecord(
    requestBody?: WechatOnlineRecordListDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultListOnlineChatRecordVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/chatRecord',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * getEvaluateSet
   * @returns RestResultOcEvaluateSet
   * @throws ApiError
   */
  public static postCustomerOnlineGetEvaluateSet(
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultOcEvaluateSet]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/getEvaluateSet',
      },
    );
  }

  /**
   * evaluate
   * @param requestBody
   * @returns RestResultString
   * @throws ApiError
   */
  public static postCustomerOnlineEvaluate(
    requestBody?: OnlineEvaluateDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/evaluate',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * questionList
   * @param requestBody
   * @returns PaginationResultOcQuestionVO
   * @throws ApiError
   */
  public static postCustomerOnlineQuestionList(
    requestBody?: OcQuestionDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | PaginationResultOcQuestionVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/questionList',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * imageAudit
   * @param requestBody
   * file
   * @returns RestResultString
   * @throws ApiError
   */
  public static postCustomerOnlineImageAudit(
    requestBody?: {
      file: Blob;
    },
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/imageAudit',
        body: requestBody || {},
        mediaType: 'multipart/form-data',
      },
    );
  }

  /**
   * textAudit
   * @param requestBody
   * @returns RestResultEmpty
   * @throws ApiError
   */
  public static postCustomerOnlineTextAudit(
    requestBody?: WebSocketMessage,
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultEmpty]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/textAudit',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取在线客服问题反馈类型
   * @returns RestResultListDictDataVO
   * @throws ApiError
   */
  public static postCustomerOnlineGetQuestionFeedback(
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultListDictDataVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/getQuestionFeedback',
      },
    );
  }

  /**
   * 自动回复评价
   * @param requestBody
   * @returns RestResultLong
   * @throws ApiError
   */
  public static postCustomerOnlineQuestionEvaluate(
    requestBody?: QuestionEvaluateDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultLong]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/customer-service/customer/online/questionEvaluate',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取客服系统-联系电话
   * @returns RestResultString
   * @throws ApiError
   */
  public static getCustomerOnlineGetAppPhone(
    config = {},
  ): Promise<[undefined | Error, undefined | RestResultString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/customer-service/customer/online/getAppPhone',
      },
    );
  }
}
