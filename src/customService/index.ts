/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
export { ApiError } from './core/ApiError';
export { apiConfig } from './core/APIConfig';
export type { APIConfig } from './core/APIConfig';

export type { CustomerUserVO } from './models/CustomerUserVO';
export type { DefaultType_0 } from './models/DefaultType_0';
export type { DictDataVO } from './models/DictDataVO';
export type { Empty } from './models/Empty';
export type { MapField } from './models/MapField';
export type { OcEvaluateSet } from './models/OcEvaluateSet';
export type { OcQuestionDTO } from './models/OcQuestionDTO';
export type { OcQuestionVO } from './models/OcQuestionVO';
export type { OnlineChatRecordVO } from './models/OnlineChatRecordVO';
export type { OnlineEvaluateDTO } from './models/OnlineEvaluateDTO';
export type { PaginationResultOcQuestionVO } from './models/PaginationResultOcQuestionVO';
export type { QuestionEvaluateDTO } from './models/QuestionEvaluateDTO';
export type { RestResultCustomerUserVO } from './models/RestResultCustomerUserVO';
export type { RestResultEmpty } from './models/RestResultEmpty';
export type { RestResultListDictDataVO } from './models/RestResultListDictDataVO';
export type { RestResultListOnlineChatRecordVO } from './models/RestResultListOnlineChatRecordVO';
export type { RestResultLong } from './models/RestResultLong';
export type { RestResultOcEvaluateSet } from './models/RestResultOcEvaluateSet';
export type { RestResultString } from './models/RestResultString';
export type { UnknownFieldSet } from './models/UnknownFieldSet';
export type { WebSocketMessage } from './models/WebSocketMessage';
export type { WechatOnlineRecordListDTO } from './models/WechatOnlineRecordListDTO';

export { CTerminalOnlineCustomerServiceRelatedInterfacesService } from './services/CTerminalOnlineCustomerServiceRelatedInterfacesService';
