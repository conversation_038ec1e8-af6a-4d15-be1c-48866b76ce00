/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponsePageRespVOUserReservationRecordVO } from '../models/AppResponsePageRespVOUserReservationRecordVO';
import type { AppResponseReservationCommonConfigModel } from '../models/AppResponseReservationCommonConfigModel';
import type { AppResponseUserReservationRecordDetail } from '../models/AppResponseUserReservationRecordDetail';
import type { AppResponseVoid } from '../models/AppResponseVoid';
import type { ReservationApplyDTO } from '../models/ReservationApplyDTO';
import type { ReservationCancelDTO } from '../models/ReservationCancelDTO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ParkingReserveService {
  /**
   * 查询预约通用配置
   * 这是一个测试
   * @returns AppResponseReservationCommonConfigModel
   * @throws ApiError
   */
  public static getReservationGetCommonConfig(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseReservationCommonConfigModel]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/parking-platform/api/v1/reservation/getCommonConfig',
      },
    );
  }

  /**
   * 提交预约申请
   * @param requestBody
   * @returns AppResponseVoid
   * @throws ApiError
   */
  public static postReservationApply(
    requestBody?: ReservationApplyDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseVoid]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/parking-platform/api/v1/reservation/apply',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询预约记录分页列表
   * @param status 预约状态
   * 0 :初始
   * 1 :已预约
   * 2 :已取消
   * 3 :已爽约
   * 4 :已履约
   * @param page
   * @param pageSize
   * @returns AppResponsePageRespVOUserReservationRecordVO
   * @throws ApiError
   */
  public static postReservationQueryRecordPage(
    {
      status,
      page = 1,
      pageSize = 10,
    }: {
      /**
       * 预约状态
       * 0 :初始
       * 1 :已预约
       * 2 :已取消
       * 3 :已爽约
       * 4 :已履约
       */
      status: 0 | 1 | 2 | 3 | 4;
      page: number;
      pageSize: number;
    },
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePageRespVOUserReservationRecordVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/parking-platform/api/v1/reservation/queryRecordPage',
        query: {
          page: page,
          pageSize: pageSize,
          status: status,
        },
      },
    );
  }

  /**
   * 查询预约记录详情
   * @param recordId
   * @returns AppResponseUserReservationRecordDetail
   * @throws ApiError
   */
  public static getReservationDetail(
    recordId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserReservationRecordDetail]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/parking-platform/api/v1/reservation/detail',
        query: {
          recordId: recordId,
        },
      },
    );
  }

  /**
   * 取消预约
   * @param requestBody
   * @returns AppResponseVoid
   * @throws ApiError
   */
  public static postReservationCancel(
    requestBody?: ReservationCancelDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseVoid]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/parking-platform/api/v1/reservation/cancel',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
