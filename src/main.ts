import uvUI from '@climblee/uv-ui';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import * as <PERSON><PERSON> from 'pinia';
import { createSSRApp } from 'vue';
import App from './App.vue';
import store from './store';

import '@/style/index.scss';
import 'virtual:svg-icons-register';
import 'virtual:uno.css';
import { routeInterceptor } from './interceptors';

// eslint-disable-next-line import/order
import PageSpy from '@huolala-tech/page-spy-uniapp';

dayjs.extend(isBetween);

// eslint-disable-next-line no-new
// const pageSpy: any = new PageSpy({
//   project: '汕头小程序',
//   api: 'spy.jscoder.com',
// });
// const { roomUrl } = pageSpy;
// const regex = /address=([^&]+)/;
// const match = roomUrl.match(regex);
// if (match && match[1]) {
//   const address = match[1];
//   console.error('🤡🤡🤡roomId:', address.slice(0, 4));
// }

export function createApp() {
  const app = createSSRApp(App);
  app.use(store);
  app.use(uvUI);
  app.use(routeInterceptor);
  return {
    app,
    Pinia,
  };
}
