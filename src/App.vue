<script setup lang="ts">
import { useUserStore } from '@/store';
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app';
import { storeToRefs } from 'pinia';
import { commonScan } from './utils/scanHandler';

const { isLogin } = storeToRefs(useUserStore());
const { getUserStatus } = useUserStore();
watch(
  () => isLogin.value,
  async () => {
    if (isLogin.value) {
      await getUserStatus(true, true);
    }
  },
  {
    immediate: true,
  },
);
onLaunch(async (options: any) => {
  console.error('onLaunch', options);
  // #ifdef MP-ALIPAY
  if (options?.query?.qrCode) {
    commonScan(options);
  }
  // #endif
});
onShow(async (options: any) => {
  console.error('onShow', options);
  console.error('onShow111', options);
  // #ifdef MP-WEIXIN
  console.error('onShow222', options);
  if (options?.query?.q) {
    console.error('onShow333', options);
    commonScan(options);
  }
  // #endif
});
onHide(() => {});
</script>
<script lang="ts">
// 全局分享配置
export default {
  // #ifdef MP-ALIPAY
  onShareAppMessage() {
    return {
      title: import.meta.env.VITE_APP_TITLE,
      desc: import.meta.env.VITE_APP_DESC,
      path: '/pages/home/<USER>',
    };
  },
  // #endif
};
</script>

<style lang="scss">
page {
  background-color: #f6f7f9;
}

.common-header-bg {
  @apply fixed top-0 left-0 w-full h-full z-1;

  // background-image: linear-gradient(180deg, #ffd3ca 0%, rgba(246, 247, 249, 0) 100%);
  background-image: url('#{$oss-prefix}/images/common/common-header-bg.png');
  background-size: 100% 100%;
}

.common-page-bg {
  background: linear-gradient(180deg, #e6f6e8 0%, rgb(230 246 233 / 0%) 49%), #f5f5f5;
  background-repeat: no-repeat;
  background-size: 100vw 1624rpx;
}

:deep(.wd-progress .wd-progress__outer) {
  height: 24rpx !important;
  overflow: hidden;
  background-color: #f0f0f0 !important;
  border-radius: 109rpx !important;
}

.detail-progress :deep(.wd-progress .wd-progress__outer) {
  background-color: #f8f8f8 !important;
}

:deep(.wd-progress .wd-progress__inner) {
  overflow: hidden;
  border-radius: 109rpx !important;
}

:deep(.wd-curtain__content-close.bottom) {
  bottom: -100rpx !important;
  width: 60rpx !important;
  height: 60rpx !important;
  padding: 0 !important;
  background-image: url('#{$oss-prefix}/images/common/popup/ad-close.png') !important;
  background-size: 100% 100%;

  &::before {
    content: '' !important;
  }
}

:deep(radio) {
  margin: 0 !important;

  .wx-radio-input {
    background-color: transparent !important;
  }

  .wx-radio-input-checked {
    @apply !bg-brand-primary;
  }
}

:deep(.wd-picker__action) {
  @apply !text-brand-primary;

  &.wd-picker__action--cancel {
    @apply !text-text-sub;
  }
}

@font-face {
  font-family: D-DIN;
  font-weight: bold;
  src: url('#{$oss-prefix}/fonts/D-DIN-Bold.ttf') format('truetype');

  // src: url('https://donghu-prod-oss.whggzk.com/mini/static/fonts/D-DINCondensed-Bold.ttf')
  //   format('truetype');
}
</style>
