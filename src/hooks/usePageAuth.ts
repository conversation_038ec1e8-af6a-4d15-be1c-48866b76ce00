import { useUserStore } from '@/store';
import { pageAuth, whiteList } from '../../config/auth';
import { usePageRouteInfo } from './usePageRouteInfo';
/**
 * @description: 页面权限 TODO: 如果登录则根据角色判断是否有权限，未登录只能访问白名单页面
 * @param {string} role
 * @return {*}
 */
export const usePageAuth = (role: string | undefined) => {
  const userStore = useUserStore();
  // 是否登录
  const { isLogin } = userStore;
  const { route } = usePageRouteInfo();
  console.log('route', userStore.userInfo, isLogin, route);

  const isAuth = computed(() => {
    if (!isLogin) {
      return whiteList.some((item) => {
        if (item instanceof RegExp) {
          return item.test(route);
        }
        return item.split('?')[0] === route;
      });
    }

    const routes = pageAuth.find((item) => item.role === role)?.routes;
    const auth = routes?.some((item) => {
      if (item instanceof RegExp) {
        return item.test(route);
      }
      return item.split('?')[0] === route;
    });
    return auth;
  });

  return { isAuth, isLogin };
};
