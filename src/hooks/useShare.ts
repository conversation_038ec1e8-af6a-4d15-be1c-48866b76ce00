import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app';

interface ShareOptions {
  title?: string; // 分享标题
  path?: string; // 分享路径
  imageUrl?: string; // 分享图片
  desc?: string; // 分享描述(仅支持部分平台)
  content?: string; // 分享内容(仅支持部分平台)
}

/**
 * 统一的分享 Hook
 * @param options 分享配置项
 */
export function useShare(options?: any) {
  const pages = getCurrentPages();
  const currentPage = pages?.[pages.length - 1];
  const shareInfo = computed(() => {
    return {
      title: import.meta.env.VITE_APP_TITLE,
      // @ts-ignore
      path: currentPage?.$page?.fullPath,
      desc: import.meta.env.VITE_APP_DESC,
      ...(options?.value || {}),
    };
  });
  // 分享给朋友
  onShareAppMessage(() => shareInfo.value);

  // 分享到朋友圈
  onShareTimeline(() => shareInfo.value);
  return {
    onShareAppMessage,
    onShareTimeline,
  };
}
