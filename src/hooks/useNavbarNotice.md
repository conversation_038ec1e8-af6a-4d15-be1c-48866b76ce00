ios app getSystemInfo 的结果
```json
{
    "SDKVersion": "",
    "appId": "__UNI__91BEB0E",
    "appLanguage": "zh-Hans",
    "appName": "",
    "appVersion": "14.14",
    "appVersionCode": "1414",
    "appWgtVersion": "1.0.0",
    "brand": "apple",
    "browserName": "wkwebview",
    "browserVersion": "17.4",
    "deviceBrand": "apple",
    "deviceId": "FF086265E981AF69EDC92AA5F7C37435",
    "deviceModel": "iPhone Simulator",
    "deviceOrientation": "portrait",
    "devicePixelRatio": 3,
    "deviceType": "phone",
    "language": "zh-Hans-CN",
    "model": "iPhone Simulator",
    "osLanguage": "zh-Hans-CN",
    "osName": "ios",
    "osTheme": "light",
    "osVersion": "17.4",
    "pixelRatio": 3,
    "platform": "ios",
    "safeArea": {
        "left": 0,
        "right": 393,
        "top": 59,
        "bottom": 768,
        "width": 393,
        "height": 709
    },
    "safeAreaInsets": {
        "top": 59,
        "right": 0,
        "bottom": 0,
        "left": 0
    },
    "screenHeight": 852,
    "screenWidth": 393,
    "statusBarHeight": 59,
    "system": "iOS 17.4",
    "ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Html5Plus/1.0 (Immersed/20) uni-app",
    "uniCompileVersion": "4.14",
    "uniPlatform": "app",
    "uniRuntimeVersion": "4.14",
    "version": "1.9.9.81342",
    "windowBottom": 0,
    "windowHeight": 768,
    "windowTop": 0,
    "windowWidth": 393
}
```
