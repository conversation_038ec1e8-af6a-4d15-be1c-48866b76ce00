export const usePageRequest = (
  requestApi: any,
  watchRefresh: boolean = true,
  initQuery: boolean = false,
  pageSize: number = 10,
  requestConfig: any = {},
) => {
  const pageNum = ref(0);
  const hasMore = ref(false);
  const list = ref<any[]>([]);
  const requestList = ref<any[]>([]);
  const isQuerying = ref(false);
  const error = ref(false);
  const totalNum = ref(0);
  const getData = async () => {
    if (!requestApi.value.request) return;
    if (pageNum.value && (!hasMore.value || isQuerying.value)) return;
    isQuerying.value = true;
    error.value = false;
    const extraParams = requestApi.value.businessParams || {};
    const [err, res] = await requestApi.value.request(
      {
        page: pageNum.value + 1,
        pageSize,
        ...extraParams,
      },
      { ...requestConfig },
    );
    isQuerying.value = false;
    if (err) {
      error.value = true;
      if (!pageNum.value) {
        list.value = [];
      }
      return;
    }
    if (res?.data?.dataList?.length) {
      console.log('res.data.dataList', res.data.dataList, list.value);

      pageNum.value++;
      totalNum.value = res.data.totalNum;
      if (pageNum.value <= 1) {
        list.value = res.data.dataList;
      } else {
        list.value = [...list.value, ...res.data.dataList];
      }
      requestList.value = res.data.dataList;
    } else if (!pageNum.value) {
      list.value = [];
    }
    hasMore.value = res.data.hasMore;
  };
  if (initQuery) {
    getData();
  }
  watch(
    () => requestApi.value,
    () => {
      console.error('requestApi', requestApi.value);
      if (watchRefresh) {
        refreshList();
      }
    },
    { deep: true },
  );
  const refreshList = () => {
    pageNum.value = 0;
    list.value = [];
    hasMore.value = false;
    error.value = false;
    getData();
  };
  return {
    pageNum,
    hasMore,
    list,
    requestList,
    isQuerying,
    error,
    totalNum,
    getData,
    refreshList,
  };
};
