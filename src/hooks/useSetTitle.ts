import { setNavigationBarTitle } from '@/utils/jsapi';

const { VITE_APP_TITLE } = import.meta.env;

export const useSetTitle = (initialTitle: string = VITE_APP_TITLE) => {
  const title = ref(initialTitle);

  const setTitle = (newTitle: string) => {
    title.value = newTitle;
  };

  onLoad(() => {
    setNavigationBarTitle(title.value);
  });

  watch(title, (newTitle) => {
    setNavigationBarTitle(title.value);
  });

  return { title, setTitle };
};
