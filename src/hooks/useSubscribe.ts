interface IAliErr {
  error: number;
  errorMessage: string;
  behavior?: string;
  keep?: boolean;
  refuse?: boolean;
  result?: ISubscribeRes;
  show?: boolean;
}
interface ISubscribeRes {
  subscribeEntityIds: string[];
  subscribedEntityIds: string[];
  unsubscribedEntityIds: string[];
  currentSubscribedEntityIds: string[];
  entityList: string[];
}

export interface subscribeItem {
  entityId: string;
  status: '0' | '1' | '2';
}

interface SubscribeMessageRes {
  chooseNoRemind: boolean; // 是否勾选不再提醒
  show: boolean; // 面板是否弹出来
  subscribeIds: subscribeItem[];
}

/**
 * @description: 支付宝订阅
 * @description: 订阅  status 0 拒绝  1 本次成功  2 历史成功
 * @description: https://opendocs.alipay.com/mini/api/requestSubscribeMessage?pathHash=b41c4bb6
 * @param {*} ids
 * @return {*}
 */
export function alipaySubscribe(
  ids: string[],
): Promise<[undefined | IAliErr, undefined | SubscribeMessageRes]> {
  return new Promise((resolve, reject) => {
    my.requestSubscribeMessage({
      entityIds: ids,
      success: (res: any) => {
        const { keep, refuse, result, show } = res;
        const {
          currentSubscribedEntityIds, // 本次订阅成功的模版列表
          unsubscribedEntityIds, // 未订阅的模版列表
        } = result;
        const chooseNoRemind = keep || refuse;
        const subscribeIds: subscribeItem[] = ids.map((id) => {
          if (unsubscribedEntityIds.includes(id)) {
            return {
              entityId: id,
              status: '0',
            };
          } else if (currentSubscribedEntityIds.includes(id)) {
            return { status: '1', entityId: id };
          } else {
            return { status: '2', entityId: id };
          }
        });
        resolve([undefined, { chooseNoRemind, show, subscribeIds }]);
      },
      fail: (err: any) => {
        resolve([err, undefined]);
      },
    });
  });
}

interface IWxErr {
  errMsg: string;
  errCode: number;
}

/**
 * @description: 微信订阅
 * @description: https://developers.weixin.qq.com/miniprogram/dev/api/open-api/subscribe-message/wx.requestSubscribeMessage.html
 * @param {*} ids
 * @return {*}
 */
export function wxSubscribe(
  ids: string[],
): Promise<[IWxErr | undefined, undefined | SubscribeMessageRes]> {
  return new Promise((resolve, reject) => {
    wx.requestSubscribeMessage({
      tmplIds: ids,
      success(res) {
        const subscribeIds = ids.map((item) => {
          return {
            entityId: item,
            status: res[item] === 'accept' ? '1' : '0',
          };
        });
        resolve([undefined, { chooseNoRemind: false, show: true, subscribeIds }]);
      },
      fail(err) {
        resolve([err, undefined]);
      },
    });
  });
}

/**
 * @description: 消息订阅
 * @param {string} ids
 * @param {*} forceCall
 * @return {*}
 */
export async function subscribeMessage(
  ids: string[],
): Promise<[IWxErr | IAliErr | undefined, undefined | SubscribeMessageRes]> {
  let err;
  let res;
  // #ifdef MP-WEIXIN
  [err, res] = await wxSubscribe(ids);
  // #endif
  // #ifdef MP-ALIPAY
  [err, res] = await alipaySubscribe(ids);
  // #endif
  return [err, res];
}

interface ISubscribeOptions {
  ids: string[];
  api?: (params: any) => void;
  params?: Record<string, any>;
  apiKey?: string;
}

/**
 * @description: 订阅接口
 * @param {ISubscribeOptions} options
 * @param options.ids 订阅模版id
 * @param options.api 订阅服务端接口
 * @param options.params 订阅接口扩展字段
 * @param options.apiKey 订阅接口模版id字段 默认 templateIds
 * @return {*}
 */
export async function useSubscribe(options: ISubscribeOptions) {
  const { ids = [], api, params = {}, apiKey = '' } = options;
  if (ids.length === 0) return;
  const [err, res] = await subscribeMessage(options.ids);
  if (api && res) {
    const { subscribeIds } = res;
    const templateIds = subscribeIds.reduce((pre, cur) => {
      if (cur.status !== '0') {
        pre = [...pre, cur.entityId];
        return pre;
      }
      return pre;
    }, [] as string[]);
    if (templateIds.length === 0) return;
    const data = {
      ...params,
    };
    if (apiKey) {
      data[apiKey] = templateIds;
    } else {
      data.templateIds = templateIds;
    }
    api(data);
  }
}
