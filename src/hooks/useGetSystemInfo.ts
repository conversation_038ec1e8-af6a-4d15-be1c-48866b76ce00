export const useGetSystemInfo = () => {
  // rpx/px比例
  const systemInfo = ref(uni.getSystemInfoSync());
  console.log('systemInfo', systemInfo);
  const ratio = ref(systemInfo.value.windowWidth ? 750 / systemInfo.value.windowWidth : 2);

  const { statusBarHeight = 0 } = systemInfo.value;
  let { titleBarHeight = 0 } = systemInfo.value;

  if (process.env.UNI_PLATFORM === 'mp-weixin') {
    const { height, top } = uni.getMenuButtonBoundingClientRect();
    if (titleBarHeight === 0) {
      titleBarHeight = height + (top - statusBarHeight) * 2;
    }
  }

  const px2rpx = (px: number) => {
    return px * ratio.value;
  };

  const rpx2px = (rpx: number) => {
    return rpx / ratio.value;
  };

  return {
    barHeight: statusBarHeight + titleBarHeight,
    px2rpx,
    rpx2px,
    ratio,
    systemInfo,
  };
};
