import { isEmpty, isObject, isFunction } from '@/utils/is';

/**
 * @description: 错误统一处理 hook
 * @param {Function} callback 页面加载回调 - init
 * @param {Ref} defaultName 页面layout名称
 * @return {*}
 */
export const useErrorHandle = (callback: any, layout: Ref) => {
  const IsError = ref(false); // 是否出错
  const originLayout = layout.value;

  /**
   * @description: 页面加载
   * @param {*} options 页面参数
   * @return {*}
   */
  onLoad(async (options) => {
    if (!isFunction(callback)) {
      return;
    }
    try {
      await callback(options);
    } catch (error) {
      if (!isEmpty(error) && isObject(error)) {
        uni.setStorageSync('ErrorMaskConfig', JSON.stringify(error));
        console.log('页面加载错误', error);
      }

      IsError.value = true;
      uni.$on('refreshPage', async (loading: Ref) => {
        await reFresh();
        loading.value = false;
      });
      layout.value = 'error';
    }
  });

  /**
   * @description: 刷新页面
   * @param {*}
   * @return {*}
   */
  const reFresh = async () => {
    try {
      await callback();
      IsError.value = false;
      uni.$off('refreshPage');
      layout.value = originLayout;
      uni.removeStorage({ key: 'ErrorMaskConfig' });
    } catch (error) {
      IsError.value = true;
      layout.value = 'error';
    }
  };

  /**
   * @description: 移除事件
   * @param {*}
   * @return {*}
   */
  const offEvents = () => {
    if (IsError.value) {
      uni.$off('refreshPage');
      uni.removeStorage({ key: 'ErrorMaskConfig' });
    }
  };

  // TODO: 添加页面回退卸载事件
  onBackPress(() => {
    offEvents();
    return true;
  });
  onUnload(() => {
    offEvents();
  });

  return {
    onLoad,
    IsError,
    reFresh,
  };
};
