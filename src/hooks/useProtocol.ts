import { ProtocolRelatedService } from '@/service';
import { useRichTextStore } from '@/store';
import { onProtocolItemClick } from '@/utils/protocolClick';
import { ref } from 'vue';

export function useProtocol(codes: string[]) {
  const { setContent } = useRichTextStore();
  const protocolList = ref<any[]>([]);

  const fetchProtocolList = async () => {
    const [err, res] = await ProtocolRelatedService.postProtocolList({ codes });
    if (res?.data) {
      protocolList.value = res.data.map((item: any) => ({
        id: item.id,
        code: item.code,
        name: item.name,
        type: item.type,
        linkUrl: item.linkUrl,
        content: item.content,
        version: item.version,
      }));
    }
    return protocolList.value;
  };

  const onProtocolClick = async (index: number) => {
    const item = protocolList.value[index];
    onProtocolItemClick(item);
  };

  onMounted(() => {
    fetchProtocolList();
  });

  return {
    protocolList,
    fetchProtocolList,
    onProtocolClick,
  };
}
