// 检查是否登录了，未登录跳转登录页面
import { useUserStore } from '@/store';
import { goTo } from '@/utils/route';

export const checkLogin = (redirectUrl = '') => {
  const userStore = useUserStore();
  // 是否登录
  const { isLogin } = userStore;
  return new Promise((resolve, reject) => {
    if (isLogin) {
      resolve(true);
    } else {
      if (redirectUrl) {
        uni.redirectTo({
          url: `/pages/login/index/index?redirect=${encodeURIComponent(redirectUrl)}`,
        });
      } else {
        goTo('/pages/login/index/index');
      }
      reject(false);
    }
  });
};
