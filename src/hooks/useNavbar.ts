import { getIsTabbar } from '@/utils';

export default () => {
  // 获取页面栈
  const pages = getCurrentPages();
  const isTabBar = getIsTabbar();
  const transparency = ref(0);

  // 页面滚动到底部时的操作，通常用于加载更多数据
  const onScrollToLower = () => {};
  // 获取屏幕边界到安全区域距离
  const data = uni.getSystemInfoSync();
  const { safeAreaInsets, statusBarHeight = 0, osName, windowHeight, windowWidth } = data;
  let { titleBarHeight = 0 } = data;

  console.log('getSystemInfoSync', data);

  if (process.env.UNI_PLATFORM === 'mp-weixin') {
    const { height, top } = uni.getMenuButtonBoundingClientRect();
    if (titleBarHeight === 0) {
      titleBarHeight = height + (top - statusBarHeight) * 2;
    }
  } else if (process.env.UNI_PLATFORM === 'app') {
    if (osName === 'android') {
      titleBarHeight = 46;
    } else {
      titleBarHeight = 44;
    }
  }

  onPageScroll((e) => {
    const opacity = e.scrollTop / 300;
    transparency.value = opacity > 1 ? 1 : opacity;
  });

  return {
    barHeight: statusBarHeight + titleBarHeight,
    windowHeight,
    statusBarHeight,
    titleBarHeight,
    pages,
    isTabBar,
    onScrollToLower,
    safeAreaInsets,
    transparency,
    radio: windowWidth / 750,
  };
};
