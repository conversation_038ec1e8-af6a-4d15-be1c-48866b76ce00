/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
export { ApiError } from './core/ApiError';
export { apiConfig } from './core/APIConfig';
export type { APIConfig } from './core/APIConfig';

export type { ActiveVipGoodsRequest } from './models/ActiveVipGoodsRequest';
export { AdvancedAuthRsp } from './models/AdvancedAuthRsp';
export type { AgainPayRequest } from './models/AgainPayRequest';
export type { AlipayEncryptContentReq } from './models/AlipayEncryptContentReq';
export type { AppFilingInfoFacadeVO } from './models/AppFilingInfoFacadeVO';
export type { AppParkingInfoFacadeVO } from './models/AppParkingInfoFacadeVO';
export type { AppParkingListDTO } from './models/AppParkingListDTO';
export type { AppResponse } from './models/AppResponse';
export type { AppResponse_ } from './models/AppResponse_';
export type { AppResponse_AdvancedAuthRsp_ } from './models/AppResponse_AdvancedAuthRsp_';
export type { AppResponse_BalanceQueryRespVO_ } from './models/AppResponse_BalanceQueryRespVO_';
export type { AppResponse_BalanceWithdrawRespVO_ } from './models/AppResponse_BalanceWithdrawRespVO_';
export type { AppResponse_BusinessChargingViewRespVO_ } from './models/AppResponse_BusinessChargingViewRespVO_';
export type { AppResponse_BusinessParkingViewRespVO_ } from './models/AppResponse_BusinessParkingViewRespVO_';
export type { AppResponse_BusinessResourceOverviewRepsVO_ } from './models/AppResponse_BusinessResourceOverviewRepsVO_';
export type { AppResponse_ChargingRevenueVO_ } from './models/AppResponse_ChargingRevenueVO_';
export type { AppResponse_CloseParkingRevenueVO_ } from './models/AppResponse_CloseParkingRevenueVO_';
export type { AppResponse_CouponTypeModifyRespVO_ } from './models/AppResponse_CouponTypeModifyRespVO_';
export type { AppResponse_CreateVipOrderResponse_ } from './models/AppResponse_CreateVipOrderResponse_';
export type { AppResponse_EnterpriseStationPageQueryRespVO_ } from './models/AppResponse_EnterpriseStationPageQueryRespVO_';
export type { AppResponse_FeedbackResp_ } from './models/AppResponse_FeedbackResp_';
export type { AppResponse_FileDownloadRespDTO_ } from './models/AppResponse_FileDownloadRespDTO_';
export type { AppResponse_GetVipActiveStatusResponse_ } from './models/AppResponse_GetVipActiveStatusResponse_';
export type { AppResponse_InformationContentRespVO_ } from './models/AppResponse_InformationContentRespVO_';
export type { AppResponse_InformationSummaryRespVO_ } from './models/AppResponse_InformationSummaryRespVO_';
export type { AppResponse_InvoiceTitleResponse_ } from './models/AppResponse_InvoiceTitleResponse_';
export type { AppResponse_List_EnterpriseListQueryRespVO_ } from './models/AppResponse_List_EnterpriseListQueryRespVO_';
export type { AppResponse_List_InformationCategoryRespVO_ } from './models/AppResponse_List_InformationCategoryRespVO_';
export type { AppResponse_List_InformationColumnRespVO_ } from './models/AppResponse_List_InformationColumnRespVO_';
export type { AppResponse_List_ListBenefitGoodsResponse_ } from './models/AppResponse_List_ListBenefitGoodsResponse_';
export type { AppResponse_List_ListInvoiceOrdersResponse_ } from './models/AppResponse_List_ListInvoiceOrdersResponse_';
export type { AppResponse_List_MallInvoiceOrderRes_ } from './models/AppResponse_List_MallInvoiceOrderRes_';
export type { AppResponse_List_OnlineGameAreaInfo_ } from './models/AppResponse_List_OnlineGameAreaInfo_';
export type { AppResponse_List_ProblemConfigRespVO_ } from './models/AppResponse_List_ProblemConfigRespVO_';
export type { AppResponse_List_RightsFlowDetailVO_ } from './models/AppResponse_List_RightsFlowDetailVO_';
export type { AppResponse_List_StationMapGroupRespVO_ } from './models/AppResponse_List_StationMapGroupRespVO_';
export type { AppResponse_ListBenefitGoodsResponse_ } from './models/AppResponse_ListBenefitGoodsResponse_';
export type { AppResponse_MallRevenueVO_ } from './models/AppResponse_MallRevenueVO_';
export type { AppResponse_MapDataQueryRespVO_ } from './models/AppResponse_MapDataQueryRespVO_';
export type { AppResponse_MerchantFlowFileGenerateRespDTO_ } from './models/AppResponse_MerchantFlowFileGenerateRespDTO_';
export type { AppResponse_NearBikeInfoModel_ } from './models/AppResponse_NearBikeInfoModel_';
export type { AppResponse_OrderDetailQueryRespVO_ } from './models/AppResponse_OrderDetailQueryRespVO_';
export type { AppResponse_OrderPageQueryRespVO_ } from './models/AppResponse_OrderPageQueryRespVO_';
export type { AppResponse_PageRespVO_FeedbackResp_ } from './models/AppResponse_PageRespVO_FeedbackResp_';
export type { AppResponse_PageRespVO_InvoiceTitleResponse_ } from './models/AppResponse_PageRespVO_InvoiceTitleResponse_';
export type { AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_ } from './models/AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_';
export type { AppResponse_PageRespVO_ListVipGoodsResponse_ } from './models/AppResponse_PageRespVO_ListVipGoodsResponse_';
export type { AppResponse_PageRespVO_MallInvoiceOrderRes_ } from './models/AppResponse_PageRespVO_MallInvoiceOrderRes_';
export type { AppResponse_PageRespVO_PrizeQueryRespVO_ } from './models/AppResponse_PageRespVO_PrizeQueryRespVO_';
export type { AppResponse_PageRespVO_WalletRecordLogRespVO_ } from './models/AppResponse_PageRespVO_WalletRecordLogRespVO_';
export type { AppResponse_PayChargeRespVO_ } from './models/AppResponse_PayChargeRespVO_';
export type { AppResponse_PileCheckRespVO_ } from './models/AppResponse_PileCheckRespVO_';
export type { AppResponse_PrizeCountRespVO_ } from './models/AppResponse_PrizeCountRespVO_';
export type { AppResponse_PrizeInfoResponse_ } from './models/AppResponse_PrizeInfoResponse_';
export type { AppResponse_PrizeQueryRespVO_ } from './models/AppResponse_PrizeQueryRespVO_';
export type { AppResponse_ProcessQueryRespVO_ } from './models/AppResponse_ProcessQueryRespVO_';
export type { AppResponse_QrcodeScanRespVO_ } from './models/AppResponse_QrcodeScanRespVO_';
export type { AppResponse_RevenueOverviewVO_ } from './models/AppResponse_RevenueOverviewVO_';
export type { AppResponse_RightsCountQueryResponse_ } from './models/AppResponse_RightsCountQueryResponse_';
export type { AppResponse_RightsFlowResponse_ } from './models/AppResponse_RightsFlowResponse_';
export type { AppResponse_RightsPreviewFlowResponse_ } from './models/AppResponse_RightsPreviewFlowResponse_';
export type { AppResponse_RoadParkingRevenueVO_ } from './models/AppResponse_RoadParkingRevenueVO_';
export type { AppResponse_SilentAuthRsp_ } from './models/AppResponse_SilentAuthRsp_';
export type { AppResponse_StartChargeRespVO_ } from './models/AppResponse_StartChargeRespVO_';
export type { AppResponse_StationCollectPageQueryRespVO_ } from './models/AppResponse_StationCollectPageQueryRespVO_';
export type { AppResponse_StationDetailQueryRespVO_ } from './models/AppResponse_StationDetailQueryRespVO_';
export type { AppResponse_StationFeeQueryRespVO_ } from './models/AppResponse_StationFeeQueryRespVO_';
export type { AppResponse_StationGunsQueryRespVO_ } from './models/AppResponse_StationGunsQueryRespVO_';
export type { AppResponse_StationListQueryRespVO_ } from './models/AppResponse_StationListQueryRespVO_';
export type { AppResponse_StationTypePropVO_ } from './models/AppResponse_StationTypePropVO_';
export type { AppResponse_StopChargeRespVO_ } from './models/AppResponse_StopChargeRespVO_';
export type { AppResponse_UnderwayOrderQueryRespVO_ } from './models/AppResponse_UnderwayOrderQueryRespVO_';
export type { AppResponse_UpCcbOrderPayRespVO_ } from './models/AppResponse_UpCcbOrderPayRespVO_';
export type { AppResponse_UpOrderPayQueryRespDTO_ } from './models/AppResponse_UpOrderPayQueryRespDTO_';
export type { AppResponse_UpOrderRefundRespDTO_ } from './models/AppResponse_UpOrderRefundRespDTO_';
export type { AppResponse_UpOweOrderRespVO_ } from './models/AppResponse_UpOweOrderRespVO_';
export type { AppResponse_UserEnterpriseMarkRespVO_ } from './models/AppResponse_UserEnterpriseMarkRespVO_';
export type { AppResponse_UserListQueryRespVO_ } from './models/AppResponse_UserListQueryRespVO_';
export type { AppResponse_UserOverviewRespVO_ } from './models/AppResponse_UserOverviewRespVO_';
export type { AppResponse_UserQueryRespVO_ } from './models/AppResponse_UserQueryRespVO_';
export type { AppResponse_WeatherQueryRespVO_ } from './models/AppResponse_WeatherQueryRespVO_';
export type { AppResponseAppFilingInfoFacadeVO } from './models/AppResponseAppFilingInfoFacadeVO';
export type { AppResponseAppParkingInfoFacadeVO } from './models/AppResponseAppParkingInfoFacadeVO';
export type { AppResponseAuthTokenRes } from './models/AppResponseAuthTokenRes';
export type { AppResponseBannerQueryRespVO } from './models/AppResponseBannerQueryRespVO';
export type { AppResponseBoolean } from './models/AppResponseBoolean';
export type { AppResponseEvaluationConfigRespVO } from './models/AppResponseEvaluationConfigRespVO';
export type { AppResponseEvaluationRespVO } from './models/AppResponseEvaluationRespVO';
export type { AppResponseFileUploadRespVO } from './models/AppResponseFileUploadRespVO';
export type { AppResponseInteger } from './models/AppResponseInteger';
export type { AppResponseInvoiceDetailResponse } from './models/AppResponseInvoiceDetailResponse';
export type { AppResponseInvoiceOrderPageQueryRespVO } from './models/AppResponseInvoiceOrderPageQueryRespVO';
export type { AppResponseListFacadeEnterpriseTitleResponse } from './models/AppResponseListFacadeEnterpriseTitleResponse';
export type { AppResponseListInvoiceDetailResponse } from './models/AppResponseListInvoiceDetailResponse';
export type { AppResponseListInvoiceOrderVO } from './models/AppResponseListInvoiceOrderVO';
export type { AppResponseListListMerchantInfoVO } from './models/AppResponseListListMerchantInfoVO';
export type { AppResponseListParkingStatisticsFacadeVO } from './models/AppResponseListParkingStatisticsFacadeVO';
export type { AppResponseListProtocolQueryRespVO } from './models/AppResponseListProtocolQueryRespVO';
export type { AppResponseMallOrderRespVO } from './models/AppResponseMallOrderRespVO';
export type { AppResponseMobileRegCheckRsp } from './models/AppResponseMobileRegCheckRsp';
export type { AppResponsePageRespVOInvoicableOrderResponse } from './models/AppResponsePageRespVOInvoicableOrderResponse';
export type { AppResponsePageRespVOInvoiceListResponse } from './models/AppResponsePageRespVOInvoiceListResponse';
export type { AppResponsePageRespVOParkingListFacadeVO } from './models/AppResponsePageRespVOParkingListFacadeVO';
export type { AppResponsePageRespVOPointsFlowRespVO } from './models/AppResponsePageRespVOPointsFlowRespVO';
export type { AppResponsePointsQueryRespVO } from './models/AppResponsePointsQueryRespVO';
export type { AppResponsePredictionChargeRespVO } from './models/AppResponsePredictionChargeRespVO';
export type { AppResponsePredictionParkRespVO } from './models/AppResponsePredictionParkRespVO';
export type { AppResponseProtocolDetailRespVO } from './models/AppResponseProtocolDetailRespVO';
export type { AppResponseRightsDetailPageVORightsFlowDetailVO } from './models/AppResponseRightsDetailPageVORightsFlowDetailVO';
export type { AppResponseString } from './models/AppResponseString';
export type { AppResponseSuccessRes } from './models/AppResponseSuccessRes';
export type { AppResponseSuNingOrderInfoResponse } from './models/AppResponseSuNingOrderInfoResponse';
export type { AppResponseSuNingPayResponse } from './models/AppResponseSuNingPayResponse';
export type { AppResponseUserAppLoginRsp } from './models/AppResponseUserAppLoginRsp';
export type { AppResponseUserInfoQueryRsp } from './models/AppResponseUserInfoQueryRsp';
export type { AppResponseUserInfoUpdateRes } from './models/AppResponseUserInfoUpdateRes';
export type { AppResponseUserStatusCheckCauseRes } from './models/AppResponseUserStatusCheckCauseRes';
export type { AppResponseVoid } from './models/AppResponseVoid';
export type { AreaCarOverviewRespVO } from './models/AreaCarOverviewRespVO';
export type { AreaRevenueTrendVO } from './models/AreaRevenueTrendVO';
export type { AuthTokenRes } from './models/AuthTokenRes';
export type { BalanceQueryRespVO } from './models/BalanceQueryRespVO';
export type { BalanceWithdrawReqVO } from './models/BalanceWithdrawReqVO';
export type { BalanceWithdrawRespVO } from './models/BalanceWithdrawRespVO';
export { BannerElementVO } from './models/BannerElementVO';
export type { BannerQueryRequest } from './models/BannerQueryRequest';
export type { BannerQueryRespVO } from './models/BannerQueryRespVO';
export { BannerVO } from './models/BannerVO';
export type { BikeCountInfo } from './models/BikeCountInfo';
export type { BikePointsInfo } from './models/BikePointsInfo';
export type { BikeQueryRequest } from './models/BikeQueryRequest';
export type { BillingConfigFacadeGVO } from './models/BillingConfigFacadeGVO';
export type { BillingConfigFacadeVO } from './models/BillingConfigFacadeVO';
export type { Body } from './models/Body';
export type { Body1 } from './models/Body1';
export type { BusinessChargingViewRespVO } from './models/BusinessChargingViewRespVO';
export type { BusinessParkingViewRespVO } from './models/BusinessParkingViewRespVO';
export type { BusinessResourceOverviewRepsVO } from './models/BusinessResourceOverviewRepsVO';
export type { BusinessRevenuePropVO } from './models/BusinessRevenuePropVO';
export type { ChangeMobileReq } from './models/ChangeMobileReq';
export type { ChannelGrantFlowRes } from './models/ChannelGrantFlowRes';
export { ChargePriceVO } from './models/ChargePriceVO';
export type { ChargingCountTrendRespVO } from './models/ChargingCountTrendRespVO';
export type { ChargingOrderStatVO } from './models/ChargingOrderStatVO';
export type { ChargingPqOrderTrendRespVO } from './models/ChargingPqOrderTrendRespVO';
export type { ChargingRevenueVO } from './models/ChargingRevenueVO';
export type { ChargingStationRankRespVO } from './models/ChargingStationRankRespVO';
export type { CloseParkingRevenueVO } from './models/CloseParkingRevenueVO';
export type { CouponBizPropVO } from './models/CouponBizPropVO';
export type { CouponQueryRequest } from './models/CouponQueryRequest';
export type { CouponStatVO } from './models/CouponStatVO';
export { CouponTypeModifyRespVO } from './models/CouponTypeModifyRespVO';
export { CouponTypeMofifyReqVO } from './models/CouponTypeMofifyReqVO';
export type { CreateVipOrderRequest } from './models/CreateVipOrderRequest';
export type { CreateVipOrderResponse } from './models/CreateVipOrderResponse';
export { DetailRespVO } from './models/DetailRespVO';
export type { DetailRespVO3 } from './models/DetailRespVO3';
export { EnterpriseChargePriceVO } from './models/EnterpriseChargePriceVO';
export { EnterpriseListQueryRespVO } from './models/EnterpriseListQueryRespVO';
export { EnterpriseStationDetailQueryRespVO } from './models/EnterpriseStationDetailQueryRespVO';
export { EnterpriseStationPageQueryReqVO } from './models/EnterpriseStationPageQueryReqVO';
export type { EnterpriseStationPageQueryRespVO } from './models/EnterpriseStationPageQueryRespVO';
export type { EnterpriseTitleRequest } from './models/EnterpriseTitleRequest';
export type { EquityUnitFacadeVO } from './models/EquityUnitFacadeVO';
export type { EvaluationConfigRespVO } from './models/EvaluationConfigRespVO';
export { EvaluationReqVO } from './models/EvaluationReqVO';
export { EvaluationRespVO } from './models/EvaluationRespVO';
export type { FacadeEnterpriseTitleResponse } from './models/FacadeEnterpriseTitleResponse';
export type { FeedbackAddReq } from './models/FeedbackAddReq';
export type { FeedbackDetailReq } from './models/FeedbackDetailReq';
export type { FeedbackQueryReq } from './models/FeedbackQueryReq';
export type { FeedbackResp } from './models/FeedbackResp';
export type { FileDownloadRespDTO } from './models/FileDownloadRespDTO';
export type { FileUploadRespVO } from './models/FileUploadRespVO';
export type { FilingInfoFacadeVO } from './models/FilingInfoFacadeVO';
export type { GetVipActiveStatusRequest } from './models/GetVipActiveStatusRequest';
export type { GetVipActiveStatusResponse } from './models/GetVipActiveStatusResponse';
export type { GetVipGoodsRequest } from './models/GetVipGoodsRequest';
export type { GetVipInvoiceRequest } from './models/GetVipInvoiceRequest';
export type { ImageUploadRequest } from './models/ImageUploadRequest';
export type { InformationCategoryListRequest } from './models/InformationCategoryListRequest';
export type { InformationCategoryRespVO } from './models/InformationCategoryRespVO';
export type { InformationColumnRespVO } from './models/InformationColumnRespVO';
export type { InformationContentRespVO } from './models/InformationContentRespVO';
export type { InformationDetailRequest } from './models/InformationDetailRequest';
export type { InformationPageRequest } from './models/InformationPageRequest';
export type { InformationSummaryRequest } from './models/InformationSummaryRequest';
export type { InformationSummaryRespVO } from './models/InformationSummaryRespVO';
export { InvoicableOrderQueryRequest } from './models/InvoicableOrderQueryRequest';
export type { InvoicableOrderResponse } from './models/InvoicableOrderResponse';
export { InvoiceAgainIssueRequest } from './models/InvoiceAgainIssueRequest';
export type { InvoiceDetailRequest } from './models/InvoiceDetailRequest';
export { InvoiceDetailResponse } from './models/InvoiceDetailResponse';
export { InvoiceHistoryRequest } from './models/InvoiceHistoryRequest';
export { InvoiceIssueRequest } from './models/InvoiceIssueRequest';
export { InvoiceListRequest } from './models/InvoiceListRequest';
export { InvoiceListResponse } from './models/InvoiceListResponse';
export { InvoiceOrderPageQueryReqVO } from './models/InvoiceOrderPageQueryReqVO';
export type { InvoiceOrderPageQueryRespVO } from './models/InvoiceOrderPageQueryRespVO';
export type { InvoiceOrderVO } from './models/InvoiceOrderVO';
export type { InvoiceRelatedOrderQueryReqVO } from './models/InvoiceRelatedOrderQueryReqVO';
export { InvoiceSendEmailRequest } from './models/InvoiceSendEmailRequest';
export type { InvoiceTitleAddRequest } from './models/InvoiceTitleAddRequest';
export type { InvoiceTitleIdRequest } from './models/InvoiceTitleIdRequest';
export type { InvoiceTitleResponse } from './models/InvoiceTitleResponse';
export type { InvoiceTitleSetDefaultRequest } from './models/InvoiceTitleSetDefaultRequest';
export type { key } from './models/key';
export type { ListBenefitGoodsRequest } from './models/ListBenefitGoodsRequest';
export type { ListBenefitGoodsResponse } from './models/ListBenefitGoodsResponse';
export type { ListGoodsOpenHistoryRequest } from './models/ListGoodsOpenHistoryRequest';
export type { ListGoodsOpenHistoryResponse } from './models/ListGoodsOpenHistoryResponse';
export type { ListInvoiceOrdersResponse } from './models/ListInvoiceOrdersResponse';
export type { ListMerchantInfoVO } from './models/ListMerchantInfoVO';
export type { ListVipGoodsRequest } from './models/ListVipGoodsRequest';
export type { ListVipGoodsResponse } from './models/ListVipGoodsResponse';
export type { MallInvoiceOrderRes } from './models/MallInvoiceOrderRes';
export type { MallInvoiceQueryRequest } from './models/MallInvoiceQueryRequest';
export type { MallOrderInfo } from './models/MallOrderInfo';
export type { MallOrderQueryRequest } from './models/MallOrderQueryRequest';
export type { MallOrderRespVO } from './models/MallOrderRespVO';
export type { MallRevenueVO } from './models/MallRevenueVO';
export type { ManageUnitFacadeVO } from './models/ManageUnitFacadeVO';
export type { Map_String_ } from './models/Map_String_';
export type { MapDataQueryReqVO } from './models/MapDataQueryReqVO';
export type { MapDataQueryRespVO } from './models/MapDataQueryRespVO';
export type { MapObject } from './models/MapObject';
export type { MerchantFlowFileGenerateRespDTO } from './models/MerchantFlowFileGenerateRespDTO';
export type { MessageRequest } from './models/MessageRequest';
export type { MiniAdvAuthReq } from './models/MiniAdvAuthReq';
export type { MiniSilentAuthReq } from './models/MiniSilentAuthReq';
export type { MobileRegCheckReq } from './models/MobileRegCheckReq';
export type { MobileRegCheckRsp } from './models/MobileRegCheckRsp';
export type { MobileRegisterReq } from './models/MobileRegisterReq';
export type { NavigationRecordSaveRequest } from './models/NavigationRecordSaveRequest';
export type { NearBikeInfoModel } from './models/NearBikeInfoModel';
export type { Object } from './models/Object';
export type { OnlineGameAreaInfo } from './models/OnlineGameAreaInfo';
export type { OrderDetailQueryReqVO } from './models/OrderDetailQueryReqVO';
export { OrderDetailQueryRespVO } from './models/OrderDetailQueryRespVO';
export { OrderListVO } from './models/OrderListVO';
export { OrderPageQueryReqVO } from './models/OrderPageQueryReqVO';
export type { OrderPageQueryRespVO } from './models/OrderPageQueryRespVO';
export type { OverviewNumRatioRepsVO } from './models/OverviewNumRatioRepsVO';
export type { PageReqVO } from './models/PageReqVO';
export type { PageRespVO_FeedbackResp_ } from './models/PageRespVO_FeedbackResp_';
export type { PageRespVO_InvoiceTitleResponse_ } from './models/PageRespVO_InvoiceTitleResponse_';
export type { PageRespVO_ListGoodsOpenHistoryResponse_ } from './models/PageRespVO_ListGoodsOpenHistoryResponse_';
export type { PageRespVO_ListVipGoodsResponse_ } from './models/PageRespVO_ListVipGoodsResponse_';
export type { PageRespVO_MallInvoiceOrderRes_ } from './models/PageRespVO_MallInvoiceOrderRes_';
export type { PageRespVO_PrizeQueryRespVO_ } from './models/PageRespVO_PrizeQueryRespVO_';
export type { PageRespVO_WalletRecordLogRespVO_ } from './models/PageRespVO_WalletRecordLogRespVO_';
export type { PageRespVOInvoicableOrderResponse } from './models/PageRespVOInvoicableOrderResponse';
export type { PageRespVOInvoiceListResponse } from './models/PageRespVOInvoiceListResponse';
export type { PageRespVOParkingListFacadeVO } from './models/PageRespVOParkingListFacadeVO';
export type { PageRespVOPointsFlowRespVO } from './models/PageRespVOPointsFlowRespVO';
export type { ParkingHotRankRespVO } from './models/ParkingHotRankRespVO';
export type { ParkingInfoFacadeVO } from './models/ParkingInfoFacadeVO';
export type { ParkingInoutCountTrendRespVO } from './models/ParkingInoutCountTrendRespVO';
export type { ParkingListFacadeVO } from './models/ParkingListFacadeVO';
export type { ParkingMaterialsFacadeVO } from './models/ParkingMaterialsFacadeVO';
export type { ParkingPileFacadeVO } from './models/ParkingPileFacadeVO';
export type { ParkingSpaceFacadeVO } from './models/ParkingSpaceFacadeVO';
export type { ParkingStatisticsFacadeVO } from './models/ParkingStatisticsFacadeVO';
export type { ParkingTurnoverOrUtilizationRatioVO } from './models/ParkingTurnoverOrUtilizationRatioVO';
export type { ParkingUsedTrendRespVO } from './models/ParkingUsedTrendRespVO';
export type { PayChannelRevenuePropVO } from './models/PayChannelRevenuePropVO';
export type { PayChargeRespVO } from './models/PayChargeRespVO';
export type { PayOrderNotifyReqDTO } from './models/PayOrderNotifyReqDTO';
export { PicConfigDTO } from './models/PicConfigDTO';
export type { PileCheckReqVO } from './models/PileCheckReqVO';
export type { PileCheckRespVO } from './models/PileCheckRespVO';
export { PointsFlowQueryRequest } from './models/PointsFlowQueryRequest';
export type { PointsFlowRespVO } from './models/PointsFlowRespVO';
export type { PointsQueryRespVO } from './models/PointsQueryRespVO';
export type { PredictionChargeRespVO } from './models/PredictionChargeRespVO';
export type { PredictionParkRespVO } from './models/PredictionParkRespVO';
export type { PredictionValueVO } from './models/PredictionValueVO';
export type { PredictionValueVO2 } from './models/PredictionValueVO2';
export type { PrizeCountRequest } from './models/PrizeCountRequest';
export type { PrizeCountRespVO } from './models/PrizeCountRespVO';
export type { PrizeDetailRequest } from './models/PrizeDetailRequest';
export { PrizeInfoResponse } from './models/PrizeInfoResponse';
export { PrizeListRequest } from './models/PrizeListRequest';
export type { PrizePreviewRequest } from './models/PrizePreviewRequest';
export type { PrizeQueryRequest } from './models/PrizeQueryRequest';
export { PrizeQueryRespVO } from './models/PrizeQueryRespVO';
export { ProblemConfigQueryRequest } from './models/ProblemConfigQueryRequest';
export type { ProblemConfigRespVO } from './models/ProblemConfigRespVO';
export type { ProcessQueryReqVO } from './models/ProcessQueryReqVO';
export { ProcessQueryRespVO } from './models/ProcessQueryRespVO';
export type { ProductTypePropVO } from './models/ProductTypePropVO';
export type { PropItemVO } from './models/PropItemVO';
export { ProtocolDetailRespVO } from './models/ProtocolDetailRespVO';
export type { ProtocolQueryRequest } from './models/ProtocolQueryRequest';
export type { ProtocolQueryRespVO } from './models/ProtocolQueryRespVO';
export type { QrcodeScanReqVO } from './models/QrcodeScanReqVO';
export { QrcodeScanRespVO } from './models/QrcodeScanRespVO';
export type { RefreshTokenReq } from './models/RefreshTokenReq';
export { RefundDetailVO } from './models/RefundDetailVO';
export type { RefundOrderNotifyReqDTO } from './models/RefundOrderNotifyReqDTO';
export type { RevenueOverviewVO } from './models/RevenueOverviewVO';
export type { RevenueTrendVO } from './models/RevenueTrendVO';
export type { RightsCountQueryRequest } from './models/RightsCountQueryRequest';
export type { RightsCountQueryResponse } from './models/RightsCountQueryResponse';
export type { RightsDetailPageVORightsFlowDetailVO } from './models/RightsDetailPageVORightsFlowDetailVO';
export type { RightsFlowDetail } from './models/RightsFlowDetail';
export type { RightsFlowDetailVO } from './models/RightsFlowDetailVO';
export type { RightsFlowResponse } from './models/RightsFlowResponse';
export type { RightsPreviewFlowResponse } from './models/RightsPreviewFlowResponse';
export type { RightsPreviewQueryRequest } from './models/RightsPreviewQueryRequest';
export type { RightsQueryRequest } from './models/RightsQueryRequest';
export type { RoadParkingRevenueVO } from './models/RoadParkingRevenueVO';
export type { SetBannerOrderRequest } from './models/SetBannerOrderRequest';
export { SilentAuthRsp } from './models/SilentAuthRsp';
export { SmsSendReq } from './models/SmsSendReq';
export { StartChargeReqVO } from './models/StartChargeReqVO';
export type { StartChargeRespVO } from './models/StartChargeRespVO';
export type { StationBuildsRespVO } from './models/StationBuildsRespVO';
export type { StationCollectCancelReqVO } from './models/StationCollectCancelReqVO';
export type { StationCollectPageQueryReqVO } from './models/StationCollectPageQueryReqVO';
export type { StationCollectPageQueryRespVO } from './models/StationCollectPageQueryRespVO';
export type { StationCollectReqVO } from './models/StationCollectReqVO';
export { StationCollectVO } from './models/StationCollectVO';
export type { StationContactsRespVO } from './models/StationContactsRespVO';
export type { StationDetailQueryReqVO } from './models/StationDetailQueryReqVO';
export { StationDetailQueryRespVO } from './models/StationDetailQueryRespVO';
export type { StationDetailRespVO } from './models/StationDetailRespVO';
export type { StationFeeQueryReqVO } from './models/StationFeeQueryReqVO';
export type { StationFeeQueryRespVO } from './models/StationFeeQueryRespVO';
export type { StationFinanceCfgRespVO } from './models/StationFinanceCfgRespVO';
export { StationGunsQueryReqVO } from './models/StationGunsQueryReqVO';
export { StationGunsQueryRespVO } from './models/StationGunsQueryRespVO';
export { StationInfoVO } from './models/StationInfoVO';
export { StationListQueryReqVO } from './models/StationListQueryReqVO';
export type { StationListQueryRespVO } from './models/StationListQueryRespVO';
export { StationMapGroupReqVO } from './models/StationMapGroupReqVO';
export { StationMapGroupRespVO } from './models/StationMapGroupRespVO';
export type { StationRevenueVO } from './models/StationRevenueVO';
export type { StationTypePropVO } from './models/StationTypePropVO';
export type { StopChargeReqVO } from './models/StopChargeReqVO';
export type { StopChargeRespVO } from './models/StopChargeRespVO';
export type { SuccessRes } from './models/SuccessRes';
export type { SuNingOrderInfoRequest } from './models/SuNingOrderInfoRequest';
export { SuNingOrderInfoResponse } from './models/SuNingOrderInfoResponse';
export { SuNingPayRequest } from './models/SuNingPayRequest';
export type { SuNingPayResponse } from './models/SuNingPayResponse';
export type { TestTokenReq } from './models/TestTokenReq';
export { TextConfigDTO } from './models/TextConfigDTO';
export type { TimeDistributionRespVO } from './models/TimeDistributionRespVO';
export { UnderwayOrderQueryRespVO } from './models/UnderwayOrderQueryRespVO';
export type { UpCcbOrderPayRespVO } from './models/UpCcbOrderPayRespVO';
export type { UpOrderPayQueryRespDTO } from './models/UpOrderPayQueryRespDTO';
export type { UpOrderRefundRespDTO } from './models/UpOrderRefundRespDTO';
export { UpOrderReqVO } from './models/UpOrderReqVO';
export { UpOweOrderReqVO } from './models/UpOweOrderReqVO';
export type { UpOweOrderRespVO } from './models/UpOweOrderRespVO';
export { UserAppLoginReq } from './models/UserAppLoginReq';
export type { UserAppLoginRsp } from './models/UserAppLoginRsp';
export type { UserEnterpriseMarkRespVO } from './models/UserEnterpriseMarkRespVO';
export { UserInfoQueryRsp } from './models/UserInfoQueryRsp';
export { UserInfoUpdateReq } from './models/UserInfoUpdateReq';
export { UserInfoUpdateRes } from './models/UserInfoUpdateRes';
export type { UserListQueryReqVO } from './models/UserListQueryReqVO';
export type { UserListQueryRespVO } from './models/UserListQueryRespVO';
export type { UserOverviewRespVO } from './models/UserOverviewRespVO';
export type { UserQueryRespVO } from './models/UserQueryRespVO';
export { UserResponseData_Object_ } from './models/UserResponseData_Object_';
export type { UserSpecialRegisterReq } from './models/UserSpecialRegisterReq';
export type { UserStatusCheckCauseRes } from './models/UserStatusCheckCauseRes';
export { WalletRecordLogRespVO } from './models/WalletRecordLogRespVO';
export type { WeatherQueryRespVO } from './models/WeatherQueryRespVO';

export { AccountManagementService } from './services/AccountManagementService';
export { AlipayAuthorizationRelatedService } from './services/AlipayAuthorizationRelatedService';
export { BerthNavigationService } from './services/BerthNavigationService';
export { BicycleService } from './services/BicycleService';
export { BoothService } from './services/BoothService';
export { BusinessViewService } from './services/BusinessViewService';
export { ChargingService } from './services/ChargingService';
export { ChargingOrdersService } from './services/ChargingOrdersService';
export { EnterpriseRelatedService } from './services/EnterpriseRelatedService';
export { EvaluationInterfaceService } from './services/EvaluationInterfaceService';
export { FeedbackConfigurationService } from './services/FeedbackConfigurationService';
export { FeedbackRelatedService } from './services/FeedbackRelatedService';
export { FileUploadControllerService } from './services/FileUploadControllerService';
export { FilingApplicationService } from './services/FilingApplicationService';
export { ForecastQueryService } from './services/ForecastQueryService';
export { HealthCheckService } from './services/HealthCheckService';
export { InformationBoothDemoService } from './services/InformationBoothDemoService';
export { InvoiceHeaderService } from './services/InvoiceHeaderService';
export { InvoiceOrderService } from './services/InvoiceOrderService';
export { InvoiceRelatedService } from './services/InvoiceRelatedService';
export { InvoiceRelatedInterfaceService } from './services/InvoiceRelatedInterfaceService';
export { KanbanEntryService } from './services/KanbanEntryService';
export { MallInvoiceService } from './services/MallInvoiceService';
export { MapSearchService } from './services/MapSearchService';
export { MarketingRightsService } from './services/MarketingRightsService';
export { NewsService } from './services/NewsService';
export { PaymentRelatedInterfacesService } from './services/PaymentRelatedInterfacesService';
export { PersonalCenterOrderNotificationService } from './services/PersonalCenterOrderNotificationService';
export { PersonalCenterUserWalletRelatedUserWalletRelatedService } from './services/PersonalCenterUserWalletRelatedUserWalletRelatedService';
export { PointsMallOrderService } from './services/PointsMallOrderService';
export { ProtocolRelatedService } from './services/ProtocolRelatedService';
export { RevenueViewService } from './services/RevenueViewService';
export { RightsPackageService } from './services/RightsPackageService';
export { SmsService } from './services/SmsService';
export { StationService } from './services/StationService';
export { StationCollectService } from './services/StationCollectService';
export { UserCouponsService } from './services/UserCouponsService';
export { UserPointsService } from './services/UserPointsService';
export { UserWalletService } from './services/UserWalletService';
export { WalletService } from './services/WalletService';
export { WeatherControllerService } from './services/WeatherControllerService';
export { WeChatAuthorizationRelatedService } from './services/WeChatAuthorizationRelatedService';
