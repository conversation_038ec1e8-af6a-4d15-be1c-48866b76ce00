/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponse_StationCollectPageQueryRespVO_ } from '../models/AppResponse_StationCollectPageQueryRespVO_';
import type { StationCollectCancelReqVO } from '../models/StationCollectCancelReqVO';
import type { StationCollectPageQueryReqVO } from '../models/StationCollectPageQueryReqVO';
import type { StationCollectReqVO } from '../models/StationCollectReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class StationCollectService {
  /**
   * 查询收藏列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_StationCollectPageQueryRespVO_
   * @throws ApiError
   */
  public static postCollectStationList(
    requestBody?: StationCollectPageQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationCollectPageQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/collect/station/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 收藏/取消收藏
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postCollectStationCollect(
    requestBody?: StationCollectReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/collect/station/collect',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 批量取消收藏
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postCollectStationCancel(
    requestBody?: StationCollectCancelReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/collect/station/cancel',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
