/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponse_OrderDetailQueryRespVO_ } from '../models/AppResponse_OrderDetailQueryRespVO_';
import type { AppResponse_OrderPageQueryRespVO_ } from '../models/AppResponse_OrderPageQueryRespVO_';
import type { AppResponse_UnderwayOrderQueryRespVO_ } from '../models/AppResponse_UnderwayOrderQueryRespVO_';
import type { OrderDetailQueryReqVO } from '../models/OrderDetailQueryReqVO';
import type { OrderPageQueryReqVO } from '../models/OrderPageQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ChargingOrdersService {
  /**
   * 查询订单列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_OrderPageQueryRespVO_
   * @throws ApiError
   */
  public static postOrderList(
    requestBody?: OrderPageQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_OrderPageQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/order/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询订单详情
   * @param requestBody
   * @returns AppResponse_OrderDetailQueryRespVO_
   * @throws ApiError
   */
  public static postOrderDetail(
    requestBody?: OrderDetailQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_OrderDetailQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/order/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询进行中的订单
   * 进行中订单定义：充电中、已支付
   * @returns AppResponse_UnderwayOrderQueryRespVO_
   * @throws ApiError
   */
  public static postOrderUnderway(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UnderwayOrderQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/order/underway',
      },
    );
  }

  /**
   * 订单隐藏
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postOrderHide(
    requestBody?: OrderDetailQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/order/hide',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
