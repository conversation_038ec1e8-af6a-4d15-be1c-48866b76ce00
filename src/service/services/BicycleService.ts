/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_NearBikeInfoModel_ } from '../models/AppResponse_NearBikeInfoModel_';
import type { BikeQueryRequest } from '../models/BikeQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class BicycleService {
  /**
   * 根据定位坐标查询周边单车信息
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_NearBikeInfoModel_
   * @throws ApiError
   */
  public static postBikeQueryNearBikeInfo(
    requestBody?: BikeQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_NearBikeInfoModel_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/bike/queryNearBikeInfo',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
