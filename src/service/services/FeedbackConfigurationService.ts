/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_List_ProblemConfigRespVO_ } from '../models/AppResponse_List_ProblemConfigRespVO_';
import type { ProblemConfigQueryRequest } from '../models/ProblemConfigQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class FeedbackConfigurationService {
  /**
   * 列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_List_ProblemConfigRespVO_
   * @throws ApiError
   */
  public static postProblemConfigList(
    requestBody?: ProblemConfigQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_ProblemConfigRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/problem/config/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
