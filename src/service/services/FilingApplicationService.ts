/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppParkingListDTO } from '../models/AppParkingListDTO';
import type { AppResponseAppFilingInfoFacadeVO } from '../models/AppResponseAppFilingInfoFacadeVO';
import type { AppResponseAppParkingInfoFacadeVO } from '../models/AppResponseAppParkingInfoFacadeVO';
import type { AppResponseListListMerchantInfoVO } from '../models/AppResponseListListMerchantInfoVO';
import type { AppResponseListParkingStatisticsFacadeVO } from '../models/AppResponseListParkingStatisticsFacadeVO';
import type { AppResponsePageRespVOParkingListFacadeVO } from '../models/AppResponsePageRespVOParkingListFacadeVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class FilingApplicationService {
  /**
   * 商户列表查询
   * 这是一个测试
   * @returns AppResponseListListMerchantInfoVO
   * @throws ApiError
   */
  public static getMerchantParkingListMerchantInfo(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListListMerchantInfoVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/merchant/parking/listMerchantInfo',
      },
    );
  }

  /**
   * 商户备案申请数据统计
   * @param merchantCode
   * @returns AppResponseListParkingStatisticsFacadeVO
   * @throws ApiError
   */
  public static getMerchantParkingParkingStatistics(
    merchantCode: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListParkingStatisticsFacadeVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/merchant/parking/parkingStatistics',
        query: {
          merchantCode: merchantCode,
        },
      },
    );
  }

  /**
   * 商户备案列表查询
   * @param requestBody
   * @returns AppResponsePageRespVOParkingListFacadeVO
   * @throws ApiError
   */
  public static postMerchantParkingParkingList(
    requestBody?: AppParkingListDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePageRespVOParkingListFacadeVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/merchant/parking/parkingList',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 备案场站详情查询
   * @param applyNo
   * @returns AppResponseAppParkingInfoFacadeVO
   * @throws ApiError
   */
  public static getMerchantParkingParkingInfo(
    applyNo: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseAppParkingInfoFacadeVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/merchant/parking/parkingInfo',
        query: {
          applyNo: applyNo,
        },
      },
    );
  }

  /**
   * 备案号查询备案场站
   * @param filingNo
   * @returns AppResponseAppFilingInfoFacadeVO
   * @throws ApiError
   */
  public static getMerchantParkingFilingInfo(
    filingNo: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseAppFilingInfoFacadeVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/merchant/parking/filingInfo',
        query: {
          filingNo: filingNo,
        },
      },
    );
  }
}
