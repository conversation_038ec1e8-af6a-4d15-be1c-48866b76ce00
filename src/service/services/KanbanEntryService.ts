/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseInteger } from '../models/AppResponseInteger';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class KanbanEntryService {
  /**
   * 显示视图入口 0无权限 1营收视图 2经营视图 3全部
   * 这是一个测试
   * @returns AppResponseInteger
   * @throws ApiError
   */
  public static getDashboardEntry(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseInteger]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/entry',
      },
    );
  }
}
