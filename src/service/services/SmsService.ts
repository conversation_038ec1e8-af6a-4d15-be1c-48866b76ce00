/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseSuccessRes } from '../models/AppResponseSuccessRes';
import type { SmsSendReq } from '../models/SmsSendReq';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class SmsService {
  /**
   * 获取验证码
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseSuccessRes
   * @throws ApiError
   */
  public static postSmsSendSms(
    requestBody?: SmsSendReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuccessRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/sms/sendSms',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
