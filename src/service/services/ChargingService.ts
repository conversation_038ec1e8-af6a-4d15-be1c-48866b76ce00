/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_CouponTypeModifyRespVO_ } from '../models/AppResponse_CouponTypeModifyRespVO_';
import type { AppResponse_PayChargeRespVO_ } from '../models/AppResponse_PayChargeRespVO_';
import type { AppResponse_PileCheckRespVO_ } from '../models/AppResponse_PileCheckRespVO_';
import type { AppResponse_ProcessQueryRespVO_ } from '../models/AppResponse_ProcessQueryRespVO_';
import type { AppResponse_QrcodeScanRespVO_ } from '../models/AppResponse_QrcodeScanRespVO_';
import type { AppResponse_StartChargeRespVO_ } from '../models/AppResponse_StartChargeRespVO_';
import type { AppResponse_StopChargeRespVO_ } from '../models/AppResponse_StopChargeRespVO_';
import type { CouponTypeMofifyReqVO } from '../models/CouponTypeMofifyReqVO';
import type { PileCheckReqVO } from '../models/PileCheckReqVO';
import type { ProcessQueryReqVO } from '../models/ProcessQueryReqVO';
import type { QrcodeScanReqVO } from '../models/QrcodeScanReqVO';
import type { StartChargeReqVO } from '../models/StartChargeReqVO';
import type { StopChargeReqVO } from '../models/StopChargeReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ChargingService {
  /**
   * 校验桩枪编号
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_PileCheckRespVO_
   * @throws ApiError
   */
  public static postChargeCheck(
    requestBody?: PileCheckReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PileCheckRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/check',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 扫码详情
   * @param requestBody
   * @returns AppResponse_QrcodeScanRespVO_
   * @throws ApiError
   */
  public static postChargeQrcode(
    requestBody?: QrcodeScanReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_QrcodeScanRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/qrcode',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 支付&启动充电
   * @param requestBody
   * @returns AppResponse_PayChargeRespVO_
   * @throws ApiError
   */
  public static postChargePrepay(
    requestBody?: StartChargeReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PayChargeRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/prepay',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 启动充电
   * @param requestBody
   * @returns AppResponse_StartChargeRespVO_
   * @throws ApiError
   */
  public static postChargeStart(
    requestBody?: StartChargeReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StartChargeRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/start',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 停止充电
   * @param requestBody
   * @returns AppResponse_StopChargeRespVO_
   * @throws ApiError
   */
  public static postChargeStop(
    requestBody?: StopChargeReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StopChargeRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/stop',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询充电进度
   * @param requestBody
   * @returns AppResponse_ProcessQueryRespVO_
   * @throws ApiError
   */
  public static postChargeProcess(
    requestBody?: ProcessQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_ProcessQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/process',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 变更优惠券使用类型
   * @param requestBody
   * @returns AppResponse_CouponTypeModifyRespVO_
   * @throws ApiError
   */
  public static postChargeCouponType(
    requestBody?: CouponTypeMofifyReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_CouponTypeModifyRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/charge/couponType',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
