/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class HealthCheckService {
  /**
   * 心跳接口
   * 这是一个测试
   * @returns string
   * @throws ApiError
   */
  public static getHealthCheck(config = {}): Promise<[undefined | Error, undefined | string]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/charging-server/health/check',
      },
    );
  }
}
