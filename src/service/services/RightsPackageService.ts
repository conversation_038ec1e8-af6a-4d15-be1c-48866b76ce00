/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { ActiveVipGoodsRequest } from '../models/ActiveVipGoodsRequest';
import type { AgainPayRequest } from '../models/AgainPayRequest';
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponse_CreateVipOrderResponse_ } from '../models/AppResponse_CreateVipOrderResponse_';
import type { AppResponse_GetVipActiveStatusResponse_ } from '../models/AppResponse_GetVipActiveStatusResponse_';
import type { AppResponse_List_ListBenefitGoodsResponse_ } from '../models/AppResponse_List_ListBenefitGoodsResponse_';
import type { AppResponse_List_ListInvoiceOrdersResponse_ } from '../models/AppResponse_List_ListInvoiceOrdersResponse_';
import type { AppResponse_ListBenefitGoodsResponse_ } from '../models/AppResponse_ListBenefitGoodsResponse_';
import type { AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_ } from '../models/AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_';
import type { AppResponse_PageRespVO_ListVipGoodsResponse_ } from '../models/AppResponse_PageRespVO_ListVipGoodsResponse_';
import type { CreateVipOrderRequest } from '../models/CreateVipOrderRequest';
import type { GetVipActiveStatusRequest } from '../models/GetVipActiveStatusRequest';
import type { GetVipGoodsRequest } from '../models/GetVipGoodsRequest';
import type { GetVipInvoiceRequest } from '../models/GetVipInvoiceRequest';
import type { ListBenefitGoodsRequest } from '../models/ListBenefitGoodsRequest';
import type { ListGoodsOpenHistoryRequest } from '../models/ListGoodsOpenHistoryRequest';
import type { ListVipGoodsRequest } from '../models/ListVipGoodsRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class RightsPackageService {
  /**
   * 查询会员权益商品列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_List_ListBenefitGoodsResponse_
   * @throws ApiError
   */
  public static postVipBenefitListVipGoods(
    requestBody?: ListBenefitGoodsRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_ListBenefitGoodsResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/listVipGoods',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取会员权益商品详情
   * @param requestBody
   * @returns AppResponse_ListBenefitGoodsResponse_
   * @throws ApiError
   */
  public static postVipBenefitGetVipGoods(
    requestBody?: GetVipGoodsRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_ListBenefitGoodsResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/getVipGoods',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取会员权益开通状态
   * @param requestBody
   * @returns AppResponse_GetVipActiveStatusResponse_
   * @throws ApiError
   */
  public static postVipBenefitGetVipActiveStatus(
    requestBody?: GetVipActiveStatusRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_GetVipActiveStatusResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/getVipActiveStatus',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 立即开通（下单）
   * @param requestBody
   * @returns AppResponse_CreateVipOrderResponse_
   * @throws ApiError
   */
  public static postVipBenefitCreateVipOrder(
    requestBody?: CreateVipOrderRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_CreateVipOrderResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/createVipOrder',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 去支付（待支付）
   * @param requestBody
   * @returns AppResponse_CreateVipOrderResponse_
   * @throws ApiError
   */
  public static postVipBenefitAgainPay(
    requestBody?: AgainPayRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_CreateVipOrderResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/againPay',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 激活（派奖）
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postVipBenefitActiveVipGoods(
    requestBody?: ActiveVipGoodsRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/activeVipGoods',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 开通记录（订单记录-全部）
   * @param requestBody
   * @returns AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_
   * @throws ApiError
   */
  public static postVipBenefitListVipGoodsHistory(
    requestBody?: ListGoodsOpenHistoryRequest,
    config = {},
  ): Promise<
    [undefined | Error, undefined | AppResponse_PageRespVO_ListGoodsOpenHistoryResponse_]
  > {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/listVipGoodsHistory',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 我的卡包（订单商品记录）
   * @param requestBody
   * @returns AppResponse_PageRespVO_ListVipGoodsResponse_
   * @throws ApiError
   */
  public static postVipBenefitListMyVipGoods(
    requestBody?: ListVipGoodsRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_ListVipGoodsResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/listMyVipGoods',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 发票关联订单列表
   * @param requestBody
   * @returns AppResponse_List_ListInvoiceOrdersResponse_
   * @throws ApiError
   */
  public static postVipBenefitInvoiceOrderRelated(
    requestBody?: GetVipInvoiceRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_ListInvoiceOrdersResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/vip/benefit/invoice/order/related',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
