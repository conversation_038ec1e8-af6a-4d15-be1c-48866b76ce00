/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_List_StationMapGroupRespVO_ } from '../models/AppResponse_List_StationMapGroupRespVO_';
import type { AppResponse_StationDetailQueryRespVO_ } from '../models/AppResponse_StationDetailQueryRespVO_';
import type { AppResponse_StationFeeQueryRespVO_ } from '../models/AppResponse_StationFeeQueryRespVO_';
import type { AppResponse_StationGunsQueryRespVO_ } from '../models/AppResponse_StationGunsQueryRespVO_';
import type { AppResponse_StationListQueryRespVO_ } from '../models/AppResponse_StationListQueryRespVO_';
import type { StationDetailQueryReqVO } from '../models/StationDetailQueryReqVO';
import type { StationFeeQueryReqVO } from '../models/StationFeeQueryReqVO';
import type { StationGunsQueryReqVO } from '../models/StationGunsQueryReqVO';
import type { StationListQueryReqVO } from '../models/StationListQueryReqVO';
import type { StationMapGroupReqVO } from '../models/StationMapGroupReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class StationService {
  /**
   * 查询站点列表(分页)
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_StationListQueryRespVO_
   * @throws ApiError
   */
  public static postStationList(
    requestBody?: StationListQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationListQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/station/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询地图站点数据
   * @param requestBody
   * @returns AppResponse_List_StationMapGroupRespVO_
   * @throws ApiError
   */
  public static postStationMap(
    requestBody?: StationMapGroupReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_StationMapGroupRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/station/map',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询充电站点详情
   * @param requestBody
   * @returns AppResponse_StationDetailQueryRespVO_
   * @throws ApiError
   */
  public static postStationDetail(
    requestBody?: StationDetailQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationDetailQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/station/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 充电场站-充电枪实况列表
   * @param requestBody
   * @returns AppResponse_StationGunsQueryRespVO_
   * @throws ApiError
   */
  public static postStationGuns(
    requestBody?: StationGunsQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationGunsQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/station/guns',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询充电场站计费明细
   * @param requestBody
   * @returns AppResponse_StationFeeQueryRespVO_
   * @throws ApiError
   */
  public static postStationFee(
    requestBody?: StationFeeQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationFeeQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/station/fee',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
