/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_InformationContentRespVO_ } from '../models/AppResponse_InformationContentRespVO_';
import type { AppResponse_InformationSummaryRespVO_ } from '../models/AppResponse_InformationSummaryRespVO_';
import type { AppResponse_List_InformationCategoryRespVO_ } from '../models/AppResponse_List_InformationCategoryRespVO_';
import type { AppResponse_List_InformationColumnRespVO_ } from '../models/AppResponse_List_InformationColumnRespVO_';
import type { InformationCategoryListRequest } from '../models/InformationCategoryListRequest';
import type { InformationDetailRequest } from '../models/InformationDetailRequest';
import type { InformationPageRequest } from '../models/InformationPageRequest';
import type { InformationSummaryRequest } from '../models/InformationSummaryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class NewsService {
  /**
   * 查询资讯栏目列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_List_InformationColumnRespVO_
   * @throws ApiError
   */
  public static postInformationColumnList(
    requestBody?: InformationPageRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_InformationColumnRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/information/column/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询资讯分类列表
   * @param requestBody
   * @returns AppResponse_List_InformationCategoryRespVO_
   * @throws ApiError
   */
  public static postInformationCategoryList(
    requestBody?: InformationCategoryListRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_InformationCategoryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/information/category/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询资讯内容列表
   * @param requestBody
   * @returns AppResponse_InformationSummaryRespVO_
   * @throws ApiError
   */
  public static postInformationContentList(
    requestBody?: InformationSummaryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_InformationSummaryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/information/content/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询资讯内容详情
   * @param requestBody
   * @returns AppResponse_InformationContentRespVO_
   * @throws ApiError
   */
  public static postInformationContentDetail(
    requestBody?: InformationDetailRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_InformationContentRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/information/content/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
