/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseInvoiceOrderPageQueryRespVO } from '../models/AppResponseInvoiceOrderPageQueryRespVO';
import type { AppResponseListInvoiceOrderVO } from '../models/AppResponseListInvoiceOrderVO';
import type { InvoiceOrderPageQueryReqVO } from '../models/InvoiceOrderPageQueryReqVO';
import type { InvoiceRelatedOrderQueryReqVO } from '../models/InvoiceRelatedOrderQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class InvoiceOrderService {
  /**
   * 查询可开票订单列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseInvoiceOrderPageQueryRespVO
   * @throws ApiError
   */
  public static postInvoiceOrderPage(
    requestBody?: InvoiceOrderPageQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseInvoiceOrderPageQueryRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/invoice/order/page',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询发票关联订单
   * @param requestBody
   * @returns AppResponseListInvoiceOrderVO
   * @throws ApiError
   */
  public static postInvoiceOrderRelated(
    requestBody?: InvoiceRelatedOrderQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListInvoiceOrderVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/invoice/order/related',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
