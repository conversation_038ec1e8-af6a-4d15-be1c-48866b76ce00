/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponse_UpOrderRefundRespDTO_ } from '../models/AppResponse_UpOrderRefundRespDTO_';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class UserWalletService {
  /**
   * 退款订单2
   * 这是一个测试
   * @param orderId
   * @param amount
   * @param refundCode
   * @returns AppResponse_UpOrderRefundRespDTO_
   * @throws ApiError
   */
  public static postWalletRefund2Order(
    {
      orderId,
      amount,
      refundCode,
    }: {
      orderId: string;
      amount: string;
      refundCode: string;
    },
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpOrderRefundRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/refund2/order/{orderId}/{amount}/{refundCode}',
        path: {
          orderId: orderId,
          amount: amount,
          refundCode: refundCode,
        },
      },
    );
  }

  /**
   * downloadFile
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postWalletTestWithdrawRetryJob(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/test/withdraw/retry/job',
      },
    );
  }
}
