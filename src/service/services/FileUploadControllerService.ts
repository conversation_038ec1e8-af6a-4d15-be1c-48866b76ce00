/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseFileUploadRespVO } from '../models/AppResponseFileUploadRespVO';
import type { ImageUploadRequest } from '../models/ImageUploadRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class FileUploadControllerService {
  /**
   * 文件上传
   * 这是一个测试
   * @param requestBody
   * file
   * @returns AppResponseFileUploadRespVO
   * @throws ApiError
   */
  public static postFileUpload(
    requestBody?: {
      /**
       * 文件
       */
      file: Blob;
    },
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseFileUploadRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/file/upload',
        body: requestBody || {},
        mediaType: 'multipart/form-data',
      },
    );
  }

  /**
   * 图片链接上传
   * @param requestBody
   * @returns AppResponseFileUploadRespVO
   * @throws ApiError
   */
  public static postFileUploadImgUrl(
    requestBody?: ImageUploadRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseFileUploadRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/file/uploadImgUrl',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
