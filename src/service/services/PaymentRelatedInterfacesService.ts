/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse } from '../models/AppResponse';
import type { AppResponseSuNingOrderInfoResponse } from '../models/AppResponseSuNingOrderInfoResponse';
import type { AppResponseSuNingPayResponse } from '../models/AppResponseSuNingPayResponse';
import type { SuNingOrderInfoRequest } from '../models/SuNingOrderInfoRequest';
import type { SuNingPayRequest } from '../models/SuNingPayRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class PaymentRelatedInterfacesService {
  /**
   * 支付申请
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseSuNingPayResponse
   * @throws ApiError
   */
  public static postTradePay(
    requestBody?: SuNingPayRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuNingPayResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/trade/pay',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 退款申请
   * @param requestBody
   * @returns AppResponse
   * @throws ApiError
   */
  public static postTradeRefund(
    requestBody?: SuNingOrderInfoRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/trade/refund',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 订单详情
   * @param requestBody
   * @returns AppResponseSuNingOrderInfoResponse
   * @throws ApiError
   */
  public static postTradeOrderInfo(
    requestBody?: SuNingOrderInfoRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuNingOrderInfoResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/trade/order/info',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 取消订单
   * @param requestBody
   * @returns AppResponse
   * @throws ApiError
   */
  public static postTradeCancel(
    requestBody?: SuNingOrderInfoRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/trade/cancel',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
