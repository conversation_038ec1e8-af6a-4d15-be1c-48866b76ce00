/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_FeedbackResp_ } from '../models/AppResponse_FeedbackResp_';
import type { AppResponse_PageRespVO_FeedbackResp_ } from '../models/AppResponse_PageRespVO_FeedbackResp_';
import type { FeedbackAddReq } from '../models/FeedbackAddReq';
import type { FeedbackDetailReq } from '../models/FeedbackDetailReq';
import type { FeedbackQueryReq } from '../models/FeedbackQueryReq';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class FeedbackRelatedService {
  /**
   * 提交问题反馈
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_FeedbackResp_
   * @throws ApiError
   */
  public static postFeedbackAdd(
    requestBody?: FeedbackAddReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_FeedbackResp_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/feedback/add',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 问题反馈列表
   * @param requestBody
   * @returns AppResponse_PageRespVO_FeedbackResp_
   * @throws ApiError
   */
  public static postFeedbackList(
    requestBody?: FeedbackQueryReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_FeedbackResp_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/feedback/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 问题反馈详情
   * @param requestBody
   * @returns AppResponse_FeedbackResp_
   * @throws ApiError
   */
  public static postFeedbackDetail(
    requestBody?: FeedbackDetailReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_FeedbackResp_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/feedback/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
