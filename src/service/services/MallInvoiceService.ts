/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_List_MallInvoiceOrderRes_ } from '../models/AppResponse_List_MallInvoiceOrderRes_';
import type { AppResponse_PageRespVO_MallInvoiceOrderRes_ } from '../models/AppResponse_PageRespVO_MallInvoiceOrderRes_';
import type { InvoiceDetailRequest } from '../models/InvoiceDetailRequest';
import type { MallInvoiceQueryRequest } from '../models/MallInvoiceQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class MallInvoiceService {
  /**
   * 查询商城可开票订单列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_PageRespVO_MallInvoiceOrderRes_
   * @throws ApiError
   */
  public static postInvoiceMallQueryCanInvoiceOrderPage(
    requestBody?: MallInvoiceQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_MallInvoiceOrderRes_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/invoice/mall/queryCanInvoiceOrderPage',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询商城发票关联订单
   * @param requestBody
   * @returns AppResponse_List_MallInvoiceOrderRes_
   * @throws ApiError
   */
  public static postInvoiceMallQueryInvoiceRelateOrder(
    requestBody?: InvoiceDetailRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_MallInvoiceOrderRes_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/invoice/mall/queryInvoiceRelateOrder',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
