/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_BusinessChargingViewRespVO_ } from '../models/AppResponse_BusinessChargingViewRespVO_';
import type { AppResponse_BusinessParkingViewRespVO_ } from '../models/AppResponse_BusinessParkingViewRespVO_';
import type { AppResponse_BusinessResourceOverviewRepsVO_ } from '../models/AppResponse_BusinessResourceOverviewRepsVO_';
import type { AppResponse_UserOverviewRespVO_ } from '../models/AppResponse_UserOverviewRespVO_';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class BusinessViewService {
  /**
   * 资源总览（与时间条件无关系）
   * 这是一个测试
   * @returns AppResponse_BusinessResourceOverviewRepsVO_
   * @throws ApiError
   */
  public static getDashboardBusinessResourceOverview(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_BusinessResourceOverviewRepsVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/business/resourceOverview',
      },
    );
  }

  /**
   * 资源总览 - 用户数据概览
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_UserOverviewRespVO_
   * @throws ApiError
   */
  public static getDashboardBusinessGetUserOverview(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UserOverviewRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/business/getUserOverview',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 停车经营视图
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_BusinessParkingViewRespVO_
   * @throws ApiError
   */
  public static getDashboardBusinessParkingView(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_BusinessParkingViewRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/business/parkingView',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 充电经营视图
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_BusinessChargingViewRespVO_
   * @throws ApiError
   */
  public static getDashboardBusinessChargingView(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_BusinessChargingViewRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/business/chargingView',
        query: {
          type: type,
        },
      },
    );
  }
}
