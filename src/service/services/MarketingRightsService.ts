/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_List_RightsFlowDetailVO_ } from '../models/AppResponse_List_RightsFlowDetailVO_';
import type { AppResponse_PrizeInfoResponse_ } from '../models/AppResponse_PrizeInfoResponse_';
import type { AppResponse_RightsCountQueryResponse_ } from '../models/AppResponse_RightsCountQueryResponse_';
import type { AppResponse_RightsFlowResponse_ } from '../models/AppResponse_RightsFlowResponse_';
import type { AppResponse_RightsPreviewFlowResponse_ } from '../models/AppResponse_RightsPreviewFlowResponse_';
import type { PrizePreviewRequest } from '../models/PrizePreviewRequest';
import type { PrizeQueryRequest } from '../models/PrizeQueryRequest';
import type { RightsCountQueryRequest } from '../models/RightsCountQueryRequest';
import type { RightsPreviewQueryRequest } from '../models/RightsPreviewQueryRequest';
import type { RightsQueryRequest } from '../models/RightsQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class MarketingRightsService {
  /**
   * 查询奖品详情
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_PrizeInfoResponse_
   * @throws ApiError
   */
  public static postMkplatRightsQueryPrize(
    requestBody?: PrizeQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PrizeInfoResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mkplat/rights/queryPrize',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询用户权益列表
   * @param requestBody
   * @returns AppResponse_RightsFlowResponse_
   * @throws ApiError
   */
  public static postMkplatRightsQueryUserRights(
    requestBody?: RightsQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_RightsFlowResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mkplat/rights/queryUserRights',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询用户权益数量
   * @param requestBody
   * @returns AppResponse_RightsCountQueryResponse_
   * @throws ApiError
   */
  public static postMkplatRightsCountUserRights(
    requestBody?: RightsCountQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_RightsCountQueryResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mkplat/rights/countUserRights',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 用户权益预览
   * @param requestBody
   * @returns AppResponse_RightsPreviewFlowResponse_
   * @throws ApiError
   */
  public static postMkplatRightsPreview(
    requestBody?: RightsPreviewQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_RightsPreviewFlowResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mkplat/rights/preview',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 用户权益预览(增加优惠券核销配置规则过滤的逻辑)
   * @param requestBody
   * @returns AppResponse_List_RightsFlowDetailVO_
   * @throws ApiError
   */
  public static postMkplatRightsRuleFilterPreview(
    requestBody?: PrizePreviewRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_RightsFlowDetailVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mkplat/rights/ruleFilter/preview',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
