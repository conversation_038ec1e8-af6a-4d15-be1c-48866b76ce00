/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { NavigationRecordSaveRequest } from '../models/NavigationRecordSaveRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class BerthNavigationService {
  /**
   * 保存精准泊位导航记录
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postNavigationRecordSave(
    requestBody?: NavigationRecordSaveRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/navigation/record/save',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
