/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseListProtocolQueryRespVO } from '../models/AppResponseListProtocolQueryRespVO';
import type { AppResponseProtocolDetailRespVO } from '../models/AppResponseProtocolDetailRespVO';
import type { ProtocolQueryRequest } from '../models/ProtocolQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ProtocolRelatedService {
  /**
   * 协议列表查询
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseListProtocolQueryRespVO
   * @throws ApiError
   */
  public static postProtocolList(
    requestBody?: ProtocolQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListProtocolQueryRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/protocol/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 协议详情
   * @param protocolId
   * @returns AppResponseProtocolDetailRespVO
   * @throws ApiError
   */
  public static getProtocolDetail(
    protocolId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseProtocolDetailRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/protocol/detail',
        query: {
          protocolId: protocolId,
        },
      },
    );
  }
}
