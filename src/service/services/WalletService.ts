/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_BalanceQueryRespVO_ } from '../models/AppResponse_BalanceQueryRespVO_';
import type { AppResponse_BalanceWithdrawRespVO_ } from '../models/AppResponse_BalanceWithdrawRespVO_';
import type { AppResponse_PageRespVO_PrizeQueryRespVO_ } from '../models/AppResponse_PageRespVO_PrizeQueryRespVO_';
import type { AppResponse_PageRespVO_WalletRecordLogRespVO_ } from '../models/AppResponse_PageRespVO_WalletRecordLogRespVO_';
import type { AppResponse_PrizeCountRespVO_ } from '../models/AppResponse_PrizeCountRespVO_';
import type { AppResponse_PrizeQueryRespVO_ } from '../models/AppResponse_PrizeQueryRespVO_';
import type { AppResponse_UpCcbOrderPayRespVO_ } from '../models/AppResponse_UpCcbOrderPayRespVO_';
import type { AppResponse_UpOweOrderRespVO_ } from '../models/AppResponse_UpOweOrderRespVO_';
import type { BalanceWithdrawReqVO } from '../models/BalanceWithdrawReqVO';
import type { PageReqVO } from '../models/PageReqVO';
import type { PrizeCountRequest } from '../models/PrizeCountRequest';
import type { PrizeDetailRequest } from '../models/PrizeDetailRequest';
import type { PrizeListRequest } from '../models/PrizeListRequest';
import type { UpOrderReqVO } from '../models/UpOrderReqVO';
import type { UpOweOrderReqVO } from '../models/UpOweOrderReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class WalletService {
  /**
   * 欠费订单补交
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_UpOweOrderRespVO_
   * @throws ApiError
   */
  public static postWalletOweOrderRecharge(
    requestBody?: UpOweOrderReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpOweOrderRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/owe/order/recharge',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 用户余额提现接口
   * @param requestBody
   * @returns AppResponse_BalanceWithdrawRespVO_
   * @throws ApiError
   */
  public static postWalletBalanceWithdraw(
    requestBody?: BalanceWithdrawReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_BalanceWithdrawRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/balanceWithdraw',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 余额查询
   * @returns AppResponse_BalanceQueryRespVO_
   * @throws ApiError
   */
  public static postWalletBalanceQuery(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_BalanceQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/balanceQuery',
      },
    );
  }

  /**
   * 用户充值下单接口
   * @param requestBody
   * @returns AppResponse_UpCcbOrderPayRespVO_
   * @throws ApiError
   */
  public static postWalletRecharge(
    requestBody?: UpOrderReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpCcbOrderPayRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/recharge',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 钱包明细查询
   * @param requestBody
   * @returns AppResponse_PageRespVO_WalletRecordLogRespVO_
   * @throws ApiError
   */
  public static postWalletWalletDetailLog(
    requestBody?: PageReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_WalletRecordLogRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/walletDetailLog',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询优惠券列表
   * @param requestBody
   * @returns AppResponse_PageRespVO_PrizeQueryRespVO_
   * @throws ApiError
   */
  public static postWalletPrizeQuery(
    requestBody?: PrizeListRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_PrizeQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/prizeQuery',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查询优惠券详情
   * @param requestBody
   * @returns AppResponse_PrizeQueryRespVO_
   * @throws ApiError
   */
  public static postWalletPrizeDetailQuery(
    requestBody?: PrizeDetailRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PrizeQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/prizeDetailQuery',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 统计优惠券
   * @param requestBody
   * @returns AppResponse_PrizeCountRespVO_
   * @throws ApiError
   */
  public static postWalletPrizeCount(
    requestBody?: PrizeCountRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PrizeCountRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/prize/count',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
