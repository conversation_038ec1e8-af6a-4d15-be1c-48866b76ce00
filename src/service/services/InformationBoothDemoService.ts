/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse } from '../models/AppResponse';
import type { AppResponse_List_OnlineGameAreaInfo_ } from '../models/AppResponse_List_OnlineGameAreaInfo_';
import type { AppResponse_UserListQueryRespVO_ } from '../models/AppResponse_UserListQueryRespVO_';
import type { AppResponse_UserQueryRespVO_ } from '../models/AppResponse_UserQueryRespVO_';
import type { AppResponseString } from '../models/AppResponseString';
import type { UserListQueryReqVO } from '../models/UserListQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class InformationBoothDemoService {
  /**
   * 测试创建token
   * 这是一个测试
   * @param userId 用户uid
   * @returns AppResponseString
   * @throws ApiError
   */
  public static getUserCreate(
    userId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/user/create',
        query: {
          userId: userId,
        },
      },
    );
  }

  /**
   * 测试DB和redis
   * 「已废弃」
   * @returns AppResponse_UserQueryRespVO_
   * @throws ApiError
   */
  public static getUserGet(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UserQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/user/get',
      },
    );
  }

  /**
   * 测试DB分页
   * 「已废弃」
   * @param requestBody
   * @returns AppResponse_UserListQueryRespVO_
   * @throws ApiError
   */
  public static postUserList(
    requestBody?: UserListQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UserListQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/user/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 测试nacos
   * 「已废弃」
   * @returns AppResponseString
   * @throws ApiError
   */
  public static getUserGetNacosValue(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/user/getNacosValue',
      },
    );
  }

  /**
   * 测试nacos
   * 「已废弃」
   * @returns AppResponse_List_OnlineGameAreaInfo_
   * @throws ApiError
   */
  public static getUserGetArea(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_OnlineGameAreaInfo_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/user/getArea',
      },
    );
  }

  /**
   * 测试cache
   * 「已废弃」
   * @returns AppResponse
   * @throws ApiError
   */
  public static getUserCachetest(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/user/cachetest',
      },
    );
  }
}
