/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_MapDataQueryRespVO_ } from '../models/AppResponse_MapDataQueryRespVO_';
import type { MapDataQueryReqVO } from '../models/MapDataQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class MapSearchService {
  /**
   * 查询地图相关信息
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_MapDataQueryRespVO_
   * @throws ApiError
   */
  public static postMapSearch(
    requestBody?: MapDataQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_MapDataQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/map/search',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
