/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_WeatherQueryRespVO_ } from '../models/AppResponse_WeatherQueryRespVO_';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class WeatherControllerService {
  /**
   * 获取当前天气
   * 这是一个测试
   * @param cityCode 不传的时候，默认取配置值${gaode.weather.defaultCityCode}
   * @returns AppResponse_WeatherQueryRespVO_
   * @throws ApiError
   */
  public static getWeatherGetLiveWeather(
    cityCode?: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_WeatherQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/weather/getLiveWeather',
        query: {
          cityCode: cityCode,
        },
      },
    );
  }
}
