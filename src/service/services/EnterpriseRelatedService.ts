/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_EnterpriseStationPageQueryRespVO_ } from '../models/AppResponse_EnterpriseStationPageQueryRespVO_';
import type { AppResponse_List_EnterpriseListQueryRespVO_ } from '../models/AppResponse_List_EnterpriseListQueryRespVO_';
import type { AppResponse_UserEnterpriseMarkRespVO_ } from '../models/AppResponse_UserEnterpriseMarkRespVO_';
import type { EnterpriseStationPageQueryReqVO } from '../models/EnterpriseStationPageQueryReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class EnterpriseRelatedService {
  /**
   * 用户中心获取充电企业标识
   * 这是一个测试
   * @returns AppResponse_UserEnterpriseMarkRespVO_
   * @throws ApiError
   */
  public static postEnterpriseUserMark(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UserEnterpriseMarkRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/enterprise/userMark',
      },
    );
  }

  /**
   * 用户中心充电企业号信息
   * @returns AppResponse_List_EnterpriseListQueryRespVO_
   * @throws ApiError
   */
  public static postEnterpriseUserRelatedList(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_List_EnterpriseListQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/enterprise/userRelatedList',
      },
    );
  }

  /**
   * 企业号站点信息
   * @param requestBody
   * @returns AppResponse_EnterpriseStationPageQueryRespVO_
   * @throws ApiError
   */
  public static postEnterpriseStationList(
    requestBody?: EnterpriseStationPageQueryReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_EnterpriseStationPageQueryRespVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/enterprise/station/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
