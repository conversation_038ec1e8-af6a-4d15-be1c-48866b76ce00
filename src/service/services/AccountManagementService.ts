/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseAuthTokenRes } from '../models/AppResponseAuthTokenRes';
import type { AppResponseBoolean } from '../models/AppResponseBoolean';
import type { AppResponseMobileRegCheckRsp } from '../models/AppResponseMobileRegCheckRsp';
import type { AppResponseString } from '../models/AppResponseString';
import type { AppResponseSuccessRes } from '../models/AppResponseSuccessRes';
import type { AppResponseUserAppLoginRsp } from '../models/AppResponseUserAppLoginRsp';
import type { AppResponseUserInfoQueryRsp } from '../models/AppResponseUserInfoQueryRsp';
import type { AppResponseUserInfoUpdateRes } from '../models/AppResponseUserInfoUpdateRes';
import type { AppResponseUserStatusCheckCauseRes } from '../models/AppResponseUserStatusCheckCauseRes';
import type { ChangeMobileReq } from '../models/ChangeMobileReq';
import type { MessageRequest } from '../models/MessageRequest';
import type { MobileRegCheckReq } from '../models/MobileRegCheckReq';
import type { MobileRegisterReq } from '../models/MobileRegisterReq';
import type { RefreshTokenReq } from '../models/RefreshTokenReq';
import type { TestTokenReq } from '../models/TestTokenReq';
import type { UserAppLoginReq } from '../models/UserAppLoginReq';
import type { UserInfoUpdateReq } from '../models/UserInfoUpdateReq';
import type { UserResponseData_Object_ } from '../models/UserResponseData_Object_';
import type { UserSpecialRegisterReq } from '../models/UserSpecialRegisterReq';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class AccountManagementService {
  /**
   * 校验手机是否已注册
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseMobileRegCheckRsp
   * @throws ApiError
   */
  public static postUserCheckMobileRegister(
    requestBody?: MobileRegCheckReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseMobileRegCheckRsp]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/checkMobileRegister',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 用户注册
   * @param requestBody
   * @returns AppResponseUserAppLoginRsp
   * @throws ApiError
   */
  public static postUserRegister(
    requestBody?: MobileRegisterReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserAppLoginRsp]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/register',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取用户个人信息
   * @returns AppResponseUserInfoQueryRsp
   * @throws ApiError
   */
  public static postUserGetUserInfo(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserInfoQueryRsp]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/getUserInfo',
      },
    );
  }

  /**
   * APP登录
   * @param requestBody
   * @returns AppResponseUserAppLoginRsp
   * @throws ApiError
   */
  public static postUserLoginApp(
    requestBody?: UserAppLoginReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserAppLoginRsp]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/loginApp',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 退出登录
   * @returns AppResponseBoolean
   * @throws ApiError
   */
  public static postUserLogout(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseBoolean]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/logout',
      },
    );
  }

  /**
   * 申请注销账号
   * @param cancelReason
   * @param requestBody
   * @returns AppResponseSuccessRes
   * @throws ApiError
   */
  public static postUserApplyCancel(
    cancelReason: string,
    requestBody?: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuccessRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/applyCancel',
        query: {
          cancelReason: cancelReason,
        },
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取用户账号异常信息
   * @returns AppResponseUserStatusCheckCauseRes
   * @throws ApiError
   */
  public static getUserGetUserStatus(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserStatusCheckCauseRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/user/getUserStatus',
      },
    );
  }

  /**
   * 生成测试token
   * @param requestBody
   * @returns UserResponseData_Object_
   * @throws ApiError
   */
  public static postUserTestToken(
    requestBody?: TestTokenReq,
    config = {},
  ): Promise<[undefined | Error, undefined | UserResponseData_Object_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/testToken',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 已读申请注销被拒接口
   * @returns AppResponseSuccessRes
   * @throws ApiError
   */
  public static postUserReadCancelRefuse(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuccessRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/readCancelRefuse',
      },
    );
  }

  /**
   * 撤销注销账号
   * @returns AppResponseSuccessRes
   * @throws ApiError
   */
  public static postUserRevokeCancel(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuccessRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/revokeCancel',
      },
    );
  }

  /**
   * sendTestMessage
   * @param requestBody
   * @returns UserResponseData_Object_
   * @throws ApiError
   */
  public static postUserTestMessageSend(
    requestBody?: MessageRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | UserResponseData_Object_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/test/message/send',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 刷新令牌
   * @param requestBody
   * @returns AppResponseAuthTokenRes
   * @throws ApiError
   */
  public static postUserRefreshToken(
    requestBody?: RefreshTokenReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseAuthTokenRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/refresh/token',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 更新用户个人信息
   * @param requestBody
   * @returns AppResponseUserInfoUpdateRes
   * @throws ApiError
   */
  public static postUserUpdateUserInfo(
    requestBody?: UserInfoUpdateReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseUserInfoUpdateRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/updateUserInfo',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * specialUserRegister
   * @param requestBody
   * @returns UserResponseData_Object_
   * @throws ApiError
   */
  public static postUserTestSpecialRegister(
    requestBody?: UserSpecialRegisterReq,
    config = {},
  ): Promise<[undefined | Error, undefined | UserResponseData_Object_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/test/special/register',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 修改手机号
   * @param requestBody
   * @returns AppResponseSuccessRes
   * @throws ApiError
   */
  public static postUserChangeMobile(
    requestBody?: ChangeMobileReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseSuccessRes]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/changeMobile',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 苏宁联合登录
   * @returns AppResponseString
   * @throws ApiError
   */
  public static postUserSuningUnionLogin(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseString]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/user/suningUnionLogin',
      },
    );
  }
}
