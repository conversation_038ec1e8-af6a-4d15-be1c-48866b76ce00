/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponse_InvoiceTitleResponse_ } from '../models/AppResponse_InvoiceTitleResponse_';
import type { AppResponse_PageRespVO_InvoiceTitleResponse_ } from '../models/AppResponse_PageRespVO_InvoiceTitleResponse_';
import type { InvoiceTitleAddRequest } from '../models/InvoiceTitleAddRequest';
import type { InvoiceTitleIdRequest } from '../models/InvoiceTitleIdRequest';
import type { InvoiceTitleSetDefaultRequest } from '../models/InvoiceTitleSetDefaultRequest';
import type { PageReqVO } from '../models/PageReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class InvoiceHeaderService {
  /**
   * 登记/更新发票抬头
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postInvoiceTitleSave(
    requestBody?: InvoiceTitleAddRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/title/save',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 查看发票抬头详情
   * @param requestBody
   * @returns AppResponse_InvoiceTitleResponse_
   * @throws ApiError
   */
  public static postInvoiceTitleDetail(
    requestBody?: InvoiceTitleIdRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_InvoiceTitleResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/title/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 设置默认发票抬头
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postInvoiceTitleSetDefault(
    requestBody?: InvoiceTitleSetDefaultRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/title/set/default',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 删除发票抬头
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postInvoiceTitleDelete(
    requestBody?: InvoiceTitleIdRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/title/delete',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 发票抬头查询列表
   * @param requestBody
   * @returns AppResponse_PageRespVO_InvoiceTitleResponse_
   * @throws ApiError
   */
  public static postInvoiceTitleList(
    requestBody?: PageReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_PageRespVO_InvoiceTitleResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/title/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
