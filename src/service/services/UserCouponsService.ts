/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseRightsDetailPageVORightsFlowDetailVO } from '../models/AppResponseRightsDetailPageVORightsFlowDetailVO';
import type { CouponQueryRequest } from '../models/CouponQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class UserCouponsService {
  /**
   * 优惠券列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseRightsDetailPageVORightsFlowDetailVO
   * @throws ApiError
   */
  public static postUserCouponPage(
    requestBody?: CouponQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseRightsDetailPageVORightsFlowDetailVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/user/coupon/page',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
