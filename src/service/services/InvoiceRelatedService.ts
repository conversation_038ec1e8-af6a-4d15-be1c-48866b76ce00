/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ } from '../models/AppResponse_';
import type { AppResponseInvoiceDetailResponse } from '../models/AppResponseInvoiceDetailResponse';
import type { AppResponseListFacadeEnterpriseTitleResponse } from '../models/AppResponseListFacadeEnterpriseTitleResponse';
import type { AppResponseListInvoiceDetailResponse } from '../models/AppResponseListInvoiceDetailResponse';
import type { AppResponsePageRespVOInvoiceListResponse } from '../models/AppResponsePageRespVOInvoiceListResponse';
import type { EnterpriseTitleRequest } from '../models/EnterpriseTitleRequest';
import type { InvoiceAgainIssueRequest } from '../models/InvoiceAgainIssueRequest';
import type { InvoiceDetailRequest } from '../models/InvoiceDetailRequest';
import type { InvoiceHistoryRequest } from '../models/InvoiceHistoryRequest';
import type { InvoiceIssueRequest } from '../models/InvoiceIssueRequest';
import type { InvoiceListRequest } from '../models/InvoiceListRequest';
import type { InvoiceSendEmailRequest } from '../models/InvoiceSendEmailRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class InvoiceRelatedService {
  /**
   * 开具发票
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseInvoiceDetailResponse
   * @throws ApiError
   */
  public static postInvoiceIssue(
    requestBody?: InvoiceIssueRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseInvoiceDetailResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/issue',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * @deprecated
   * 发票历史查询
   * 「已废弃」
   * @param requestBody
   * @returns AppResponseListInvoiceDetailResponse
   * @throws ApiError
   */
  public static postInvoiceHistorySearch(
    requestBody?: InvoiceHistoryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListInvoiceDetailResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/history/search',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 已开发票列表
   * @param requestBody
   * @returns AppResponsePageRespVOInvoiceListResponse
   * @throws ApiError
   */
  public static postInvoiceList(
    requestBody?: InvoiceListRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePageRespVOInvoiceListResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 发票详情
   * @param requestBody
   * @returns AppResponseInvoiceDetailResponse
   * @throws ApiError
   */
  public static postInvoiceDetail(
    requestBody?: InvoiceDetailRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseInvoiceDetailResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/detail',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 发票重新发送到邮箱
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postInvoiceRePush(
    requestBody?: InvoiceSendEmailRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/rePush',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 重新开票
   * @param requestBody
   * @returns AppResponse_
   * @throws ApiError
   */
  public static postInvoiceIssueAgain(
    requestBody?: InvoiceAgainIssueRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/issue/again',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取发票抬头
   * @param requestBody
   * @returns AppResponseListFacadeEnterpriseTitleResponse
   * @throws ApiError
   */
  public static postInvoiceEnterpriseTitleQuery(
    requestBody?: EnterpriseTitleRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseListFacadeEnterpriseTitleResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/enterprise/title/query',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
