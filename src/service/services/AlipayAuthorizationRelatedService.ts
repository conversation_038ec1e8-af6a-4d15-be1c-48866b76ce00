/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_AdvancedAuthRsp_ } from '../models/AppResponse_AdvancedAuthRsp_';
import type { AppResponse_SilentAuthRsp_ } from '../models/AppResponse_SilentAuthRsp_';
import type { MiniAdvAuthReq } from '../models/MiniAdvAuthReq';
import type { MiniSilentAuthReq } from '../models/MiniSilentAuthReq';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class AlipayAuthorizationRelatedService {
  /**
   * 支付宝静默授权
   * 这是一个测试
   * @param requestBody
   * @returns AppResponse_SilentAuthRsp_
   * @throws ApiError
   */
  public static postAlipayAuthSilent(
    requestBody?: MiniSilentAuthReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_SilentAuthRsp_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/alipay/auth/silent',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 支付宝高级授权
   * @param requestBody
   * @returns AppResponse_AdvancedAuthRsp_
   * @throws ApiError
   */
  public static postAlipayAuthAdvanced(
    requestBody?: MiniAdvAuthReq,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_AdvancedAuthRsp_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/alipay/auth/advanced',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
