/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseMallOrderRespVO } from '../models/AppResponseMallOrderRespVO';
import type { MallOrderQueryRequest } from '../models/MallOrderQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class PointsMallOrderService {
  /**
   * 查询积分商城订单列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponseMallOrderRespVO
   * @throws ApiError
   */
  public static postMallOrderList(
    requestBody?: MallOrderQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseMallOrderRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/mall/order/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
