/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_ChargingRevenueVO_ } from '../models/AppResponse_ChargingRevenueVO_';
import type { AppResponse_CloseParkingRevenueVO_ } from '../models/AppResponse_CloseParkingRevenueVO_';
import type { AppResponse_MallRevenueVO_ } from '../models/AppResponse_MallRevenueVO_';
import type { AppResponse_RevenueOverviewVO_ } from '../models/AppResponse_RevenueOverviewVO_';
import type { AppResponse_RoadParkingRevenueVO_ } from '../models/AppResponse_RoadParkingRevenueVO_';
import type { AppResponse_StationTypePropVO_ } from '../models/AppResponse_StationTypePropVO_';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class RevenueViewService {
  /**
   * 营收总览
   * 这是一个测试
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_RevenueOverviewVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetRevenueOverview(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_RevenueOverviewVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getRevenueOverview',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 路边停车营收
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_RoadParkingRevenueVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetRoadParkingRevenue(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_RoadParkingRevenueVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getRoadParkingRevenue',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 场库停车营收
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_CloseParkingRevenueVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetCloseParkingRevenue(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_CloseParkingRevenueVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getCloseParkingRevenue',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 场库场站类型数量占比
   * @returns AppResponse_StationTypePropVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetCloseStationTypeProp(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationTypePropVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getCloseStationTypeProp',
      },
    );
  }

  /**
   * 充电桩营收
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_ChargingRevenueVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetChargingRevenue(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_ChargingRevenueVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getChargingRevenue',
        query: {
          type: type,
        },
      },
    );
  }

  /**
   * 充电场站类型数量占比
   * @returns AppResponse_StationTypePropVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetChargingStationTypeProp(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_StationTypePropVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getChargingStationTypeProp',
      },
    );
  }

  /**
   * 商城营收
   * @param type 时间类型 CURRENT_DAY 本日 LAST_30_DAY 近30日 LAST_12_MONTH 近12月
   * @returns AppResponse_MallRevenueVO_
   * @throws ApiError
   */
  public static getDashboardRevenueGetMallRevenue(
    type: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_MallRevenueVO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/dashboard/revenue/getMallRevenue',
        query: {
          type: type,
        },
      },
    );
  }
}
