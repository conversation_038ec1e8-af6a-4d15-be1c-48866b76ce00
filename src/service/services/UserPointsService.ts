/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponsePageRespVOPointsFlowRespVO } from '../models/AppResponsePageRespVOPointsFlowRespVO';
import type { AppResponsePointsQueryRespVO } from '../models/AppResponsePointsQueryRespVO';
import type { PointsFlowQueryRequest } from '../models/PointsFlowQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class UserPointsService {
  /**
   * 用户积分查询
   * 这是一个测试
   * @returns AppResponsePointsQueryRespVO
   * @throws ApiError
   */
  public static getPointsQueryUserPoints(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePointsQueryRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/api/points/queryUserPoints',
      },
    );
  }

  /**
   * 用户积分流水查询
   * @param requestBody
   * @returns AppResponsePageRespVOPointsFlowRespVO
   * @throws ApiError
   */
  public static postPointsUserFlow(
    requestBody?: PointsFlowQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePageRespVOPointsFlowRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/points/userFlow',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
