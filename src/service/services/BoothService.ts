/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseBannerQueryRespVO } from '../models/AppResponseBannerQueryRespVO';
import type { AppResponseBoolean } from '../models/AppResponseBoolean';
import type { BannerQueryRequest } from '../models/BannerQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class BoothService {
  /**
   * 设置展位元素顺序
   * 这是一个测试
   * @param requestBody
   * bannerId
   * elementOrder
   * @returns AppResponseBoolean
   * @throws ApiError
   */
  public static postBannerSetOrder(
    requestBody?: {
      /**
       * 展位ID
       */
      bannerId: number;
      /**
       * 展位元素（元素ID数组按顺序排列）
       */
      elementOrder: Array<number>;
    },
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseBoolean]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/banner/setOrder',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 展位获取接口
   * @param requestBody
   * @returns AppResponseBannerQueryRespVO
   * @throws ApiError
   */
  public static postBannerList(
    requestBody?: BannerQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseBannerQueryRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/banner/list',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
