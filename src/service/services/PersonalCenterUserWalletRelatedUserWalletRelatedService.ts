/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponse_FileDownloadRespDTO_ } from '../models/AppResponse_FileDownloadRespDTO_';
import type { AppResponse_MerchantFlowFileGenerateRespDTO_ } from '../models/AppResponse_MerchantFlowFileGenerateRespDTO_';
import type { AppResponse_UpOrderPayQueryRespDTO_ } from '../models/AppResponse_UpOrderPayQueryRespDTO_';
import type { AppResponse_UpOrderRefundRespDTO_ } from '../models/AppResponse_UpOrderRefundRespDTO_';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class PersonalCenterUserWalletRelatedUserWalletRelatedService {
  /**
   * 支付订单查询
   * 这是一个测试
   * @param orderId
   * @returns AppResponse_UpOrderPayQueryRespDTO_
   * @throws ApiError
   */
  public static postWalletPayOrderQuery(
    orderId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpOrderPayQueryRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/pay/order/query/{orderId}',
        path: {
          orderId: orderId,
        },
      },
    );
  }

  /**
   * 退款订单
   * @param orderId
   * @param amount
   * @returns AppResponse_UpOrderRefundRespDTO_
   * @throws ApiError
   */
  public static postWalletRefundOrder(
    orderId: string,
    amount: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpOrderRefundRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/refund/order/{orderId}/{amount}',
        path: {
          orderId: orderId,
          amount: amount,
        },
      },
    );
  }

  /**
   * 退款订单查询
   * @param orderId
   * @param refundId
   * @returns AppResponse_UpOrderPayQueryRespDTO_
   * @throws ApiError
   */
  public static postWalletRefundOrderQuery(
    orderId: string,
    refundId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_UpOrderPayQueryRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/refund/order/query/{orderId}/{refundId}',
        path: {
          orderId: orderId,
          refundId: refundId,
        },
      },
    );
  }

  /**
   * generateFile
   * @param settleTime
   * @returns AppResponse_MerchantFlowFileGenerateRespDTO_
   * @throws ApiError
   */
  public static postWalletFileGenerate(
    settleTime: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_MerchantFlowFileGenerateRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/file/generate/{settleTime}',
        path: {
          settleTime: settleTime,
        },
      },
    );
  }

  /**
   * downloadFile
   * @param fileName
   * @returns AppResponse_FileDownloadRespDTO_
   * @throws ApiError
   */
  public static postWalletFileDownload(
    fileName: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponse_FileDownloadRespDTO_]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/wallet/file/download/{fileName}',
        path: {
          fileName: fileName,
        },
      },
    );
  }
}
