/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { PayOrderNotifyReqDTO } from '../models/PayOrderNotifyReqDTO';
import type { RefundOrderNotifyReqDTO } from '../models/RefundOrderNotifyReqDTO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class PersonalCenterOrderNotificationService {
  /**
   * 订单支付成功回调通知接口
   * 这是一个测试
   * @param requestBody
   * @returns string
   * @throws ApiError
   */
  public static postOpenOrderPayOrderNotify(
    requestBody?: PayOrderNotifyReqDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | string]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/open/api/order/payOrderNotify',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 订单退款成功回调通知接口
   * 岳阳统一支付回调通知暂只支持 channel 为 WECHAT / SXF / IPSPAY ,其他支付渠道为同步退款或者可通过查询接口查询
   * @param requestBody
   * @returns string
   * @throws ApiError
   */
  public static postOpenOrderRefundOrderNotify(
    requestBody?: RefundOrderNotifyReqDTO,
    config = {},
  ): Promise<[undefined | Error, undefined | string]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/open/api/order/refundOrderNotify',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
