/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponsePageRespVOInvoicableOrderResponse } from '../models/AppResponsePageRespVOInvoicableOrderResponse';
import type { InvoicableOrderQueryRequest } from '../models/InvoicableOrderQueryRequest';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class InvoiceRelatedInterfaceService {
  /**
   * 可开票订单列表
   * 这是一个测试
   * @param requestBody
   * @returns AppResponsePageRespVOInvoicableOrderResponse
   * @throws ApiError
   */
  public static postInvoiceCanOpenList(
    requestBody?: InvoicableOrderQueryRequest,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePageRespVOInvoicableOrderResponse]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/app-server/api/invoice/canOpenList',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }
}
