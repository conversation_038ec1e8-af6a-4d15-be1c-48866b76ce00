/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponseEvaluationConfigRespVO } from '../models/AppResponseEvaluationConfigRespVO';
import type { AppResponseEvaluationRespVO } from '../models/AppResponseEvaluationRespVO';
import type { AppResponseVoid } from '../models/AppResponseVoid';
import type { EvaluationReqVO } from '../models/EvaluationReqVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class EvaluationInterfaceService {
  /**
   * 获取评价配置模版
   * 这是一个测试
   * @returns AppResponseEvaluationConfigRespVO
   * @throws ApiError
   */
  public static postEvaluationConfig(
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseEvaluationConfigRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/evaluation/config',
      },
    );
  }

  /**
   * 提交订单评价
   * @param requestBody
   * @returns AppResponseVoid
   * @throws ApiError
   */
  public static postEvaluationSubmit(
    requestBody?: EvaluationReqVO,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseVoid]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'POST',
        url: '/charging-server/api/evaluation/submit',
        body: requestBody || {},
        mediaType: 'application/json',
      },
    );
  }

  /**
   * 获取订单评价
   * @param orderId
   * @param payTime
   * @returns AppResponseEvaluationRespVO
   * @throws ApiError
   */
  public static getEvaluationInfo(
    orderId: string,
    payTime: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponseEvaluationRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/charging-server/api/evaluation/info',
        query: {
          orderId: orderId,
          payTime: payTime,
        },
      },
    );
  }
}
