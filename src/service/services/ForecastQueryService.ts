/* prettier-ignore-file */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

/* generated using @soeasy/service-codegen -- do not edit */
import type { AppResponsePredictionChargeRespVO } from '../models/AppResponsePredictionChargeRespVO';
import type { AppResponsePredictionParkRespVO } from '../models/AppResponsePredictionParkRespVO';

import { apiConfig } from '../core/APIConfig';
import { request as __request } from '../core/request';

export class ForecastQueryService {
  /**
   * 查询车场泊位预测
   * 这是一个测试
   * @param stationId 场站ID
   * @returns AppResponsePredictionParkRespVO
   * @throws ApiError
   */
  public static getPredictionPark(
    stationId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePredictionParkRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/prediction/park',
        query: {
          stationId: stationId,
        },
      },
    );
  }

  /**
   * 查询充电枪数预测
   * @param stationId 场站ID
   * @returns AppResponsePredictionChargeRespVO
   * @throws ApiError
   */
  public static getPredictionCharge(
    stationId: string,
    config = {},
  ): Promise<[undefined | Error, undefined | AppResponsePredictionChargeRespVO]> {
    return __request(
      { ...apiConfig, ...config },
      {
        method: 'GET',
        url: '/app-server/prediction/charge',
        query: {
          stationId: stationId,
        },
      },
    );
  }
}
