import { defineStore } from 'pinia';
import { StationListQueryReqVO, StationService } from '@/service';
import { useSwitch } from '@/pages/nearby/hooks/switch';
import { getDistance } from '@/utils/geo';
import { isPointInRegion } from '@/pages/nearby/hooks/useMapContext';
import { getScaleDistanceAndDistance } from '@/pages/nearby/util';
import { DEFAULTGEO } from '@/config';

export const useNearBy = defineStore('nearBy', () => {
  const { status: isCharging, toggle: toggleCharging } = useSwitch(true);
  const activeMarkerDetail = ref({});
  const chargeType = ref<StationListQueryReqVO.chargeType>(StationListQueryReqVO.chargeType.DC);
  const sortType = ref<StationListQueryReqVO.sortType>('');
  const markersChargingList = ref<any>([]);
  // 地图中心点
  const centerGeo = ref({ lon: DEFAULTGEO.longitude, lat: DEFAULTGEO.latitude });
  // 之前的位置，用于计算是否需要重新请求,默认 0，避免第一次不请求
  const prevCenterGeo = ref({ lon: 0, lat: 0 });
  // 用户所在位置
  const myGeo = ref({ lon: DEFAULTGEO.longitude, lat: DEFAULTGEO.latitude });
  const locationInit = ref(false);

  const searchMarker = ref({});
  const beforeScale = ref(17);
  const scale = ref(17);

  const getMarkerDetail = async (stationId: string) => {
    // 获取详情，默认用户所在位置
    const { lon, lat } = myGeo.value;
    const [err, res] = await StationService.postStationDetail({ stationId, lon, lat });

    if (res?.data) {
      const { data } = res;
      activeMarkerDetail.value = {
        stationName: data.stationName,
        acIdle: data.acIdle,
        acPrice: data.acPrice?.price,
        dcIdle: data.dcIdle,
        dcTotal: data.dcTotal,
        dcPrice: data.dcPrice?.price,
        stationAddress: data.stationAddress,
        distance: data.distance,
        stationId: data.stationId,
      };
    }
  };

  const setActiveMarkerDetail = (val: any) => {
    activeMarkerDetail.value = val;
  };

  const setLocationInit = (val: boolean) => {
    locationInit.value = val;
  };

  const setMyGeo = (val: any) => {
    myGeo.value = val;
  };

  const setChargeType = (val: string) => {
    chargeType.value = val;
  };
  const setSortType = (val: string) => {
    sortType.value = val;
  };

  const setSearchMarker = (val: any) => {
    searchMarker.value = val;
  };

  const setCenterGeo = (val: any) => {
    console.error('setCenterGeo', val);
    centerGeo.value = val;
  };

  const setPrevCenterGeo = (val) => {
    prevCenterGeo.value = val;
  };

  const regionChangeCustomCallout = async () => {
    if (process.env.UNI_PLATFORM === 'app') {
      // markersChargingList.value = await isPointInRegionApp(markersChargingList.value);
    } else {
      markersChargingList.value = await isPointInRegion(markersChargingList.value);
    }
    // markersChargingList.value = await isPointInRegion(markersChargingList.value);
  };

  const setScale = (val: number) => {
    scale.value = val;
  };

  const searchDataGeo = computed(() => {
    if (searchMarker.value.latitude) {
      return {
        lat: searchMarker.value.latitude,
        lon: searchMarker.value.longitude,
      };
    } else {
      return {
        lat: centerGeo.value.lat,
        lon: centerGeo.value.lon,
      };
    }
  });

  const needRefresh = computed(() => {
    const changeDistance = getDistance(
      centerGeo.value.lat,
      centerGeo.value.lon,
      prevCenterGeo.value.lat,
      prevCenterGeo.value.lon,
    );
    const { distance } = getScaleDistanceAndDistance(scale.value);
    if (distance) {
      if (changeDistance > distance * 0.8) {
        return true;
      }
    }
    // 最后判断一次，scale 变化大于 2 时也需要刷新
    return (
      Math.abs(beforeScale.value - scale.value) >= 2 ||
      (beforeScale.value < 17 && scale.value >= 17)
    );
  });

  watch(
    [chargeType, sortType, centerGeo, scale, locationInit],
    async (newVal, oldVal, onInvalidate) => {
      if (!isCharging.value || !locationInit.value || !needRefresh.value) return;
      let isExpired = false; // 是否过期
      onInvalidate(() => {
        isExpired = true;
      });

      const { distance, scaleDistance } = getScaleDistanceAndDistance(scale.value);
      beforeScale.value = scale.value;

      setPrevCenterGeo(centerGeo.value);
      const [err, res] = await StationService.postStationMap({
        ...searchDataGeo.value,
        distance,
        chargeType: chargeType.value,
        sortType: sortType.value,
        scaleDistance,
        isCluster: scale.value < 17,
      });

      // 如果 isExpired 过期了就不再更新数据了
      if (!isExpired) {
        if (res?.data) {
          markersChargingList.value = res?.data.map((item, index) => {
            if (item.childSize && item.childSize > 1) {
              item.callout = {
                content: `${item.childSize}个充电站`,
                display: 'ALWAYS',
                fontSize: 13,
                padding: 4,
                borderRadius: 5,
                borderWidth: 1,
                borderColor: '#ffffff',
                color: '#000',
                bgColor: '#ffffff',
                textAlign: 'center',
                anchorX: 0,
                anchorY: 0,
              };
            }

            return {
              ...item,
              // 这里不能用stationId id，特别长，会显示不出来
              id: index + 1,
              latitude: item.lat,
              longitude: item.lon,
              iconPath: `${import.meta.env.VITE_ALI_OSS_URL_PREFIX}/images/nearby/charge-icon.png`,
              width: 42,
              height: 42,
            };
          });

          regionChangeCustomCallout();
        }
      }
    },
  );

  return {
    getMarkerDetail,
    activeMarkerDetail,
    setActiveMarkerDetail,
    setChargeType,
    setSortType,
    chargeType,
    sortType,
    markersChargingList,
    setCenterGeo,
    isCharging,
    toggleCharging,
    regionChangeCustomCallout,
    centerGeo,
    setMyGeo,
    setScale,
    scale,
    setSearchMarker,
    searchMarker,
    searchDataGeo,
    setLocationInit,
    locationInit,
  };
});
