import { defineStore } from 'pinia';
import { ref } from 'vue';

interface content {
  userAgreement?: string;
  privacyPolicy?: string;
  about?: string;
}

export const useRichTextStore = defineStore('richText', () => {
  const content = ref<content>({});
  const setContent = (val: content) => {
    console.log('setContent', val);
    content.value = val;
  };
  return {
    setContent,
    content,
  };
});
