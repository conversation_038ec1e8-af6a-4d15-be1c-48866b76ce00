// @ts-nocheck
import { AlipayAuthorizationRelatedService, WeChatAuthorizationRelatedService } from '@/service';
import { getSilentCode } from '@/utils/auth';
import { getAlipayPhoneNumber, getWechatPhoneNumber } from '@/utils/jsapi';
import { CHANNEL_CODE } from '@/utils/platform';

// const platformSilentAuthApi = {
//   [EPlatform['mp-alipay']]: AlipayAuthorizationRelatedService.postAlipayAuthSilent,
//   [EPlatform['mp-weixin']]: WeChatAuthorizationRelatedService.postWechatAuthSilent,
// };

/**
 * @description: 静默授权
 * @return {*}
 */
export async function silentAuth() {
  const silentCode = await getSilentCode();
  if (!silentCode) return false;
  const params = {
    code: silentCode,
    channelCode: CHANNEL_CODE,
  };
  // #ifdef MP-WEIXIN
  const [err, data] = await WeChatAuthorizationRelatedService.postWechatAuthSilent(params);
  // #endif
  // #ifdef MP-ALIPAY
  const [err, data] = await AlipayAuthorizationRelatedService.postAlipayAuthSilent(params);
  // #endif
  if (!err && data && data?.data) {
    return data.data || false;
  }
  return false;
}

/**
 * @description: 手机号授权
 * @param {any} e
 * @return {*}
 */
export async function mobileAuth(e: any) {
  let params;
  // #ifdef MP-WEIXIN
  params = await getWechatPhoneNumber(e);
  // #endif
  // #ifdef MP-ALIPAY
  params = await getAlipayPhoneNumber(e);
  // #endif
  console.error('params', params);
  // #ifdef MP-WEIXIN
  const [err, data] = await WeChatAuthorizationRelatedService.postWechatAuthAdvanced({
    ...params,
    channelCode: CHANNEL_CODE,
  });
  // #endif
  // #ifdef MP-ALIPAY
  const [err, data] = await AlipayAuthorizationRelatedService.postAlipayAuthAdvanced({
    ...params,
    channelCode: CHANNEL_CODE,
  });
  // #endif
  if (data && data?.data) {
    return data.data || false;
  }
  return false;
}
