import { defineStore } from 'pinia';
import { ref } from 'vue';
import { AccountManagementService, UserStatusCheckCauseRes } from '@/service';
import { showSingleToast } from '@/utils/jsapi';
import { mobileAuth, silentAuth } from './service';

export enum EUserStatus {
  Cancel = 'CANCEL', // 已注销
  CancelApplyApprove = 'CANCEL_APPLY_APPROVE', // 注销申请通过
  CancelApplyRefuse = 'CANCEL_APPLY_REFUSE', // 注销申请拒绝
  CancelApplying = 'CANCEL_APPLYING', // 注销申请中
  Disable = 'DISABLE', // 禁用
  Enable = 'ENABLE', // 正常
  None = 'NONE', // 未注册
}
export type TUserStatus = `${EUserStatus}`;

interface User {
  nickname: string;
  avatar: string;
  accessToken?: string;
  refreshToken?: string;
  alipayUid?: string;
  mobile?: string;
  fullMobile?: string;
  nickName?: string;
  openId?: string;
  osType?: string;
  realName?: string;
  status?: TUserStatus;
  userId?: string;
  residueRevokeCancelTime?: string;
}

const initState = {
  nickname: '',
  avatar: '',
};

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<User>({ ...initState });
    const userStatusInfo = ref<UserStatusCheckCauseRes>({});
    const isLogin = computed(() => !!userInfo.value.mobile);
    const token = computed(() => userInfo.value.accessToken);
    const setUserInfo = (val: User) => {
      userInfo.value = val;
    };
    const clearUserInfo = () => {
      userInfo.value = { ...initState };
    };
    const reset = () => {
      userInfo.value = { ...initState };
    };

    const setToken = (val: string) => {
      userInfo.value.accessToken = val;
    };

    const setRefreshToken = (val: string) => {
      userInfo.value.refreshToken = val;
    };

    /**
     * @description: 静默授权
     * @return {*}
     */
    const silentLogin = async () => {
      const data = await silentAuth();
      if (data) {
        setUserInfo(data);
      }
      return !!data;
    };
    /**
     * @description: 手机号授权
     * @param {any} e
     * @return {*}
     */
    const mobileLogin = async (e: any) => {
      const data = await mobileAuth(e);
      if (data) {
        const { accessToken = '', refreshToken = '', mobile, userId } = data;
        if (accessToken) {
          userInfo.value.accessToken = accessToken;
        }
        if (refreshToken) {
          userInfo.value.refreshToken = refreshToken;
        }
        if (mobile) {
          userInfo.value.mobile = mobile;
        }
        if (userId) {
          userInfo.value.userId = userId;
        }
      }
      return !!data;
    };

    /**
     * @description: 获取用户信息
     * @return {*}
     */
    const getUserInfo = async () => {
      const [, res] = await AccountManagementService.postUserGetUserInfo();
      if (res?.data) {
        const { avatar, nickName, status, mobile, residueRevokeCancelTime, userId, fullMobile } =
          res.data;
        setUserInfo({
          ...userInfo.value,
          avatar: avatar || '',
          nickname: nickName || '',
          mobile,
          status,
          residueRevokeCancelTime,
          userId,
          fullMobile,
        });
      }
    };

    /**
     * @description: 退出登录
     * @return {*}
     */
    const logout = async () => {
      const [err, res] = await AccountManagementService.postUserLogout();
      if (err) {
        showSingleToast(err?.subMsg || '退出登录失败');
        return;
      }
      reset();
      // 跳转首页
      uni.reLaunch({
        url: '/pages/home/<USER>',
      });
    };

    /**
     * @description: 申请注销
     * @return {*}
     */
    const applyCancelUser = async (cancelReason: string) => {
      const [err, res] = await AccountManagementService.postUserApplyCancel(
        cancelReason,
        undefined,
        {
          HEADERS: { 'access-key': 'abcde' },
        },
      );
      if (err) {
        showSingleToast(err?.subMsg || '申请注销失败');
        return;
      }
      // reset();
      uni.showToast({
        title: '注销成功',
        icon: 'success',
      });
      // 刷新注销页
      uni.reLaunch({
        url: '/pages/mine-sub/setting/logOut/index',
      });
    };

    /**
     * @description: 刷新token
     * @return {*}
     */

    const refreshToken = async () => {
      if (!userInfo.value.refreshToken) {
        reset();
        return;
      }
      const [err, res] = await AccountManagementService.postUserRefreshToken({
        refreshToken: userInfo.value.refreshToken,
      });
      if (err) {
        reset();
        return;
      }
      if (res?.data) {
        const { accessToken, refreshToken } = res.data;
        if (accessToken && refreshToken) {
          userInfo.value.accessToken = accessToken;
          userInfo.value.refreshToken = refreshToken;
          getUserInfo();
        }
      }
    };

    const getUserStatus = async (refresh = false, abnormalGo = false) => {
      if (Object.keys(userStatusInfo).length && !refresh) {
        return;
      }
      const [err, res] = await AccountManagementService.getUserGetUserStatus();
      if (res?.data) {
        userStatusInfo.value = res.data;
        if (
          abnormalGo &&
          [EUserStatus.CancelApplying, EUserStatus.CancelApplyRefuse, EUserStatus.Disable].includes(
            res.data.status as EUserStatus,
          )
        ) {
          // 账户申请中 、账户注销被驳回、冻结
          uni.reLaunch({
            url: `/pages/mine-sub/setting/logOut/index`,
          });
        }
      }
    };
    return {
      userInfo,
      userStatusInfo,
      setUserInfo,
      clearUserInfo,
      getUserInfo,
      applyCancelUser,
      reset,
      token,
      isLogin,
      silentLogin,
      mobileLogin,
      setToken,
      setRefreshToken,
      logout,
      refreshToken,
      getUserStatus,
    };
  },
  {
    persist: true,
  },
);
