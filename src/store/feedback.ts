import { ref, computed } from 'vue';
import { defineStore } from 'pinia';

export interface IOption {
  id: number;
  problemClassName: string;
  problemClass: string;
}

export const useFeedbackStore = defineStore('feedback', () => {
  const currentOption = ref<IOption>({ id: 0, problemClassName: '', problemClass: '' });
  const setCurrentOption = (
    option: IOption = { id: 0, problemClassName: '', problemClass: '' },
  ) => {
    currentOption.value = option;
  };

  return {
    currentOption,
    setCurrentOption,
  };
});
