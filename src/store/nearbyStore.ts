import { StationListQueryReqVO } from '@/service';
import { getLocation } from '@/utils/jsapi';
import { defineStore } from 'pinia';

export const useNearbyStore = defineStore('nearBy', () => {
  const scale = ref(13);
  // 用户位置
  const userLocation = ref<{ latitude: number; longitude: number }>({
    latitude: 0,
    longitude: 0,
  });
  // 地图中心点
  const mapCenterLoc = ref({
    latitude: 0,
    longitude: 0,
  });
  // 搜索关键词
  const searchKey = ref('');
  // 筛选栏
  const filterData = ref<{
    baseParkType: number | null;
    sortType: StationListQueryReqVO.sortType | null;
  }>({
    baseParkType: null,
    sortType: StationListQueryReqVO.sortType.DISTANCE,
  });
  // 距离
  const distance = ref(5);
  // 弹窗筛选数据-停车
  const parkModalFilterData = ref<{ parkType: string | null; supportCharge: number | null }>({
    parkType: null,
    supportCharge: null,
  });
  // 弹窗筛选数据-充电
  const chargeModalFilterData = ref<{
    chargeType: StationListQueryReqVO.chargeType | null;
    parkingChargeType: StationListQueryReqVO.parkingChargeType | null;
    carType: StationListQueryReqVO.carType | null;
  }>({
    chargeType: null,
    parkingChargeType: null,
    carType: null,
  });
  const resetLoc = async (forceUpdate = false) => {
    const res = await getLocation();
    // if (res.isDefault && !forceUpdate) {
    //   return;
    // }
    if (forceUpdate) {
      scale.value = scale.value === 13 ? 13.000001 : 13;
    }
    userLocation.value = {
      latitude: res.latitude,
      longitude: res.longitude,
    };
    updateMapCenterLoc({
      latitude: res.latitude,
      longitude: res.longitude,
    });
  };
  const updateScale = (value: number) => {
    scale.value = value;
  };
  const updateMapCenterLoc = (loc: { latitude: number; longitude: number }) => {
    mapCenterLoc.value = loc;
  };
  const updateSearchKey = (key: string) => {
    searchKey.value = key;
  };
  const updateFilterData = (data: {
    baseParkType: number | null;
    sortType: StationListQueryReqVO.sortType | null;
  }) => {
    filterData.value = data;
  };
  const updateParkModalFilterData = (data: {
    parkType: string | null;
    supportCharge: number | null;
  }) => {
    parkModalFilterData.value = data;
  };
  const updateChargeModalFilterData = (data: {
    chargeType: StationListQueryReqVO.chargeType | null;
    parkingChargeType: StationListQueryReqVO.parkingChargeType | null;
    carType: StationListQueryReqVO.carType | null;
  }) => {
    chargeModalFilterData.value = data;
    console.log('chargeModalFilterData', chargeModalFilterData.value);
  };
  const updateDistance = (value: number) => {
    distance.value = value;
  };
  const resetFilter = () => {
    filterData.value = {
      baseParkType: null,
      sortType: StationListQueryReqVO.sortType.DISTANCE,
    };
    distance.value = 5;
    parkModalFilterData.value = {
      parkType: null,
      supportCharge: null,
    };
    chargeModalFilterData.value = {
      chargeType: null,
      parkingChargeType: null,
      carType: null,
    };
  };
  return {
    scale,
    userLocation,
    mapCenterLoc,
    searchKey,
    filterData,
    distance,
    parkModalFilterData,
    chargeModalFilterData,
    updateScale,
    resetLoc,
    updateMapCenterLoc,
    updateSearchKey,
    updateFilterData,
    updateParkModalFilterData,
    updateChargeModalFilterData,
    updateDistance,
    resetFilter,
  };
});
