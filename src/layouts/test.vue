<template>
  <TransparentTitle :title="getTitile()" />
  <view class="transparent-layout">
    1111
    <slot ref="pageRef" />
  </view>
  <!-- 网络错误 -->
  <view v-if="appStore.netError" class="h-full w-full fixed top-0 left-0 bg-page-background z-2000">
    <AEmpty type="net" button buttonText="刷新" @button-click="handleRefresh" />
  </view>
</template>

<script setup lang="ts">
import TransparentTitle from '@/components/TransparentTitle/index.vue';
import { useAppStore } from '@/store';
// @ts-ignore
// eslint-disable-next-line
import pageJson from '@/pages.json'

const pageRef = ref();

const appStore = useAppStore();
appStore.netError = false;
const handleRefresh = () => {
  const pages = getCurrentPages();
  const page: Page.PageInstance & {
    options?: any;
    route?: string;
  } = pages[pages.length - 1];
  // 获取参数
  const params: {
    [key: string]: any;
  } = page?.options;
  const route = page?.route;
  const query = Object.keys(params)
    .map((key) => `${key}=${params[key]}`)
    .join('&');
  // 重新加载页面
  uni.redirectTo({
    url: `/${route}?${query}`,
  });
};
// 获取当前页的标题
const getTitile = () => {
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  const route = page?.route;
  return (
    pageJson.pages.find((item: any) => item.path === route)?.style?.navigationBarTitleText || ''
  );
};

onReady(() => {
  console.log('default layout mounted', pageRef.value);
});
</script>

<style lang="scss"></style>
