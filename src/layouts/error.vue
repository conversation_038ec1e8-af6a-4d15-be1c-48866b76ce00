<template>
  <view>
    <!-- 网络错误 -->
    <view class="h-full w-full fixed top-0 left-0 bg-page-background z-2000">
      <AEmpty
        :image="config.image"
        :title="config.title"
        :desc="config.desc"
        :buttonText="config.buttonText"
        :imageHeight="config.imageHeight"
        :imageWidth="config.imageWidth"
        :loading="loading"
        button
        @button-click="handleRefresh"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
const loading = ref(false);
const config = ref<ErrorMask>({
  image: '/images/common/empty/net.png',
  title: '网络错误',
  desc: '请返回重试',
  buttonText: '刷新',
});
onLoad(async () => {
  const error = uni.getStorageSync('ErrorMaskConfig');
  if (error) {
    config.value = JSON.parse(error);
  }
});

const handleRefresh = () => {
  loading.value = true;
  uni.$emit('refreshPage', loading);
};
</script>
