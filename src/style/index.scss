.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

// 全局字体引用 DEMO
// @font-face {
//   font-family: TiTi;
//   src: url('./fonts/PangMenZhengDaoBiaoTiTi-1.ttf');
// }

// .titi {
//   font-family: TiTi, Arial, sans-serif;
// }

.uni-modal {
  padding: 40rpx 0rpx 0rpx;
  border-radius: 16rpx;

  .uni-modal__hd {
    padding: 0;
    font-size: 36rpx;
    font-weight: 500;
    line-height: 42rpx;
    color: #333;
    text-align: center;
    letter-spacing: 0.58rpx;
  }

  .uni-modal__bd {
    font-size: 28rpx;
    font-weight: normal;
    line-height: 42rpx;
    color: #999;
    text-align: center;
    letter-spacing: 0;
  }

  .uni-modal__btn {
    padding: 24rpx 0;
    font-size: 36rpx;
    font-weight: 500;
    line-height: 43rpx;
    color: #f33813 !important;
    text-align: center;
    letter-spacing: 0.58rpx;
  }
}

.uv-modal {
  border-radius: 16rpx !important;

  .uv-modal__title {
    padding: 40rpx 0rpx 0rpx;
    font-size: 36rpx !important;
    font-weight: 500 !important;
    line-height: 42rpx !important;
    color: #333 !important;
    text-align: center;
    letter-spacing: 0.58rpx !important;
  }

  .uv-modal__content {
    font-size: 28rpx !important;
    font-weight: normal;
    line-height: 42rpx !important;
    color: #999 !important;
    text-align: center;
    letter-spacing: 0 !important;
  }

  .uv-modal__button-group__wrapper__text {
    padding: 24rpx 0;
    font-size: 36rpx !important;
    font-weight: 500;
    line-height: 43rpx;
    color: #f33813 !important;
    text-align: center;
    letter-spacing: 0.58rpx;
  }
}
