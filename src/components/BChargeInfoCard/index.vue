<template>
  <BBusinessCard
    :name="props.chargeInfo.stationName || ''"
    :address="props.chargeInfo.stationAddress || ''"
    :distance="props.chargeInfo.distance ?? undefined"
    :tipInfo="props.chargeInfo.parkChargeTypeDesc || ''"
    :latitude="props.chargeInfo.lat ?? undefined"
    :longitude="props.chargeInfo.lon ?? undefined"
    :remark="(showCollectRemark && props.chargeInfo.memo) || ''"
    @click="goChargeDetail"
  >
    <template #tip-slot>
      <text class="text-text-primary font-500">{{ props.chargeInfo.parkChargeTypeDesc }}</text>
    </template>
    <template v-for="item in infoList" :key="item.name">
      <view class="flex-between items-center w-full mt-6" v-if="chargeInfo[item.totalNumKey]">
        <view class="text-secondary-24 shrink-0">
          <text class="text-text-primary">¥</text>
          <text class="text-54rpx font-DIN font-bold text-text-primary">
            {{ chargeInfo[item.priceKey] }}
          </text>
          <text class="text-secondary-24 text-text-secondary">/度</text>
        </view>
        <view>
          <BProgressInfo
            :progressName="item.name"
            progressWidth="120rpx"
            :progressStatus="getProgressStatus(chargeInfo[item.statusKey] || '')"
            :usedNum="
              getUsedNum(chargeInfo[item.totalNumKey] || 0, chargeInfo[item.leftNumKey] || 0)
            "
            :leftNum="chargeInfo[item.leftNumKey] || 0"
            :totalNum="chargeInfo[item.totalNumKey] || 0"
          />
        </view>
      </view>
    </template>
  </BBusinessCard>
</template>
<script setup lang="ts">
import BBusinessCard from '@/components/BBusinessCard/index.vue';
import BProgressInfo from '@/components/BProgressInfo/index.vue';
import { StationCollectVO, StationInfoVO } from '@/service';
import { getProgressStatus } from '@/utils/stationStatus';

const props = withDefaults(
  defineProps<{
    chargeInfo: StationInfoVO & StationCollectVO;
    showCollectRemark: boolean;
  }>(),
  {
    chargeInfo: () => ({}),
    showCollectRemark: false,
  },
);
const infoList = ref([
  {
    name: '快充泊位',
    leftNumKey: 'dcIdle',
    totalNumKey: 'dcTotal',
    priceKey: 'dcPrice',
    statusKey: 'dcGunUsedStatus',
  },
  {
    name: '慢充泊位',
    leftNumKey: 'acIdle',
    totalNumKey: 'acTotal',
    priceKey: 'acPrice',
    statusKey: 'acGunUsedStatus',
  },
]);
const goChargeDetail = () => {
  uni.navigateTo({
    url: `/pages/noticeBoard/stationDetail/chargeDetail/index?stationId=${props.chargeInfo.stationId}`,
  });
};
const getUsedNum = (totalNum: number, num: number) => {
  return totalNum - num;
};
// const getProgressStatus = (leftNum: number, totalNum: number) => {
//   if (!totalNum) {
//     return 'DANGER';
//   } else if ((totalNum - leftNum) / totalNum > 0.8) {
//     return 'DANGER';
//   } else if ((totalNum - leftNum) / totalNum > 0.6) {
//     return 'NORMAL';
//   }
//   return 'FREE';
// };
const parkingChargeTypeMap = reactive({
  FREE: '免费停车',
  FREE_LIMIT: '限免停车',
  PAID: '收费停车',
});
const parkingChargeType = computed(() => {
  return (
    parkingChargeTypeMap[props.chargeInfo.parkChargeType as keyof typeof parkingChargeTypeMap] || ''
  );
});
</script>
<style scoped lang="scss"></style>
