<template>
  <view class="flex" @click="jumpToDetail">
    <view class="flex-1 flex-between flex-col">
      <view class="text-text-primary line-clamp-2 text-primary-28">{{ props.item.title }}</view>
      <view class="text-text-sub text-secondary-24">
        <text v-if="props.item.pushTime">{{ pushTimeFormat }}</text>
        <text class="ml-2 truncate">{{ props.item.author }}</text>
      </view>
    </view>
    <image
      class="w-50 h-33 rounded-4 of-hidden shrink-0 ml-10"
      mode="aspectFill"
      :src="props.item.pic"
    />
  </view>
</template>
<script setup lang="ts">
import { useGetSystemInfo } from '@/hooks/useGetSystemInfo';
import { InformationContentRespVO } from '@/service';
import dayjs from 'dayjs';

const { systemInfo } = useGetSystemInfo();

const props = withDefaults(
  defineProps<{
    item: InformationContentRespVO;
  }>(),
  { item: () => ({}) },
);
const pushTimeFormat = computed(() => {
  if (!props.item.pushTime) return '';
  if (dayjs(props.item.pushTime).isSame(dayjs(), 'year')) {
    return dayjs(props.item.pushTime).format('MM-DD');
  } else {
    return dayjs(props.item.pushTime).format('YYYY-MM-DD');
  }
});
// 跳转资讯详情
const jumpToDetail = () => {
  if (
    systemInfo.value.uniPlatform === 'mp-weixin' &&
    props?.item?.contentUrl?.includes('mp.weixin.qq.com')
  ) {
    // @ts-ignore
    wx.openOfficialAccountArticle({
      url: props.item.contentUrl,
    });
  } else {
    uni.navigateTo({
      url: `/pages/hotNews/detail?id=${props.item.id}`,
    });
  }
};
</script>
<style lang="scss" scoped></style>
