<template>
  <button
    :class="[
      {
        'app-btn--plain': plain,
        'app-btn--disabled': disabled,
      },
      'app-reset-button',
      'app-btn',
      `app-btn--${type}`,
      `app-btn--${shape}`,
      `app-btn--${size}`,
      className,
    ]"
    type="primary"
    :size="size"
    hover-class="app-btn--hover"
    :style="{ ...baseColor, ...customStyle }"
    :open-type="openType"
    :scope="scope"
    @click.stop="handleClick"
    @getphonenumber="handleGetPhoneNumber"
    @getAuthorize="handleGetAuthorize"
    @error="handleError"
  >
    <slot></slot>
  </button>
</template>

<script lang="ts" setup>
interface ButtonProps {
  type: 'primary' | 'charging' | 'parking' | 'brand' | 'vip' | 'brand-plain';
  size: 'default' | 'mini';
  shape: 'square' | 'circle';
  plain: boolean;
  disabled: boolean;
  color: string;
  customStyle: { [key: string]: any };
  openType: string;
  scope: string;
  className: string;
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  size: 'default',
  shape: 'square',
  plain: false,
  disabled: false,
  color: '',
  // @ts-ignore
  customStyle: () => ({}),
  openType: '',
  scope: '',
  className: '',
});

const emit = defineEmits(['click', 'getPhoneNumber', 'getAuthorize', 'getAuthorize', 'error']);

const handleClick = () => {
  if (!props.disabled) {
    emit('click');
  }
};

const baseColor = computed(() => {
  const style: any = {};
  if (props.color) {
    // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色
    style.color = props.plain ? props.color : 'white';
    if (!props.plain) {
      // 非镂空，背景色使用自定义的颜色
      style.backgroundColor = props.color;
    }
    if (props.color.indexOf('gradient') !== -1) {
      // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色
      style.borderWidth = 0;
      if (!props.plain) {
        style.backgroundImage = props.color;
      }
    } else {
      // 非渐变色，则设置边框相关的属性
      style.borderColor = props.color;
      style.borderWidth = '1px';
      style.borderStyle = 'solid';
    }
  }
  return style;
});

const handleGetPhoneNumber = (res: any) => {
  emit('getPhoneNumber', res);
};

const handleGetAuthorize = () => {
  emit('getAuthorize');
};

const handleError = (res: any) => {
  emit('error', res);
};
</script>

<style lang="scss" scoped>
.app-reset-button {
  padding: 0;
  background-color: transparent;
}

.app-reset-button::after {
  border: none;
}

.app-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  font-size: 32rpx;
  line-height: 38rpx;
  text-align: center;

  &--brand {
    color: #fff;

    @apply border-brand-primary bg-brand-primary;

    &.app-btn--plain {
      @apply text-brand-primary border-brand-primary;
    }
  }

  &--brand-plain {
    @apply border-brand-primary bg-white text-brand-primary;

    &.app-btn--plain {
      @apply border-brand-primary bg-white text-brand-primary;
    }
  }

  &--primary {
    @apply border-brand-primary bg-brand-primary text-white;

    &.app-btn--plain {
      @apply text-brand-primary bg-brand-primary;
    }
  }

  &--parking {
    color: #fff;
    background-color: #0398fd;
    border-color: #0398fd;

    &.app-btn--plain {
      color: #0398fd;
      border-color: #0398fd;
    }
  }

  &--charging {
    color: #fff;
    background-color: #2ccb8e;
    border-color: #2ccb8e;

    &.app-btn--plain {
      color: #2ccb8e;
      border-color: #2ccb8e;
    }
  }

  &--hover {
    opacity: 0.7;
  }

  &--disabled {
    opacity: 0.4;

    // border-color: rgba(0, 0, 0, 0.2) !important;
    // color: rgba(0, 0, 0, 0.2) !important;
  }

  &--plain {
    background-color: #fff;
    border-style: solid;
    border-width: 2rpx;
  }

  &--square {
    border-radius: 16rpx;
  }

  &--circle {
    border-radius: 50vw;
  }

  &--mini {
    padding: 15rpx 44rpx;
    font-size: 26rpx;
    /* #ifdef MP-ALIPAY */
    line-height: 20rpx;
    /* #endif */
    &.app-btn--square {
      border-radius: 8rpx;
    }
  }

  &--vip {
    color: #e7cab2;
    background-color: #323230;
    border-color: #323230;

    &.app-btn--plain {
      color: #323230;
      border-color: #323230;
    }
  }
}
</style>
