<template>
  <view class="common-card mt-4">
    <view v-for="(item, index) in newsList" :key="item.id">
      <BNewsItem :item="item" />
      <view v-if="index !== newsList.length - 1" class="slider-1px my-6" />
    </view>
  </view>
</template>
<script setup lang="ts">
import BNewsItem from '@/components/BNewsItem/index.vue';
import { InformationContentRespVO } from '@/service';

const props = withDefaults(
  defineProps<{
    newsList: InformationContentRespVO[];
  }>(),
  {
    newsList: () => [],
  },
);
</script>
<style lang="scss" scoped></style>
