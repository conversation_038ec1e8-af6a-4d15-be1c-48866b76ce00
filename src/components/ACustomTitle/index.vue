<template>
  <view
    v-if="keepSpace"
    :style="{
      height: statusBarHeight + titleBarHeight + 'px',
    }"
  ></view>
  <view
    class="fixed left-0 top-0 w-screen flex items-center z-998"
    :class="customClass"
    :style="{
      paddingTop: `${statusBarHeight}px`,
      height: `${titleBarHeight}px`,
      paddingLeft: `${titlePaddingLeft}px`,
      background: `rgba(255, 255, 255, ${transparency})`,
    }"
  >
    <slot name="icon" />
    <view
      class="text-primary-36 font-700"
      :style="{ color: transparency > 0.6 ? `rgba(0, 0, 0, ${transparency})` : 'white' }"
      :class="titleClass"
    >
      {{ title }}
    </view>
    <slot />
  </view>
</template>
<script lang="ts" setup>
import useNavbar from '@/hooks/useNavbar';

const props = withDefaults(
  defineProps<{
    title?: string;
    customClass?: string;
    titleClass?: string;
    keepSpace?: boolean;
  }>(),
  {
    title: import.meta.env.VITE_APP_TITLE,
    customClass: '',
    titleClass: '',
    keepSpace: true,
  },
);
const { statusBarHeight, titleBarHeight, radio, transparency } = useNavbar();
const titlePaddingLeft = ref(24 * radio);
const titleColorType = ref<'white' | 'black'>();
onMounted(() => {
  // #ifdef MP-ALIPAY
  getPaddingLeft();
  // #endif
});
const getPaddingLeft = () => {
  if (my.canIUse('getLeftButtonsBoundingClientRect')) {
    const { backButtonIcon, homeButtonIcon } = my.getLeftButtonsBoundingClientRect();
    if (backButtonIcon && Object.keys(backButtonIcon).length > 0) {
      // 返回图标
      titlePaddingLeft.value = backButtonIcon.right + 6;
    }
    if (homeButtonIcon && Object.keys(homeButtonIcon).length > 0) {
      // home图标
      titlePaddingLeft.value = homeButtonIcon.right + 6;
    }
  }
};
// #ifdef MP-ALIPAY

watch(
  () => transparency.value,
  (val) => {
    if (val > 0.6) {
      if (titleColorType.value !== 'black') {
        titleColorType.value = 'black';
        my.setNavigationBar({
          backgroundColor: '#fff',
          frontColor: '#000',
          title: '',
        });
      }
    } else if (titleColorType.value !== 'white') {
      titleColorType.value = 'white';
      my.setNavigationBar({
        backgroundColor: '#000',
        frontColor: '#fff',
        title: '',
      });
    }
  },
  {
    immediate: true,
  },
);
// #endif
</script>
