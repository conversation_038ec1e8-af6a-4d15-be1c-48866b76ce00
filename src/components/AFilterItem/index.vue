<template>
  <view
    class="w-162rpx py-1 rounded-100 bg-white mr-5 flex-center relative text-text-primary"
    :class="[customClass, filterValue !== defaultFilterValue ? 'filtered-bg text-white' : '']"
    @click.stop="() => emit('itemClick')"
  >
    <view class="text-secondary-26 leading-normal mr-2">{{ showLabel }}</view>
    <OSSImg
      :width="18"
      :height="18"
      :src="
        filterValue !== defaultFilterValue
          ? '/images/nearby/filter-arrow-down-white.png'
          : '/images/nearby/filter-arrow-down-black.png'
      "
    />
    <AFilterPop
      v-if="showFilterPop"
      :list="filterList"
      :customPopArrowClass="customPopArrowClass"
      @filterClick="(value) => emit('filterClick', value)"
      :customStyle="customPopStyle"
    />
  </view>
</template>
<script setup lang="ts">
import AFilterPop from '@/components/AFilterPop/index.vue';
import OSSImg from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    showFilterPop: boolean;
    filterValue: any;
    defaultLabel: string;
    filterList: any[];
    defaultFilterValue?: any;
    customPopStyle?: object;
    customClass?: string;
    customPopArrowClass?: string;
  }>(),
  {
    showFilterPop: false,
    filterValue: '',
    defaultLabel: '',
    filterList: () => [],
    defaultFilterValue: '',
    customPopStyle: () => ({}),
    customClass: '',
    customPopArrowClass: '',
  },
);
const emit = defineEmits(['itemClick', 'filterClick']);
const showLabel = computed(() => {
  if (props.filterValue === props.defaultFilterValue) {
    return props.defaultLabel;
  }
  return (
    props.filterList.find((item) => item.value === props.filterValue)?.label || props.defaultLabel
  );
});
</script>
<style lang="scss" scoped>
.filtered-bg {
  background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);
}
</style>
