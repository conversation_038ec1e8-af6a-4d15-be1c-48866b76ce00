<template>
  <BBusinessCard
    :name="props.parkInfo.parkName || ''"
    :address="props.parkInfo.address || props.parkInfo.location || ''"
    :distance="props.parkInfo.distance ?? undefined"
    :tipInfo="props.parkInfo.parkingFeeDescription || ''"
    :latitude="props.parkInfo.latitude ?? undefined"
    :longitude="props.parkInfo.longitude ?? undefined"
    :remark="(showCollectRemark && props.parkInfo.collectionRemark) || ''"
    @click="goParkDetail"
  >
    <view class="flex-between items-center w-full mt-6">
      <BProgressInfo
        class="w-full"
        :className="'w-full'"
        progressName="空余泊位"
        :progressStatus="getProgressStatus(props.parkInfo.spaceStatus || '')"
        :usedNum="getUsedNum(props.parkInfo.plateNum || 0, props.parkInfo.remainNum || 0)"
        :leftNum="props.parkInfo.remainNum || 0"
        :totalNum="props.parkInfo.plateNum || 0"
      />
    </view>
  </BBusinessCard>
</template>
<script setup lang="ts">
import BBusinessCard from '@/components/BBusinessCard/index.vue';
import BProgressInfo from '@/components/BProgressInfo/index.vue';
import { BaseParkVO, SearchParkingResponse } from '@/parkService';
import { getProgressStatus } from '@/utils/stationStatus';

const props = withDefaults(
  defineProps<{
    parkInfo: BaseParkVO & SearchParkingResponse;
    showCollectRemark: boolean;
  }>(),
  {
    parkInfo: () => ({}),
    showCollectRemark: false,
  },
);
const getUsedNum = (totalNum: number, leftNum: number) => {
  return totalNum - leftNum;
};
const goParkDetail = () => {
  uni.navigateTo({
    url: `/pages/noticeBoard/stationDetail/parkDetail/index?parkId=${props.parkInfo.parkId}`,
  });
};
</script>
<style scoped lang="scss"></style>
