<template>
  <wd-popup v-model="show" position="right" custom-style="width: 600rpx; z-index: 9999">
    <view class="h-full flex flex-col">
      <TransparentTitle v-if="transparentTitle" :keep-space="true" :only-keep-space="true" />
      <view class="flex-1">
        <slot />
      </view>
      <view class="flex-between p-10 pb-safe-24rpx shrink-0 relative">
        <view class="slider-1px absolute left-0 top-0 w-full"></view>
        <AppButton type="brand" plain @click="handleReset" className="w-60 h-20">重置</AppButton>
        <AppButton type="brand" @click="handleConfirm" className="w-60 h-20">确定</AppButton>
      </view>
    </view>
  </wd-popup>
</template>
<script setup lang="ts">
import AppButton from '@/components/AppButton/index.vue';
import TransparentTitle from '@/components/TransparentTitle/index.vue';

const props = withDefaults(
  defineProps<{
    transparentTitle?: boolean;
  }>(),
  {
    transparentTitle: true,
  },
);
const emit = defineEmits(['reset', 'confirm']);
const show = ref(false);
const handleReset = () => {
  emit('reset');
};
const handleConfirm = () => {
  emit('confirm');
};
const changeVisible = (val: boolean) => {
  show.value = val;
};
defineExpose({
  changeVisible,
});
</script>
<style lang="scss" scoped></style>
