<template>
  <view class="flex" :class="className">
    <AFilterItem
      v-for="(item, index) in filterList"
      :key="index"
      :filter-list="item.children"
      :show-filter-pop="currentFilterPop === index"
      :filter-value="filterData[item.filterKey]"
      :default-label="item.defaultFilterLabel || item.label"
      :default-filter-value="item.defaultFilterValue"
      :custom-pop-style="item.customPopStyle"
      :custom-pop-arrow-class="item.customPopArrowClass"
      :custom-class="item.customClass"
      @itemClick="() => handleClick(index)"
      @filterClick="(value: any) => handleChangeFilter(item.filterKey, value)"
    />
  </view>
</template>
<script setup lang="ts">
import AFilterItem from '@/components/AFilterItem/index.vue';

const props = withDefaults(
  defineProps<{
    filterList: any[];
    filterData: any;
    className: string;
  }>(),
  {
    filterList: () => [],
    filterData: () => ({}),
    className: '',
  },
);

const emit = defineEmits(['changeFilter']);
const currentFilterPop = ref(-1);
const handleClick = (index: number) => {
  if (index === currentFilterPop.value) {
    currentFilterPop.value = -1;
  } else {
    currentFilterPop.value = index;
  }
};
const handleChangeFilter = (filterKey: string, value: any) => {
  emit('changeFilter', filterKey, value);
};
const clearPop = () => {
  currentFilterPop.value = -1;
};
defineExpose({
  clearPop,
});
</script>
<style lang="scss" scoped>
.filtered-bg {
  background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);
}
</style>
