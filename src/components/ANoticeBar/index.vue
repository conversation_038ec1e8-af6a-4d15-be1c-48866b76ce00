<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    text: string;
    prefix?: string;
    customStyle?: string;
    className?: string;
  }>(),
  {
    text: '',
    prefix: '',
  },
);
</script>
<script lang="ts">
export default {
  name: 'ANoticeBar',
};
</script>
<template>
  <view>
    <wd-notice-bar
      :custom-class="props.className"
      :style="props.customStyle"
      :text="props.text"
      :prefix="props.prefix"
    >
      <template #prefix><slot name="prefix" /></template>
    </wd-notice-bar>
  </view>
</template>

<style scoped lang="scss">
:deep(.wd-notice-bar.is-warning) {
  @apply text-secondary-26 bg-brand-primary bg-op-10 text-brand-primary px-6 py-5 rd-0;
}
</style>
