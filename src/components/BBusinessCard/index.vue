<template>
  <view class="common-card mt-6" @click="() => emits('click')">
    <view class="flex-between items-center">
      <view class="text-primary-32 text-text-primary truncate">{{ name }}</view>
      <view v-if="distance >= 0" class="flex items-center" @click.stop="goNav">
        <OSSImage :width="36" :height="36" src="/images/common/businessCard/card-distance.png" />
        <view class="text-secondary-24 text-text-secondary ml-2">{{ distance }}km</view>
      </view>
    </view>
    <view class="address flex w-full mt-3 items-center">
      <OSSImage
        class-name="shrink-0"
        :width="24"
        :height="24"
        src="/images/common/businessCard/card-loc.png"
      />
      <view class="text-secondary-24 flex-1 text-text-sub truncate ml-1" v-if="address">
        {{ address }}
      </view>
    </view>
    <slot></slot>
    <view class="tips rounded-2 flex items-center bg-page-background mt-6" v-if="tipInfo">
      <OSSImage
        class-name="shrink-0"
        :width="40"
        :height="40"
        src="/images/common/businessCard/park-icon.png"
      />
      <view class="text-auxiliary ml-2 truncate flex">
        <slot name="tip-slot">
          <view class="text-text-primary font-500 shrink-0">收费停车：</view>
          <view class="text-text-primary-light truncate">{{ tipInfo }}</view>
        </slot>
      </view>
    </view>
    <view v-if="remark" class="remark text-secondary-24 text-text-sub truncate mt-6">
      {{ remark }}
    </view>
  </view>
</template>
<script setup lang="ts">
import OSSImage from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    name: string;
    address: string;
    distance: number;
    tipInfo: string;
    remark: string;
    latitude: number | string;
    longitude: number | string;
  }>(),
  {
    name: '',
    address: '',
    distance: 0,
    tipInfo: '',
    remark: '',
    latitude: 0,
    longitude: 0,
  },
);
const emits = defineEmits(['click']);
const getDistanceText = (distance: number) => {
  if (distance < 1000) {
    return `${distance}m`;
  }
  return `${(distance / 1000).toFixed(2)}km内`;
};
const goNav = () => {
  if (props.latitude && props.longitude) {
    uni.openLocation({
      latitude: Number(props.latitude),
      longitude: Number(props.longitude),
      name: props.name,
      address: props.address,
    });
  }
};
</script>
<style lang="scss" scoped></style>
