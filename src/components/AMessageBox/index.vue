<template>
  <wd-popup v-model="show" @close="close" custom-class="!bg-transparent" transition="zoom-in">
    <view class="px-8 py-10 rounded-8 of-hidden w-150 box-border bg-white">
      <view class="text-primary-36 text-text-primary w-full text-center">
        {{ title }}
      </view>
      <slot>
        <view
          v-if="type !== 'prompt' && msg"
          class="text-secondary-26 text-text-secondary mt-4 text-center whitespace-pre-line"
        >
          <text>{{ msg }}</text>
        </view>
        <view
          v-else-if="type === 'prompt'"
          class="rounded-2 bordered-2rpx-solid-divider-color mt-4 w-full h-19 px-4 box-border flex items-center"
        >
          <wd-input
            custom-class="w-full"
            v-model="inputInnerValue"
            :type="inputType"
            :no-border="true"
            :placeholder="inputPlaceholder || '请输入'"
            :maxlength="maxlength"
            placeholder-class="text-secondary-26 text-text-weak"
            @input="inputValChange"
          />
        </view>
      </slot>
      <view class="flex-center mt-8">
        <AppButton
          className="rounded-4 !bg-page-background !text-text-secondary border-none !w-60 !mr-4"
          v-if="type !== 'alert'"
          @click="cancel"
        >
          {{ cancelText }}
        </AppButton>
        <AppButton
          :className="`rounded-4 ${type === 'alert' ? '!w-124' : '!w-60'} `"
          type="brand"
          @click="confirm"
        >
          {{ confirmText }}
        </AppButton>
      </view>
      <slot name="footer"></slot>
    </view>
  </wd-popup>
</template>
<script setup lang="ts">
import AppButton from '@/components/AppButton/index.vue';
import { showSingleToast } from '@/utils/jsapi';

const props = withDefaults(
  defineProps<{
    show: boolean;
    title: string;
    type: 'alert' | 'confirm' | 'prompt';
    msg: string;
    inputValue: string;
    inputType?: 'text' | 'number' | 'digit' | 'idcard';
    maxlength?: number;
    inputPlaceholder?: string;
    inputPattern?: RegExp;
    inputValidate?: (value: string) => boolean;
    inputErrorMsg?: string;
    cancelText?: string;
    confirmText?: string;
  }>(),
  {
    show: false,
    title: '提示',
    msg: '',
    type: 'alert',
    inputValue: '',
    inputType: 'text',
    inputPlaceholder: '请输入',
    maxlength: undefined,
    inputPattern: undefined,
    inputErrorMsg: '',
    inputValidate: () => true,
    cancelText: '取消',
    confirmText: '确定',
  },
);
const inputInnerValue = ref(props.inputValue);
const emits = defineEmits([
  'update:show',
  'update:inputValue',
  'confirm',
  'cancel',
  'close',
  'inputValidatorFn',
]);
const inputValChange = (e: { value: string | number }) => {
  emits('update:inputValue', e.value);
};
watch(
  () => props.show,
  (val) => {
    const timer = setTimeout(() => {
      inputInnerValue.value = '';
      clearTimeout(timer);
    }, 1000);
  },
);
const close = () => {
  emits('update:show', false);
  emits('close');
};
const confirm = () => {
  if (props.type === 'prompt') {
    if (props.inputPattern && !props.inputPattern.test(inputInnerValue.value)) {
      showSingleToast(props.inputErrorMsg || '请输入正确的格式');
      return;
    }
    if (props.inputValidate && !props.inputValidate(inputInnerValue.value)) {
      showSingleToast(props.inputErrorMsg || '请输入正确的格式');
      return;
    }
  }
  emits('confirm', inputInnerValue.value);
  emits('update:show', false);
};
const cancel = () => {
  emits('cancel');
  emits('update:show', false);
  if (props.type === 'prompt') {
    emits('update:inputValue', '');
  }
};
</script>
<style></style>
