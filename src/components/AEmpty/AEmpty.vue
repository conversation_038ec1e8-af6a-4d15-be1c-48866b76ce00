<script setup lang="ts">
import { debounce } from '@/utils/debounce';
import { defineEmits, defineProps, withDefaults } from 'vue';
import { Colors } from '../../../config/colors';
import AppButton from '../AppButton/index.vue';
import OSSImg from '../OSSImg/index.vue';
import { EmptyType } from './contants';

const props = withDefaults(
  defineProps<{
    type?: 'notFound' | 'content' | 'net' | 'loading';
    image?: string;
    imageHeight?: number | string;
    imageWidth?: number | string;
    title?: string | undefined;
    desc?: string | undefined;
    button?: boolean;
    buttonText?: string;
    imageGap?: number;
    loading?: boolean;
    className?: string;
  }>(),
  {
    image: '',
    imageHeight: 300,
    imageWidth: 300,
    title: undefined,
    desc: undefined,
    type: 'content',
    button: false,
    buttonText: '刷新',
    imageGap: undefined,
    loading: false,
    className: '',
  },
);

const emits = defineEmits(['buttonClick']);

const handleClick = () =>
  debounce(() => {
    emits('buttonClick');
  }, 200);
</script>

<script lang="ts">
export default {
  name: 'AEmpty',
};
</script>

<template>
  <view class="mx-a relative w-full flex-center flex-col" :class="props.className">
    <view>
      <OSSImg
        className="bg-center bg-no-repeat bg-cover"
        :src="props?.image || EmptyType[props.type].image"
        :height="imageHeight"
        :width="imageWidth"
      />
    </view>
    <view
      class="relative flex-center flex-col mt-6"
      :style="{
        marginTop: props.imageGap ? `${props.imageGap}rpx` : '24rpx',
      }"
    >
      <view
        class="text-primary-28 text-text-primary"
        v-if="props.title || props.title === undefined"
      >
        <text>{{ props.title ?? EmptyType[props.type].title }}</text>
      </view>
      <view
        class="text-secondary-24 text-text-sub mt-1"
        v-if="props.desc || props.desc === undefined"
      >
        <text>{{ props.desc ?? EmptyType[props.type].desc }}</text>
      </view>
    </view>
    <AppButton
      v-if="props.button"
      type="brand"
      className="mt-6 !bg-transparent"
      shape="circle"
      :plain="true"
      size="mini"
      @click="handleClick"
    >
      <wd-loading v-if="props.loading" :color="Colors['brand-primary']" size="30rpx" />
      <text
        class="text-secondary-26"
        :style="{
          marginLeft: props.loading ? '16rpx' : '0',
        }"
      >
        {{ props?.buttonText || '返回' }}
      </text>
    </AppButton>
  </view>
</template>

<style scoped lang="scss"></style>
