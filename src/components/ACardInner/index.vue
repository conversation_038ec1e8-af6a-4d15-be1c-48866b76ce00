<template>
  <view class="w-full overflow-hidden">
    <view class="flex w-full items-center">
      <view class="text-8 font-500 truncate flex-1">{{ props.title || '' }}</view>
      <slot name="titleInnerSlot"></slot>
    </view>
    <view class="slider-1px my-6"></view>
    <view class="text-secondary-26 text-text-secondary">
      <view
        class="flex justify-between items-center"
        v-for="(item, index) in props.infoList"
        :key="item.key"
      >
        <view class="truncate">
          {{ item.label }}:
          {{
            item.formatter
              ? item.formatter(props.dataSource[item.key])
              : props.dataSource[item.key] || '--'
          }}
        </view>
        <view
          v-if="
            index === 0 &&
            props.price &&
            props.price.priceKey &&
            props.dataSource[props.price.priceKey]
          "
          class="shrink-0 ml-4 text-primary-32 text-text-primary"
        >
          {{ props.price.unitBefore || '' }}{{ props.dataSource[props.price.priceKey] }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { withDefaults } from 'vue';

interface infoItem {
  label: string;
  key: string;
  formatter?: (value: any) => any;
}

const props = withDefaults(
  defineProps<{
    title: string;
    infoList: infoItem[];
    dataSource: any;
    price?: {
      unitBefore?: string;
      priceKey?: string;
    };
  }>(),
  {
    title: '',
    infoList: () => [],
    dataSource: () => ({}),
  },
);
</script>
