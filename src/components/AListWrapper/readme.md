| 对外提供   | 说明         |
| ---------- | ------------ |
| list       | 全部数据     |
| refresh    | 刷新         |
| clean      | 清空         |
| pageConfig | 分页器数据   |
| isLoad     | 是否正在加载 |

## 用法

```vue
<template>
  <AListWrapper
    v-slot="{ item }"
    ref="listRef"
    :getData="getData"
    autoFit
    :failCallBack="handleErr"
  >
    <childComp>{{ item.name }}</childComp>
  </AListWrapper>
</template>
<script setup lang="ts">
const listRef = ref(null)

const getData = async (page: number, pageSize: number) => {
  // mock
  const data = await queryList({
    page,
    pageSize,
    custom: '自定义参数',
  })
  return data
}

// 获取list
console.log('listRef', listRef.value.list)
</script>
```
