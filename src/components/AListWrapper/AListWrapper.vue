<!-- 废弃 虚拟列表在小程序会闪烁 待优化  -->
<script setup lang="ts">
export interface DataRepType {
  [key: string]: any;
  currentPage: number;
  totalPage: number;
  totalNum: number;
  hasMore: boolean;
  dataList: any[] | null | undefined;
}

const props = withDefaults(
  defineProps<{
    // 是否开启下拉刷新
    enableRefresh: boolean;
    // 是否开启触底加载更多
    loadMord: boolean;
    // 容器高度
    height: string | number;
    // 下拉刷新文案
    loadingText: string;
    // 没有更多文案
    noMoreText: string;
    // 错误文案
    errorText: string;
    // 无数据文案
    noneText: string;
    // 是否自动铺满
    autoFit: boolean;
    // 自定义底部
    customBottom: boolean;
    // 函数
    getData: (page: number, pageSize: number) => any;
    // limit
    pageSize: number;
    // 是否开始时加载
    start: boolean;
    // 失败时执行的函数
    failCallBack: () => any;
  }>(),
  {
    enableRefresh: false,
    loadMord: true,
    height: 100,
    loadingText: '加载中...',
    noMoreText: '没有更多了',
    errorText: '加载失败',
    noneText: '暂无数据',
    autoFit: false,
    customBottom: false,
    pageSize: 10,
    start: true,
    failCallBack: () => {},
  },
);

const emit = defineEmits(['update:modelValue']);
const list = ref<any[]>([]);
// 底部提示文案map
const bottomTextMap = ref<{
  loading: string;
  noMore: string;
  [key: string]: string;
}>({
  loading: props.loadingText,
  noMore: props.noMoreText,
  error: props.errorText,
  none: props.noneText,
});
// 底部提示状态
const bottomStatus = ref('loading');

// 加载锁
const loading = ref(false);

// 下拉刷新状态
const refresherTriggered = ref(false);

// 自适应高度
const fitHeight = ref(0);

// 分页器
const pageConfig = ref({
  page: 1,
  pageSize: 10,
  totalNum: 0,
});

// 获取当前实例
const instance = getCurrentInstance();

// 获取自适应高度
const getFitHeight = () => {
  if (!props.autoFit) return 0;
  // 获取节点到顶部的距离
  uni.getSystemInfo().then((res) => {
    const query = uni.createSelectorQuery().in(instance);
    query.select('#list-wrapper').boundingClientRect((data) => {
      const height = res.windowHeight - ((data as UniApp.NodeInfo)?.top || 0);
      fitHeight.value = height;
    });
    query.exec();
  });
};

// 生成虚拟id
const generateVirtualId = () => {
  return Math.random().toString(36).substr(2, 10);
};

// 数据处理
const handleGetData = async () => {
  const { page, pageSize } = pageConfig.value;
  // 边界判断
  if (loading.value || bottomStatus.value === 'noMore') {
    return;
  }
  loading.value = true;

  try {
    const [err, res] = await props.getData(page, pageSize);
    loading.value = false;
    if (res) {
      complete(res.data);
    } else {
      bottomStatus.value = 'error';
      await props.failCallBack();
    }
  } catch (error) {
    loading.value = false;
    bottomStatus.value = 'error';
    await props.failCallBack();
  }
};

// 触底加载更多
const handleScrollToLower = () => {
  if (bottomStatus.value !== 'noMore') {
    handleGetData();
  }
};

// 合并数据
const complete = (data: DataRepType) => {
  const { dataList, currentPage, totalNum, totalPage, hasMore } = data;
  // 注入虚拟id
  dataList?.forEach((item) => {
    item.virtualId = generateVirtualId();
  });

  pageConfig.value = {
    page: pageConfig.value.page + 1,
    pageSize: props.pageSize,
    totalNum,
  };
  if (dataList === null || dataList === undefined || dataList.length === 0) {
    bottomStatus.value = currentPage === 1 ? 'none' : 'noMore';
    return;
  }
  list.value = [...list.value, ...dataList];

  bottomStatus.value = 'loading';
  if (!hasMore) {
    bottomStatus.value = 'noMore';
  }
  nextTick(() => {
    if (props.autoFit) {
      getFitHeight();
    }
  });
};

// 下拉刷新
const refresh = async () => {
  setTimeout(async () => {
    bottomStatus.value = 'loading';
    pageConfig.value = {
      page: 1,
      pageSize: props.pageSize,
      totalNum: 0,
    };
    list.value = [];
    await handleGetData();
    refresherTriggered.value = false;
  }, 200);
};

const clean = () => {
  list.value = [];
  pageConfig.value = {
    page: 1,
    pageSize: props.pageSize,
    totalNum: 0,
  };
  bottomStatus.value = 'loading';
  refresherTriggered.value = false;
};

const handleRefreshPulling = () => {
  refresherTriggered.value = true;
};

// 滚动高度
const scrollHeight = computed(() => {
  if (props?.autoFit) {
    let height = fitHeight.value;
    if (height === 0) {
      height = 100;
    }
    return `${height}px`;
  }
  return `${props.height}px`;
});

onMounted(() => {
  if (!props.start) return;
  handleGetData();
});
defineExpose({
  list,
  refresh,
  clean,
  pageConfig,
  isLoad: loading,
});
</script>

<script lang="ts">
export default {
  name: 'AListWrapper',
};
</script>

<template>
  <view id="list-wrapper">
    <scroll-view
      :style="{
        height: scrollHeight,
      }"
      class="wrapper"
      :scroll-y="true"
      :refresher-enabled="props.enableRefresh"
      refresher-background="transparent"
      :refresher-triggered="refresherTriggered"
      @scrolltolower="handleScrollToLower"
      @refresherpulling="handleRefreshPulling"
      @refresherrefresh="refresh"
    >
      <block v-for="(el, index) in list" :key="el.virtualId">
        <slot :item="el" :index="index" :id="el.virtualId" />
      </block>
      <view v-if="!props.customBottom" class="loadmore">
        <text>{{ bottomTextMap[bottomStatus] }}</text>
      </view>
      <slot name="bottom" v-else />
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.warpper {
  @apply relative;
}

.loadmore {
  @apply flex justify-center items-center text-[#999999] text-28rpx h-60rpx w-full relative py-4;
}
</style>
