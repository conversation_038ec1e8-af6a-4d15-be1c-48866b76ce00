<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    className?: string;
    divider?: boolean;
    title?: string;
    titleClass?: string;
  }>(),
  {
    className: '',
    divider: false,
    title: '',
    titleClass: '',
  },
);

const emit = defineEmits(['click']);
</script>

<script lang="ts">
export default {
  name: 'ACard',
};
</script>

<template>
  <view
    class="w-702rpx rd-4 bg-white relative p-6 box-border mx-a"
    :class="props.className"
    @click="emit('click')"
  >
    <view class="text-primary-32 text-text-primary" :class="props.titleClass">
      <template v-if="props.title">
        <text>{{ props.title }}</text>
      </template>
      <slot v-else name="title"></slot>
    </view>
    <view v-if="props.divider" class="w-654rpx h-1rpx bg-[#eeeeee] my-6"></view>
    <slot></slot>
  </view>
</template>

<style scoped lang="scss"></style>
