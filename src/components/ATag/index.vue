<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    className?: string;
    text?: string;
    type?: 'success' | 'tip' | 'wait' | 'fail';
    color?: string;
    round?: boolean;
  }>(),
  {
    className: '',
    text: '',
    type: 'success',
    color: '',
    round: false,
  },
);

const status = {
  success: ['bg-status-success', 'text-status-success'],
  tip: ['bg-status-tip', 'text-status-tip'],
  wait: ['bg-status-wait', 'text-status-wait'],
  fail: ['bg-status-fail', 'text-status-fail'],
};

// 获取透明度0.15的背景色
const getBgColor = computed(() => {
  const { color } = props;
  if (!color) return '';
  // 判断是否是16进制颜色
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, 0.15)`;
  } else if (color.startsWith('rgb')) {
    const rgb = color.match(/\d+/g);
    return `rgba(${rgb![0]}, ${rgb![1]}, ${rgb![2]}, 0.15)`;
  }
});
</script>

<script lang="ts">
export default {
  name: 'ATag',
};
</script>

<template>
  <view
    class="py-1 px-2 rd-1 bg-op-15 text-22rpx-26rpx"
    :class="[...status[props.type]]"
    :style="{
      borderRadius: props.round ? '999rpx' : '4rpx',
      color: props.color,
      backgroundColor: getBgColor,
    }"
  >
    <text>{{ props.text }}</text>
  </view>
</template>

<style scoped lang="scss"></style>
