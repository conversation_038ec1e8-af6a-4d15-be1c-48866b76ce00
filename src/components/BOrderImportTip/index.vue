<template>
  <view
    v-if="showTip"
    class="flex justify-between items-center bg-#FFF9ED px-8 py-18rpx text-#FF5E31 text-secondary-24 box-border"
    :class="props.className"
  >
    <view class="flex items-center">
      <OSSImg src="/images/order/import-tip-icon.png" :width="36" :height="36" />
      <view class="ml-3">历史支付订单导入</view>
    </view>
    <view class="flex items-center">
      <view class="mr-6" @click="showImportTip = true">立即导入</view>
      <OSSImg
        src="/images/order/import-tip-close.png"
        :width="28"
        :height="28"
        @click="showCloseTip = true"
      />
    </view>
  </view>
  <AMessageBox
    v-model:show="showImportTip"
    type="confirm"
    :msg="`支持支付成功的订单导入，\n导入后支持开票，是否继续导入？`"
    @confirm="handleImportOrder"
  />
  <AMessageBox
    v-model:show="showCloseTip"
    type="confirm"
    msg="今日内不再提示"
    @confirm="closeTip"
  />
</template>
<script setup lang="ts">
import OSSImg from '@/components/OSSImg/index.vue';
import { ParkingOrderService } from '@/parkService';
import AMessageBox from '@/components/AMessageBox/index.vue';
import dayjs from 'dayjs';
import { showSingleToast } from '@/utils/jsapi';

const props = withDefaults(
  defineProps<{
    className?: string;
  }>(),
  {
    className: '',
  },
);
const showTip = ref(false);
const showImportTip = ref(false);
const showCloseTip = ref(false);
const checkHasOrder = async () => {
  const [err, res] = await ParkingOrderService.postOrderHasUnloggedOrders();
  showTip.value = res?.data?.hasUnloggedOrder || false;
};
onMounted(() => {
  if (uni.getStorageSync('importTipClose')) {
    if (dayjs().format('YYYY-MM-DD') !== uni.getStorageSync('importTipClose')) {
      showTip.value = true;
      uni.removeStorageSync('importTipClose');
    } else {
      showTip.value = false;
    }
  } else {
    checkHasOrder();
  }
});
const closeTip = () => {
  uni.setStorageSync('importTipClose', dayjs().format('YYYY-MM-DD'));
  showTip.value = false;
};
const handleImportOrder = async () => {
  const [err, res] = await ParkingOrderService.postOrderMergeUnLoggedOrders();
  if (err) {
    showSingleToast(err.subMsg || '导入失败');
  } else {
    showTip.value = false;
    uni.$emit('importOrderSuccess');
  }
};
</script>
<style lang="scss"></style>
