<script setup lang="ts">
import { debounce } from '@/utils/debounce';

const props = withDefaults(
  defineProps<{
    title?: string;
    className?: string;
    to?: string;
    redirectTo?: boolean;
    clickable?: boolean;
    arrow?: boolean;
    border?: boolean;
  }>(),
  {
    title: '',
    default: 'text-primary-32 text-black-primary',
    redirectTo: false,
    clickable: true,
    arrow: true,
    border: false,
    className: '',
  },
);

const handleClick = () =>
  debounce(() => {
    if (!props.clickable) return;
    if (props.redirectTo) {
      uni.redirectTo({ url: props.to });
    } else {
      uni.navigateTo({ url: props.to });
    }
  }, 200);
</script>

<script lang="ts">
export default {
  name: 'ACellNav',
};
</script>

<template>
  <view
    class="bg-white max-w-702rpx min-h-86rpx of-hidden box-border p-6 rd-4 flex flex-items-center justify-between transition-colors mx-a"
    :class="[
      props.clickable ? ' hover active:(bg-[#F5F5F5])' : '',
      props.className,
      props?.border ? 'cell-border' : '',
    ]"
    @click="handleClick"
  >
    <template v-if="props.title">
      <text class="text-primary-32 text-text-primary">{{ props.title }}</text>
    </template>
    <slot v-else />
    <view class="flex items-center">
      <slot name="right" />
      <OSSImg v-if="props.arrow" width="48" height="48" src="/images/common/arrow-right.png" />
      <slot v-else name="icon" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.cell-border {
  position: relative;

  &::after {
    content: '';
    border-top: 1rpx solid #eee;

    @apply absolute bottom-0 left-0 right-0;
  }
}
</style>
