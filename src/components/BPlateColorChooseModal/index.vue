<template>
  <AMessageBox
    v-model:show="show"
    type="confirm"
    @confirm="confirm"
    :cancel-text="textMap.cancelBtnText"
    :confirm-text="textMap.confirmBtnText"
  >
    <template>
      <view class="mt-4 text-center text-secondary-26 text-text-secondary">
        {{ textMap.msg }}
      </view>
      <radio-group @change="radioChange">
        <label
          class="flex mx-auto w-124 box-border items-center px-12 h-20 rounded-4 bg-page-background mt-4"
          v-for="(item, index) in items"
          :key="item.value"
        >
          <view class="wh-18rpx rounded-full" :style="{ backgroundColor: item.color }"></view>
          <view class="ml-3 text-text-gray text-primary-32 flex-1">
            {{ language === 'zh' ? item.name : item.enName }}
          </view>
          <view class="wh-12 flex-center">
            <radio
              class="!bg-page-background"
              backgroundColor="#F8F8F8"
              :color="Colors['brand-primary']"
              borderColor="rgba(0, 0, 0, 0.15)"
              :value="item.value"
              :checked="item.value === selectedColor"
            />
          </view>
        </label>
      </radio-group>
    </template>
  </AMessageBox>
</template>
<script setup lang="ts">
import AMessageBox from '@/components/AMessageBox/index.vue';
import { Colors } from '../../../config/colors';

const props = withDefaults(
  defineProps<{
    language: 'zh' | 'en';
  }>(),
  {
    language: 'zh',
  },
);
const emits = defineEmits(['changeShow', 'confirmColor']);
const show = ref(true);
const items = [
  { value: 'BLUE', name: '蓝牌', enName: 'Blue card', color: '#3183FF' },
  { value: 'YELLOW', name: '黄牌', enName: 'Yellow card', color: '#FFCC00' },
];
const selectedColor = ref('BLUE');
const radioChange = (e: any) => {
  selectedColor.value = e.detail.value;
};
const textMap = computed(() => {
  if (props.language === 'zh') {
    return {
      msg: '存在相同车牌号，请选择确认车牌颜色',
      confirmBtnText: '确定',
      cancelBtnText: '取消',
    };
  } else {
    return {
      msg: 'Please select your vehicle',
      confirmBtnText: 'Confirm',
      cancelBtnText: 'Cancel',
    };
  }
});
watch(
  () => show.value,
  (val: boolean) => {
    setTimeout(() => {
      emits('changeShow', val);
    }, 300);
  },
);
const confirm = () => {
  emits('confirmColor', selectedColor.value);
};
</script>
