<script setup lang="ts">
import { debounce } from '@/utils/debounce';

const props = withDefaults(
  defineProps<{
    options: string[] | number[];
    disabled?: boolean;
    modelValue: number;
  }>(),
  {
    disabled: false,
    modelValue: 0,
  },
);

const emits = defineEmits(['update:modelValue', 'change']);

const selectedId = ref<number>(props.modelValue); // 当前选中的选项

// 切换选项
const handleClick = (id: number) =>
  debounce(() => {
    if (props.disabled) return;
    selectedId.value = id;
    emits('update:modelValue', id);
    emits('change', {
      id,
      value: props.options[id],
    });
  }, 200);
</script>

<script lang="ts">
export default {
  name: 'ATabSelect',
};
</script>

<template>
  <scroll-view
    :scroll-x="true"
    :show-scrollbar="false"
    class="box-border ws-nowrap mr--6"
    style="width: 100%"
  >
    <view
      v-for="(item, id) in props.options"
      :key="id"
      class="rd-999rpx py-3 px-6 flex-center inline-block mr-6 bg-black bg-op-5 flex-center text-26rpx-31rpx"
      :class="{ 'is-active': selectedId === id }"
      @click="handleClick(id)"
    >
      <text class="text-text-primary" :class="{ 'is-active': selectedId === id }">{{ item }}</text>
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
.is-active {
  @apply text-brand-primary font-medium text-26rpx-31rpx bg-brand-light  bg-op-100;
}
</style>
