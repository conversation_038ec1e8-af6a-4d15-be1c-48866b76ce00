<template>
  <view
    class="flex-1 rounded-5 flex items-center h-15 box-border pl-5 pr-6rpx box-border of-hidden"
    :style="{ backgroundColor: bgColor }"
  >
    <OssImage className="mr-2" :width="32" :height="32" src="/images/home/<USER>" />
    <view class="flex-1 flex items-center text-primary-28 leading-15 font-400 of-hidden">
      <!-- <wd-input
        custom-class="w-full !bg-transparent"
        v-model="keyword"
        no-border
        placeholder="搜索内容"
        placeholder-class="text-text-weak"
        :disabled="disabledInput"
      /> -->
      <template v-if="keyword">
        <view class="flex-1 text-text-primary truncate" @click="onClick">
          {{ keyword }}
        </view>
        <view class="p-2" @click.stop="clearKeyword">
          <OssImage :width="32" :height="32" src="/images/nearby/close.png" />
        </view>
      </template>
      <template v-else>
        <view class="flex-1 text-text-weak" @click="onClick">搜索内容</view>
      </template>
    </view>
    <view
      v-if="showSearch"
      class="py-1 px-4 rounded-4 bg-brand-lighter text-primary-28 text-brand-primary"
      @click="search"
    >
      搜索
    </view>
  </view>
</template>
<script setup lang="ts">
import OssImage from '@/components/OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    keyword: string;
    showSearch: boolean;
    disabledInput: boolean;
    bgColor?: string;
  }>(),
  {
    keyword: '',
    showSearch: false,
    disabledInput: true,
    bgColor: '#ffffff',
  },
);
const emits = defineEmits(['search', 'clearKeyword', 'click']);
// const keywordInner = ref(props.keyword);
// watch(keywordInner, (val) => {
//   emit('changeKeyword', val);
// });
const search = () => {
  emits('search');
};
const onClick = () => {
  emits('click');
};
const clearKeyword = () => {
  emits('clearKeyword');
};
</script>
