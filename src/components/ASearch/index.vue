<template>
  <wd-input
    custom-class="is-no-border border-1rpx rounded-2 border-solid border-#e5e5e5 px-5 py-2"
    v-model="searchText"
    placeholder="请输入"
    @blur="showFlag = false"
    @focus="showFlag = !showFlag"
    placeholder-class="text-6 text-#979797"
    class="h-35rpx"
  />
  <view v-if="showFlag && list.length !== 0" class="options-card">
    <text class="options-item" v-for="item in list" :key="item.content" @click="changeValue(item)">
      {{ item.content }}
    </text>
  </view>
</template>

<script lang="ts" setup>
const emit = defineEmits(['update:modelValue']);

interface SelectOption {
  content: string;
}

const props = withDefaults(
  defineProps<{
    localData: Array<SelectOption>;
    remoteUrl: string;
    modelValue: string;
  }>(),
  {
    localData: () => [],
    remoteUrl: '',
    modelValue: '',
  },
);

const list = ref<Array<SelectOption>>([]);
const showFlag = ref(false);
watch(
  () => props.localData,
  (newVal) => {
    list.value = newVal;
  },
  { immediate: true, deep: true },
);

// 响应式状态
const searchText = ref();
watch(
  () => props.modelValue,
  (newVal) => {
    searchText.value = newVal;
  },
);

watch(
  () => searchText.value,
  (newVal) => {
    emit('update:modelValue', newVal);
  },
);

const changeValue = (data) => {
  searchText.value = data.content;
};
</script>

<style scoped lang="scss">
.options-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 400rpx;
  padding: 8rpx 0;
  margin-top: 4rpx;
  overflow: scroll;
  background-color: #fff;
  border: 1rpx solid #ebeef5;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 24rpx 0 rgb(0 0 0 / 10%);
}

.options-item {
  padding: 0rpx 20rpx;
  font-size: 28rpx;
  line-height: 70rpx;
}
</style>
