<template>
  <view class="card-bg flex">
    <view class="w-46 flex flex-col flex-center flex-shrink-0">
      <view v-if="item.prizeType === 'DISCOUNT'" class="text-#FF3B30 text-6">
        <span class="text-20 font-DIN">{{ item.discount }}</span>
        折
      </view>

      <view v-if="item.prizeType !== 'DISCOUNT'" class="text-#FF3B30 text-6">
        <span class="font-DIN" :class="getFontSize(item.discountAmount)">
          {{ item.discountAmount }}
        </span>
        元
      </view>
      <view v-if="item.prizeType !== 'DISCOUNT'" class="text-22rpx text-#666">
        满{{ item.orderThreshold }}可用
      </view>
    </view>
    <view class="px-6 py-8 flex-1">
      <view class="flex justify-between">
        <view class="flex flex-col justify-center">
          <view>
            <view class="text-7 font-medium line-clamp-1">{{ item.prizeName }}</view>
            <view class="text-22rpx text-#666 line-clamp-1">
              有效期:{{ dayjs(item.effectiveTime).format('YYYY-MM-DD') }}~{{
                dayjs(item.expireTime).format('YYYY-MM-DD')
              }}
            </view>
          </view>

          <view class="text-5 text-#999 line-clamp-1" v-if="item.useInstructions">
            {{ item.useInstructions }}
          </view>
        </view>

        <view class="flex-center">
          <view
            class="h-50rpx w-90rpx bg-white rounded-2 border-#FF3B30 border-1 border-solid flex flex-center text-#FF3B30 text-6 font-medium"
            @click="toCouponDetail(item.couponCode)"
          >
            查看
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { goTo } from '@/utils/route';
import dayjs from 'dayjs';

const props = withDefaults(
  defineProps<{
    item: {
      prizeType: string;
      discount: string;
      discountAmount: string;
      orderThreshold: string;
      prizeName: string;
      effectiveTime: string;
      expireTime: string;
      useInstructions: string;
      couponCode: string;
    };
  }>(),
  {
    item: () => {
      return {
        prizeType: '',
        discount: '',
        discountAmount: '',
        orderThreshold: '',
        prizeName: '',
        effectiveTime: '',
        expireTime: '',
        useInstructions: '',
        couponCode: '',
      };
    },
  },
);

function toCouponDetail(couponCode: string) {
  goTo('/pages/marketing/coupon/detail/index', { couponCode });
}

const getFontSize = (discountAmount: string) => {
  switch (discountAmount.length) {
    case 1:
      return 'text-20';
    case 2:
      return 'text-18';
    case 3:
      return 'text-16';
    case 4:
      return 'text-14';
    case 5:
      return 'text-12';
    case 6:
      return 'text-10';
    default:
      return 'text-20';
  }
};
</script>
<style lang="scss" scoped>
.card-bg {
  width: 702rpx;
  height: 168rpx;
  background-image: url('#{$oss-prefix}/images/mine/coupon_a.png');
  background-repeat: no-repeat;
  background-size: contain;
}
</style>
