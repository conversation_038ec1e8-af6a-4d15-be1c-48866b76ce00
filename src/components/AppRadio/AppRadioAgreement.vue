<template>
  <view class="flex items-center" :class="['radio-wrap', `radio-wrap--${size}`]">
    <!--    <view :class="['radio', { 'radio&#45;&#45;checked': value }]" @click="change"></view>-->
    <view
      :class="[
        'mr-3 wh-9',
        value
          ? 'i-mdi-check-circle text-brand-primary'
          : 'i-mdi-checkbox-blank-circle-outline text-#ccc',
      ]"
      @click="change"
    ></view>
    <view class="label">
      <template v-if="protocolList && protocolList.length > 0">
        <text @click="change">{{ text }}</text>
        <template v-for="(item, index) in protocolList" :key="item.code">
          <text class="label__filename" @click="onProtocolClick(index)">《{{ item.name }}》</text>
          <text v-if="index < protocolList.length - 1">和</text>
        </template>
      </template>
      <slot v-else></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useRichTextStore } from '@/store';
import { onProtocolItemClick } from '@/utils/protocolClick';

const props = withDefaults(
  defineProps<{
    value: boolean;
    size: 'default' | 'mini' | 'large';
    // 协议前的文案
    text: string;
    // 协议列表
    protocolList: any;
  }>(),
  {
    value: false,
    size: 'default',
    text: '',
    protocolList: [],
  },
);

const emit = defineEmits(['input']);

const { setContent } = useRichTextStore();

const change = () => {
  emit('input', !props.value);
};

const onProtocolClick = (index: number) => {
  // todo 这里有可能出现富文本的情况
  const item = props.protocolList[index];
  onProtocolItemClick(item);
};
</script>

<style lang="scss" scoped>
.radio-wrap {
  display: flex;

  .radio {
    position: relative;
    display: inline-block;
    width: 28rpx;
    height: 28rpx;
    margin-right: 12rpx;
    font-size: 0;

    &::after {
      position: absolute;
      inset: 0;
      width: 200%;
      height: 200%;
      content: '';
      border: 1rpx solid #ccc;
      border-radius: 50%;
      transform: scale(0.5);
      transform-origin: 0 0;
    }

    &--checked {
      &::after {
        background-image: url('#{$oss-prefix}/static/images/address/radio.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        border-width: 0;

        @apply bg-brand-primary;
      }
    }
  }

  .label {
    flex: 1;

    &__filename {
      @apply color-brand-primary;
    }
  }

  &--default {
    .radio {
      width: 34rpx;
      height: 34rpx;
    }

    .label {
      font-size: 28rpx;
      line-height: 32rpx;
      color: #999;
    }
  }

  &--large {
    .radio {
      width: 44rpx;
      height: 44rpx;
      transform: translateY(-2rpx);
    }

    .label {
      font-size: 30rpx;
      line-height: 42rpx;
      color: #333;
    }
  }

  &--mini {
    .radio {
      width: 28rpx;
      height: 28rpx;
      /*  #ifdef  MP-WEIXIN  */
      transform: translateY(2rpx);
      /*  #endif  */
    }

    .label {
      font-size: 24rpx;
      line-height: 32rpx;
      color: #999;
    }
  }
}
</style>
