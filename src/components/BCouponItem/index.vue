<template>
  <view
    class="card-bg flex"
    :class="[className, props.item.defrayStatus !== 'GRANTED' ? 'opacity-40' : '']"
    @click="toCouponDetail"
  >
    <view class="w-46 flex flex-col flex-center shrink-0">
      <image
        class="wh-25 rounded-2 of-hidden"
        mode="aspectFill"
        :src="props.item.imgUrl"
        v-if="props.item.imgUrl"
      />
      <OSSImg v-else src="/images/market/coupon-default.png" :width="100" :height="100" />
    </view>
    <view class="px-6 flex-1 flex justify-between items-center of-hidden">
      <view class="flex-1 of-hidden">
        <view class="text-primary-28 text-text-primary flex">
          <view v-if="props.item.classTypeDesc" class="shrink-0">
            【{{ props.item.classTypeDesc }}】
          </view>
          <view class="truncate">
            {{ props.item.awardName }}
          </view>
        </view>
        <view
          class="text-auxiliary text-text-secondary truncate mt-2"
          v-if="props.item.activeTime && props.item.expiryTime"
        >
          有效期:{{ dayjs(props.item.activeTime).format('YYYY-MM-DD') }}~{{
            dayjs(props.item.expiryTime).format('YYYY-MM-DD')
          }}
        </view>
        <view class="text-5 text-text-sub truncate mt-2" v-if="props.item.awardDesc">
          {{ props.item.awardDesc }}
        </view>
      </view>

      <view class="flex-center shrink-0">
        <slot name="right">
          <view
            class="h-50rpx w-90rpx bg-status-fail rounded-166rpx flex-center text-white text-secondary-24"
            v-if="defrayStatusMap[props.item.defrayStatus]"
            @click.stop="goUse"
          >
            {{ defrayStatusMap[props.item.defrayStatus] }}
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import OSSImg from '@/components/OSSImg/index.vue';
import { RightsFlowDetailVO } from '@/service';
import { goTo } from '@/utils/route';
import dayjs from 'dayjs';
import { defrayStatusMap } from './constants';

const props = withDefaults(
  defineProps<{
    item: RightsFlowDetailVO;
    className: string;
    type: 'coupon' | 'couponChoose';
  }>(),
  {
    item: () => ({}),
    className: '',
    type: 'coupon',
  },
);
const emit = defineEmits(['couponClick']);
const toCouponDetail = () => {
  if (props.type === 'couponChoose') {
    emit('couponClick', props.item);
  } else {
    uni.setStorageSync('couponDetail', JSON.stringify(props.item));
    goTo('/pages/marketing/coupon/detail/index');
  }
};
const goUse = () => {
  if (props.item.defrayStatus !== 'GRANTED') return;
  goTo('/pages/travelServices/index');
};
</script>
<style lang="scss" scoped>
.card-bg {
  width: 702rpx;
  height: 168rpx;
  background-image: url('#{$oss-prefix}/images/mine/coupon_a.png');
  background-repeat: no-repeat;
  background-size: contain;
}
</style>
