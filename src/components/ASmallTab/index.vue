<template>
  <view>
    <view class="h-14">
      <block v-for="(item, index) in tabList" :key="index">
        <text
          @click="activeChange(index)"
          :class="[
            'align-baseline not-first:ml-50rpx',
            activeIndex === index ? 'text-8 font-bold relative tab-active' : 'text-7 text-#666',
          ]"
        >
          {{ item }}
        </text>
      </block>
    </view>
  </view>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    tabList: string[];
  }>(),
  {},
);
const emit = defineEmits(['click']);
const activeIndex = ref(0);
const activeChange = (index: number) => {
  if (activeIndex.value === index) return;
  activeIndex.value = index;
  emit('click', {
    item: props.tabList[index],
    index,
  });
};
</script>
<style lang="scss" scoped>
.tab-active::after {
  top: 50rpx;
  left: 50%;
  display: block;
  content: '';
  transform: translateX(-50%);

  @apply bg-brand-primary w-10 h-6rpx rounded-tl-16 rounded-br-16 absolute;
}
</style>
