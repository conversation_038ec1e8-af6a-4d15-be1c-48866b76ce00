<template>
  <view
    class="fixed left-0 right-0 bottom-0 z-10 bg-#e2e1e4"
    :class="props.show ? 'block' : 'hidden'"
    @click.stop="() => {}"
  >
    <view class="">
      <view
        v-if="$slots.header || $slots.close"
        class="bg-divider-color text-text-primary text-right h-33px w-full"
      >
        <slot name="header" />
      </view>
      <view
        v-if="$slots.close"
        class="absolute right-0 top-0 px-10px text-#108ee9 text-primary-28 !font-400 leading-33px"
      >
        <slot name="close"></slot>
      </view>
      <view class="w-full pb-safe-10px leading-20px overflow-hidden" :style="{ paddingLeft: '3%' }">
        <slot />
        <view class="w-100vw">
          <view
            v-for="(item, index) in keyboardItems"
            :key="index"
            class="keyboard-key-item"
            :class="[
              currentInputIndex === 0 ? 'keyboard-item-' + index : '',
              item.disable ? '!bg-#ececed' : 'bg-white',
            ]"
            :data-index="index"
            @click.stop="onKeyItemClick(item)"
          >
            <view class="keyboard-key-content">{{ item.name }}</view>
          </view>
          <view v-if="currentInputIndex === 0" class="keyboard-key-item more" @click.stop="onMore">
            <view class="keyboard-key-content">
              {{ moreMode ? TextMap.back : TextMap.more }}
            </view>
          </view>
          <view
            class="keyboard-key-item more"
            @click.stop="onDelete"
            v-if="currentInputIndex === 0"
          >
            <view class="keyboard-key-content">{{ TextMap.delete }}</view>
          </view>
          <view class="keyboard-key-item" @click.stop="onDelete" v-else>
            <!-- <view class="keyboard-key-content">{{ TextMap.delete }}</view> -->
            <view class="flex justify-center items-center">
              <wd-icon name="close"></wd-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { PLATE_KEYBOARD_ALPHANUM, PLATE_KEYBOARD_PROVINCE } from './constants';

const props = withDefaults(
  defineProps<{
    language: 'zh' | 'en';
    show: boolean;
    type: 'alphanum' | 'province';
    currentInputIndex: number;
    disabledList: string[];
  }>(),
  {
    language: 'zh',
    show: true,
    type: 'alphanum',
    currentInputIndex: 0,
    disabledList: () => [],
  },
);
const emits = defineEmits(['onKeyItemClick', 'onDelete']);
const TextMap = computed(() => {
  if (props.language === 'zh') {
    return {
      back: '返回',
      more: '更多',
      delete: '删除',
    };
  } else {
    return {
      back: 'Back',
      more: 'More',
      delete: 'Del',
    };
  }
});
const moreMode = ref(false);
const province = PLATE_KEYBOARD_PROVINCE.map((item) => {
  return { name: item, disable: false };
});
const alphanum = PLATE_KEYBOARD_ALPHANUM.map((item) => {
  return { name: item, disable: false };
});
const cProvince = computed(() => {
  if (!moreMode.value) {
    return PLATE_KEYBOARD_PROVINCE.filter(
      (item, index) => index <= PLATE_KEYBOARD_PROVINCE.findIndex((row) => row === '新'),
    ).map((item) => {
      return { name: item, disable: props.disabledList.includes(item) };
    });
  } else {
    const arr: any = new Array(9)
      .fill(0)
      .map((item, index) => index + 1)
      .concat('0QWERTYCVBNASDFGHJKLZX民使'.split('').map((item: any) => item))
      .map((item) => ({ name: item, disable: props.disabledList.includes(String(item)) }));
    return arr;
  }
});
const cAlphanum = computed(() => {
  // if (props.currentInputIndex === 6) {
  //   if (!moreMode.value) {
  //     return alphanum;
  //   } else {
  //     return '学警港澳航挂试超使领1234567890ABCDEFGHJKWXYZ'
  //       .split('')
  //       .map((item) => ({ name: item, disable: props.disabledList.includes(item) }));
  //   }
  // } else {
  //   return '1234567890QWERTYUIOPASDFGHJKLMZXCVBN'
  //     .split('')
  //     .map((item) => ({ name: item, disable: props.disabledList.includes(item) }));
  // }
  return '1234567890QWERTYUP学军ASDFGHJKL警ZXCVBNM港澳'
    .split('')
    .map((item) => ({ name: item, disable: props.disabledList.includes(item) }));
});
const keyboardItems = computed(() => {
  if (props.type === 'province') {
    return cProvince.value;
  } else {
    return cAlphanum.value;
  }
});
const onKeyItemClick = (
  item: { name: string; disable: boolean } | { name: number; disable: boolean },
) => {
  if (item.disable === false) {
    moreMode.value = false;
    emits('onKeyItemClick', item.name);
  }
};
const onMore = () => {
  moreMode.value = !moreMode.value;
};
const onDelete = () => {
  moreMode.value = false;
  emits('onDelete');
};
</script>
<style lang="scss" scoped>
.keyboard-key-item {
  display: inline-block;
  width: 8%;
  padding: 10px 0;
  margin-top: 11px;
  margin-right: 1.6%;
  font-size: 20px;
  font-weight: 400;
  color: #333;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 1px 1px #888;

  .keyboard-key-content {
    flex: 1;
    color: #000;
    text-align: center;
  }

  &.more {
    width: 16%;
  }
}

.keyboard-item-20 {
  margin-left: 4.8%;
}

.keyboard-item-29 {
  // margin-left: 10.2%;
  margin-left: 9.6%;
}
</style>
