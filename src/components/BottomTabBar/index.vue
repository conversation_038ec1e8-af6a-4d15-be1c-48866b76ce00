<template>
  <view class="tabBar p-x-6 pb-safe grid grid-cols-5 tab-bar">
    <view v-for="item in tabList" :key="item.path" class="flex-col-center" @click="btnClick(item)">
      <template v-if="item.imgName !== 'scan'">
        <image
          class="wh-11"
          :src="
            item.active
              ? `/static/images/tabBar/${item.imgName}-active.png`
              : `/static/images/tabBar/${item.imgName}.png`
          "
        />
        <text class="mt-4 text-auxiliary" :class="item.active ? 'text-#F33813' : 'text-gray-black'">
          {{ item.title }}
        </text>
      </template>
      <template v-else>
        <view class="flex-col-center absolute -top-16">
          <image class="wh-35 relative" src="/static/images/tabBar/scan.png" />
          <view class="text-auxiliary" :class="item.active ? 'text-#F33813' : 'text-gray-black'">
            {{ item.title }}
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { checkLogin } from '@/hooks/useCheckLogin';
import { goTo } from '@/utils/route';
import { scanTap } from '@/utils/scanHandler';

interface TabItem {
  path: string;
  title: string;
  active: boolean;
  imgName: string;
}
const tabList = ref<TabItem[]>([
  { path: '/pages/home/<USER>', title: '首页', active: false, imgName: 'home' },
  { path: '/pages/nearby/index', title: '附近', active: false, imgName: 'nearby' },
  { path: '/pages/scan/index', title: '扫一扫', active: false, imgName: 'scan' },
  { path: '/pages/order/index', title: '订单', active: false, imgName: 'shop' },
  { path: '/pages/mine/index', title: '我的', active: false, imgName: 'mine' },
]);

onMounted(() => {
  // if (process.env.UNI_PLATFORM === 'mp-alipay') {
  //   uni.hideTabBar();
  // }
  // uni.hideTabBar()
  const pages = getCurrentPages();
  // @ts-ignore
  const currentPage = pages[pages.length - 1]?.$page?.fullPath;
  tabList.value.forEach((item) => {
    if (item.path === currentPage) {
      item.active = true;
    }
  });
});

const btnClick = async (item: TabItem) => {
  if (item.path === '/pages/scan/index') {
    await scanTap();
  } else if (item.path === '/pages/order/index') {
    // await checkLogin();
    goTo(item.path);
  } else {
    uni.switchTab({
      url: item.path,
    });
  }
};
</script>

<style lang="scss" scoped>
.tab-bar {
  height: 118rpx;
  background: rgb(255 255 255 / 90%);
  backdrop-filter: blur(27.2px);
  box-shadow: inset 0 0.5px 0 0 #eee;
}

.scan {
  @apply relative top--20;
}
</style>
