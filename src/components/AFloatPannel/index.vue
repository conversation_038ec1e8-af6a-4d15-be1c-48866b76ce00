<template>
  <view
    :class="`fixed left-0 right-0 bottom-0 overflow-hidden z-9 flex flex-col ${customClass} ${
      safeAreaInsetBottom ? 'pb-safe-area' : ''
    }`"
    :style="rootStyle"
    @touchstart.passive="handleTouchStart"
    @touchmove.passive="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchEnd"
  >
    <slot name="extra" />
    <view class="shrink-0 h-8 flex justify-center items-center">
      <view class="w-20 h-2 rounded-277rpx bg-black bg-opacity-30"></view>
    </view>
    <view class="flex-1 flex flex-col bg-#F5F5F5 rounded-t-8 overflow-hidden">
      <view class="shrink-0 w-full">
        <slot name="header" />
      </view>
      <view
        class="w-full flex-1 overflow-hidden"
        data-id="content"
        @touchmove.stop.prevent="handleTouchMove"
      >
        <slot />
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { computed, onBeforeMount, ref, watch, type CSSProperties } from 'vue';

interface FloatPanelProps {
  height?: number;
  anchors?: number[];
  duration?: number;
  customClass?: string;
  customStyle?: string;
  safeAreaInsetBottom?: boolean;
  showScrollbar?: boolean;
  contentDraggable?: boolean;
}

const props = withDefaults(defineProps<FloatPanelProps>(), {
  height: 0,
  anchors: () => [],
  duration: 200,
  customClass: '',
  customStyle: '',
  safeAreaInsetBottom: false,
  showScrollbar: false,
  contentDraggable: true,
});

const emit = defineEmits(['update:height', 'height-change']);

// 保持原有的核心逻辑
const heightValue = ref<number>(props.height);
const DAMP = 0.2;
let startY: number;
const windowHeight = ref<number>(0);
const dragging = ref<boolean>(false);

// Touch 处理逻辑
const touch = {
  deltaY: ref(0),
  touchStart(event: TouchEvent) {
    this.deltaY.value = 0;
    this.startY = event.touches[0].clientY;
  },
  touchMove(event: TouchEvent) {
    this.deltaY.value = event.touches[0].clientY - this.startY;
  },
};

const boundary = computed(() => ({
  min: props.anchors[0] ? props.anchors[0] : 100,
  max: props.anchors[props.anchors.length - 1]
    ? props.anchors[props.anchors.length - 1]
    : Math.round(windowHeight.value * 0.6),
}));

const anchors = computed(() =>
  props.anchors.length >= 2 ? props.anchors : [boundary.value.min, boundary.value.max],
);

const rootStyle = computed(() => {
  const style: CSSProperties = {
    height: addUnit(boundary.value.max),
    transform: `translateY(calc(100% + ${addUnit(-heightValue.value)}))`,
    transition: !dragging.value
      ? `transform ${props.duration}ms cubic-bezier(0.18, 0.89, 0.32, 1.28)`
      : 'none',
  };

  return `${objToStyle(style)};${props.customStyle}`;
});

// 工具函数
const addUnit = (value: string | number): string => {
  return typeof value === 'number' ? `${value}px` : value;
};

const objToStyle = (obj: CSSProperties): string => {
  return Object.keys(obj).reduce((acc, key) => {
    return `${acc}${key.replace(/([A-Z])/g, '-$1').toLowerCase()}:${obj[key]};`;
  }, '');
};

const closest = (arr: number[], target: number): number => {
  return arr.reduce((prev, curr) => {
    return Math.abs(curr - target) < Math.abs(prev - target) ? curr : prev;
  });
};

const updateHeight = (value: number) => {
  heightValue.value = value;
  emit('update:height', value);
};

const handleTouchStart = (event: TouchEvent) => {
  touch.touchStart(event);
  dragging.value = true;
  startY = -heightValue.value;
};

const handleTouchMove = (event: TouchEvent) => {
  const target = event.currentTarget as any;
  if (target.dataset.id === 'content') {
    if (!props.contentDraggable) return;
  }
  touch.touchMove(event);
  const moveY = touch.deltaY.value + startY;
  updateHeight(-ease(moveY));
};

const handleTouchEnd = () => {
  dragging.value = false;
  updateHeight(closest(anchors.value, heightValue.value));

  if (heightValue.value !== -startY) {
    emit('height-change', { height: heightValue.value });
  }
};

const ease = (y: number) => {
  const absDistance = Math.abs(y);
  const { min, max } = boundary.value;

  if (absDistance > max) {
    return -(max + (absDistance - max) * DAMP);
  }

  if (absDistance < min) {
    return -(min - (min - absDistance) * DAMP);
  }

  return y;
};

watch(
  () => props.height,
  (value) => {
    heightValue.value = value;
  },
);

watch(
  boundary,
  () => {
    updateHeight(closest(anchors.value, heightValue.value));
  },
  { immediate: true },
);

onBeforeMount(() => {
  const { windowHeight: _windowHeight } = uni.getSystemInfoSync();
  windowHeight.value = _windowHeight;
});
</script>

<style lang="scss" scoped>
.pb-safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
