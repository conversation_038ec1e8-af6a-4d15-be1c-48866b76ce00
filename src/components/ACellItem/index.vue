<template>
  <view class="flex-between items-center w-full">
    <view class="flex items-center">
      <view class="text-text-primary text-primary-32 shrink-0">{{ title }}</view>
      <slot name="titleExtra"></slot>
    </view>
    <slot name="right">
      <view v-if="rightText" class="flex items-center" @click="handleRightClick">
        <text class="text-secondary-24 text-text-sub">{{ rightText }}</text>
        <OSSImg :width="24" :height="24" src="/images/common/arrow-right.png" />
      </view>
    </slot>
  </view>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    title: string;
    rightText?: string;
  }>(),
  {
    title: '',
    rightText: '',
  },
);
const emits = defineEmits(['rightClick']);
const handleRightClick = () => {
  emits('rightClick');
};
</script>
<style lang="scss" scoped></style>
