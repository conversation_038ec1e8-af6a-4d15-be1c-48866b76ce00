<template>
  <view
    class="w-full overflow-x-auto of-y-hidden sticky left-0 top-0 flex justify-start items-stretch pl-6 box-border"
    :class="['tabs-box', `tabs-${props.type}`, props.className]"
  >
    <view
      v-for="(item, index) in props.list"
      :key="index"
      :class="[
        `tabs-${props.type}-item`,
        props.current === index ? 'active' : '',
        'relative',
        customStyle ? customStyle : '',
      ]"
      @click="onItemClick(index)"
    >
      <view
        v-if="item.num"
        class="absolute -top-16rpx -right-12rpx z-1 w-35rpx h-32rpx rounded-16rpx rounded-lb-unset bg-status-fail text-center"
      >
        <text class="text-24rpx leading-32rpx text-white-color">{{ item.num }}</text>
      </view>
      <text>{{ item.label }}</text>
      <slot></slot>
      <!-- 没有动画要求的话就这么写吧 -->
      <view
        v-if="type === 'line' && props.current === index && showLine"
        class="tabs-dotline"
      ></view>
    </view>
  </view>
</template>
<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    list: { label: string; value: string; num?: number }[];
    current: number;
    type: 'capsule' | 'line';
    className: string;
    showLine: boolean;
    customStyle: string;
  }>(),
  {
    list: () => [],
    current: 0,
    type: 'line',
    className: '',
    showLine: true,
    customStyle: '',
  },
);
const emit = defineEmits(['click']);
const onItemClick = (index: number) => {
  if (index === props.current) return;
  emit('click', index);
};
</script>
<style lang="scss" scoped>
.tabs-box {
  &::-webkit-scrollbar {
    display: none;
  }
}

.tabs-capsule-item {
  @apply px-6 py-3 box-border text-secondary-26 leading-31rpx flex-center rounded-26rpx text-text-primary shrink-0 bg-white-color mr-6 min-w-144rpx;

  &.active {
    background: linear-gradient(270deg, #5ebb6c 0%, #75d083 100%);

    @apply bg-brand-lighter text-white-color;
  }
}

.tabs-line-item {
  @apply text-primary-28 !font-400 text-text-secondary mr-50rpx relative shrink-0;

  &.active {
    @apply text-primary-32 text-[#111111] !font-500 leading-40rpx;
  }

  .tabs-dotline {
    background-image: url('#{$oss-prefix}/images/common/tab-line.png');

    @apply absolute top-29rpx bg-no-repeat -z-1 w-109rpx h-9rpx bg-[length:109rpx_9rpx];
  }
}
</style>
