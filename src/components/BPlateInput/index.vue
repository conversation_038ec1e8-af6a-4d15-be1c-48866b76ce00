<template>
  <view class="w-full flex justify-between">
    <view
      class="rounded-2 flex flex-center w-16 h-90rpx box-border"
      :class="[
        index === 7
          ? item
            ? 'bg-brand-lighter bordered-2rpx-solid-brand-primary'
            : 'bg-brand-lighter bordered-2rpx-dashed-brand-primary'
          : 'bg-divider-color',
        index === 0
          ? '!bg-brand-primary'
          : index === currentIndex && showKeyboard
            ? '!bg-brand-lighter bordered-2rpx-solid-brand-primary'
            : '',
      ]"
      v-for="(item, index) in plateNoList"
      :key="index"
      @click.stop="onInputClick(index)"
    >
      <view class="text-white" v-if="index === 0">{{ item || '省' }}</view>
      <view class="text-primary-32 text-text-primary" v-else-if="item">{{ item }}</view>
      <!-- <view
        class="text-primary-32 text-brand-primary input-tip"
        v-else-if="index === currentIndex && showKeyboard"
      >
        |
      </view> -->
      <view
        class="text-auxiliary text-brand-primary"
        v-else-if="index === 7 && language === 'zh'"
        :style="{ writingMode: 'vertical-lr' }"
      >
        新能源
      </view>
    </view>
  </view>
  <BKeyboard
    :language="language"
    :show="showKeyboard"
    :currentInputIndex="currentIndex"
    :type="currentIndex ? 'alphanum' : 'province'"
    :disabledList="disabledList"
    @onKeyItemClick="onKeyItemClick"
    @onDelete="onKeyDeleteClick"
  >
    <template #close>
      <view @touchstart.stop="onKeyCancelClick">{{ language === 'zh' ? '收起' : 'Close' }}</view>
    </template>
  </BKeyboard>
</template>
<script lang="ts" setup>
import BKeyboard from '@/components/BKeyboard/index.vue';
import { getIsTabbar } from '@/utils';

const plateNoList = ref<string[]>([]);
const props = withDefaults(
  defineProps<{
    language: 'zh' | 'en';
    plateNo: string;
  }>(),
  {
    language: 'zh',
    plateNo: '',
  },
);
const emits = defineEmits(['updatePlateNo', 'updatePageScroll', 'updatePlateColor']);
const disabledList = computed(() => {
  if (currentIndex.value === 1) {
    return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '学', '军', '警', '港', '澳'];
  } else if (currentIndex.value === 6) {
    return [];
  } else {
    return ['学', '军', '警', '港', '澳'];
  }
});
watch(
  () => props.plateNo,
  (val) => {
    const list = val.split('').slice(0, 8);
    const fillList = list.length < 8 ? new Array(8 - list.length).fill('') : [];
    plateNoList.value = [...list, ...fillList];
  },
  {
    immediate: true,
  },
);
// const currentIndex = computed(() => {
//   return props.plateNo.length === 8 ? 7 : props.plateNo.length;
// });
const currentIndex = ref(0);
const showKeyboard = ref(false);
const onKeyItemClick = (value: string) => {
  if (props.plateNo.length === 8) {
    emits('updatePlateNo', `${props.plateNo.slice(0, -1)}${value}`);
  } else if (props.plateNo.length === 7) {
    emits('updatePlateColor');
    emits('updatePlateNo', `${props.plateNo}${value}`);
  } else if (props.plateNo.length === 1 && !currentIndex.value) {
    emits('updatePlateNo', `${value}`);
  } else {
    emits('updatePlateNo', `${props.plateNo}${value}`);
  }
};
watch(
  () => props.plateNo,
  (val) => {
    currentIndex.value = val.length === 8 ? 7 : val.length;
  },
);
const onKeyDeleteClick = () => {
  emits('updatePlateNo', props.plateNo.slice(0, -1));
};
const onKeyCancelClick = () => {
  showKeyboard.value = false;
};
const onInputClick = (index: number) => {
  // if (index === currentIndex.value) {
  showKeyboard.value = true;
  // }
  if (props.plateNo.length === 1 && !index) {
    currentIndex.value = 0;
  } else {
    currentIndex.value = props.plateNo.length === 8 ? 7 : props.plateNo.length;
  }
};
watch(
  () => showKeyboard.value,
  (val) => {
    if (val) {
      emits('updatePageScroll', false);
      if (getIsTabbar()) {
        uni.hideTabBar();
      }
    } else {
      emits('updatePageScroll', true);
      if (getIsTabbar()) {
        uni.showTabBar();
      }
    }
  },
);
onUnmounted(() => {
  emits('updatePageScroll', true);
  if (getIsTabbar()) {
    uni.showTabBar();
  }
});
defineExpose({
  onKeyCancelClick,
});
</script>
<style lang="scss" scoped>
@keyframes blink {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  75% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.input-tip {
  animation: blink 1s infinite;
}
</style>
