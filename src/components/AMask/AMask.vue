<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    closeOnClickModal?: boolean;
    className?: string;
    zIndex?: number;
  }>(),
  {
    modelValue: false,
    closeOnClickModal: false,
    className: '',
    zIndex: 8,
  },
);

const emit = defineEmits(['update:modelValue', 'click']);

const handleClose = () => {
  if (props.closeOnClickModal) {
    emit('update:modelValue', false);
  }
};
</script>

<script lang="ts">
export default {
  name: 'AMask',
};
</script>

<template>
  <view
    v-if="props.modelValue"
    @click.stop="handleClose"
    :class="'mask-styles' + ` ${props.className}`"
    :style="{ zIndex: props.zIndex }"
    @touchmove.stop.prevent=""
  ></view>
</template>

<style scoped lang="scss">
.mask-styles {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: rgb(0 0 0 / 30%);
}
</style>
