<template>
  <image
    :class="className"
    :src="fullPath"
    :mode="mode"
    :style="{
      width: addUnit(width),
      height: addUnit(height),
      display: display,
    }"
    @click="handleAppOssClick"
  ></image>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    src: string;
    width: number | string;
    height: number | string;
    mode: string;
    lazyLoad: boolean;
    className: string;
    display: string;
  }>(),
  {
    src: '',
    width: 300,
    height: 225,
    mode: 'scaleToFill',
    lazyLoad: true,
    className: undefined,
    display: 'block',
  },
);

const emit = defineEmits(['click']);

const baseUrl = import.meta.env.VITE_ALI_OSS_URL_PREFIX;

const fullPath = computed(() => {
  return props.src ? baseUrl + props.src : '';
});

const addUnit = (value: number | string, unit = 'rpx') => {
  value = String(value);
  return /^[+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(value) ? `${value}${unit}` : value;
};

const handleAppOssClick = () => {
  emit('click');
};
</script>
