<template>
  <view :class="rootClass" :id="sliderId" class="relative w-full">
    <!-- 滑块主体 -->
    <view class="flex-1 relative h-6rpx rounded-3rpx bg-gray-200" :style="barWrapperStyle">
      <!-- 激活条 -->
      <view
        class="absolute h-full rounded-full bg-brand-primary transition-all"
        :style="barCustomStyle"
      ></view>

      <!-- 左滑块 -->
      <view
        class="slider-button"
        :style="buttonLeftStyle"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        @touchcancel="onTouchEnd"
      >
        <ATooltip
          v-if="showLeftTooltip"
          :content="tipText ? tipText : `${leftNewValue}${unit || ''}`"
          className="top--12 transition-all w-max"
        />
        <view class="slider-btn-box">
          <OSSImg :width="56" :height="56" src="/images/common/slider-button.png" />
        </view>
      </view>

      <!-- 右滑块 -->
      <view
        v-if="showRight"
        class="slider-button"
        :style="buttonRightStyle"
        @touchstart="onTouchStartRight"
        @touchmove="onTouchMoveRight"
        @touchend="onTouchEndRight"
        @touchcancel="onTouchEndRight"
      >
        <ATooltip
          v-if="showRightTooltip"
          :content="tipRightText ? tipRightText : `${rightNewValue}${unit || ''}`"
          className="top--12 transition-all w-max"
        />
        <view class="slider-btn-box">
          <OSSImg :width="56" :height="56" src="/images/common/slider-button.png" />
        </view>
      </view>
    </view>
    <view class="flex-between mt-9 text-text-primary text-secondary-24">
      <!-- 最小值标签 -->
      <view>{{ !hideMinMax ? `${minValue}${unit || ''}` : '' }}</view>
      <!-- 最大值标签 -->
      <view>{{ !hideMinMax ? `${maxValue}${unit || ''}` : '' }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import ATooltip from '@/components/ATooltip/index.vue';
import { computed, getCurrentInstance, ref, watch } from 'vue';

const instance = getCurrentInstance();
// Props 定义
const props = defineProps({
  modelValue: {
    type: [Number, Array],
    default: 0,
  },
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 100,
  },
  step: {
    type: Number,
    default: 1,
  },
  unit: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  hideLabel: {
    type: Boolean,
    default: false,
  },
  hideMinMax: {
    type: Boolean,
    default: false,
  },
  activeColor: {
    type: String,
    default: '',
  },
  inactiveColor: {
    type: String,
    default: '',
  },
  tipText: {
    type: String,
    default: '',
  },
  tipRightText: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'dragstart', 'dragmove', 'dragend']);

// 工具函数
const isArray = (val: unknown): val is any[] => Array.isArray(val);
const isNumber = (val: unknown): val is number => typeof val === 'number';
const isDef = <T,>(val: T): val is NonNullable<T> => val !== undefined && val !== null;

const timer = ref();
const showLeftTooltip = ref(false);
const showRightTooltip = ref(false);
// useTouch 组合式函数
const useTouch = () => {
  const startX = ref(0);
  const startY = ref(0);
  const deltaX = ref(0);
  const deltaY = ref(0);
  const offsetX = ref(0);
  const offsetY = ref(0);

  const touchStart = (event: TouchEvent) => {
    const touch = event.touches[0];
    startX.value = touch.clientX;
    startY.value = touch.clientY;
  };

  const touchMove = (event: TouchEvent) => {
    const touch = event.touches[0];
    deltaX.value = touch.clientX - startX.value;
    deltaY.value = touch.clientY - startY.value;
    offsetX.value = Math.abs(deltaX.value);
    offsetY.value = Math.abs(deltaY.value);
  };

  return {
    startX,
    startY,
    deltaX,
    deltaY,
    offsetX,
    offsetY,
    touchStart,
    touchMove,
  };
};

// 状态变量
const rightSlider = {
  startValue: 0,
  deltaX: 0,
  newValue: 0,
};
const sliderId = ref<string>(`slider-${Math.random().toString(36).slice(2)}`);

const touchLeft = useTouch();
const touchRight = useTouch();

const showRight = ref<boolean>(false);
const barStyle = ref<string>('');
const leftNewValue = ref<number>(0);
const rightNewValue = ref<number>(0);
const rightBarPercent = ref<number>(0);
const leftBarPercent = ref<number>(0);
const trackWidth = ref<number>(0);
const trackLeft = ref<number>(0);
const startValue = ref<number>(0);
const currentValue = ref<number | number[]>(0);
const newValue = ref<number>(0);

const maxValue = ref<number>(100);
const minValue = ref<number>(0);
const stepValue = ref<number>(1);

// 计算属性
const rootClass = computed(() => {
  return [
    'slider',
    !props.hideLabel && 'has-label',
    props.disabled && 'opacity-50 cursor-not-allowed',
  ]
    .filter(Boolean)
    .join(' ');
});

const barWrapperStyle = computed(() => {
  return props.inactiveColor ? `background: ${props.inactiveColor}` : '';
});

const barCustomStyle = computed(() => {
  return `${barStyle.value};${props.activeColor ? `background:${props.activeColor}` : ''}`;
});

const buttonLeftStyle = computed(() => {
  return `left: ${leftBarPercent.value}%; visibility: ${!props.disabled ? 'visible' : 'hidden'};`;
});

const buttonRightStyle = computed(() => {
  return `left: ${rightBarPercent.value}%; visibility: ${!props.disabled ? 'visible' : 'hidden'}`;
});

// 监听器
watch(
  () => props.max,
  (newValue) => {
    if (newValue <= props.min) {
      maxValue.value = props.min;
      minValue.value = newValue;
      console.warn('max value must be greater than min value');
    } else {
      maxValue.value = newValue;
    }
    calcBarPercent();
  },
  { immediate: true },
);

watch(
  () => props.min,
  (newValue) => {
    if (newValue >= props.max) {
      minValue.value = props.max;
      maxValue.value = newValue;
      console.warn('min value must be less than max value');
    } else {
      minValue.value = newValue;
    }
    calcBarPercent();
  },
  { immediate: true },
);

watch(
  () => props.step,
  (newValue) => {
    if (newValue < 1) {
      stepValue.value = 1;
      console.warn('step must be greater than 0');
    } else {
      stepValue.value = Math.floor(newValue);
    }
  },
  { immediate: true },
);

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue === null || newValue === undefined) {
      emit('update:modelValue', currentValue.value);
      console.warn('value cannot be null or undefined');
      return;
    }

    if (isArray(newValue)) {
      if (newValue.length !== 2) {
        console.warn('value must be dyadic array');
        return;
      }
      if (currentValue.value && isArray(currentValue.value) && equal(newValue, currentValue.value))
        return;

      currentValue.value = newValue;
      showRight.value = true;
      if (leftBarPercent.value <= rightBarPercent.value) {
        leftBarSlider(newValue[0]);
        rightBarSlider(newValue[1]);
      } else {
        leftBarSlider(newValue[1]);
        rightBarSlider(newValue[0]);
      }
    } else {
      if (!isNumber(newValue)) {
        emit('update:modelValue', currentValue.value);
        console.warn('value must be number or dyadic array');
        return;
      }
      if (newValue === currentValue.value) return;
      currentValue.value = newValue;
      leftBarSlider(newValue);
    }
  },
  { deep: true, immediate: true },
);

// 方法
function initSlider() {
  const query = uni.createSelectorQuery().in(instance.proxy);
  query
    .select(`#${sliderId.value}`)
    .boundingClientRect((data) => {
      if (data) {
        trackWidth.value = data.width;
        trackLeft.value = data.left;
      }
    })
    .exec();
}

function onTouchStart(event: TouchEvent) {
  if (props.disabled) return;
  touchLeft.touchStart(event);
  startValue.value = !isArray(props.modelValue)
    ? format(props.modelValue)
    : leftBarPercent.value < rightBarPercent.value
      ? format(props.modelValue[0])
      : format(props.modelValue[1]);
  emit('dragstart', { value: currentValue.value });
}

function onTouchMove(event: TouchEvent) {
  if (props.disabled) return;
  if (timer.value) {
    clearTimeout(timer.value);
  }
  showLeftTooltip.value = true;
  touchLeft.touchMove(event);
  const diff = (touchLeft.deltaX.value / trackWidth.value) * (maxValue.value - minValue.value);
  newValue.value = startValue.value + diff;
  leftBarSlider(newValue.value);
  emit('dragmove', { value: currentValue.value });
}

function onTouchEnd() {
  if (props.disabled) return;
  emit('dragend', { value: currentValue.value });
  timer.value = setTimeout(() => {
    showLeftTooltip.value = false;
    clearTimeout(timer.value);
  }, 2000);
}

function onTouchStartRight(event: TouchEvent) {
  if (props.disabled) return;
  touchRight.touchStart(event);
  rightSlider.startValue =
    leftBarPercent.value < rightBarPercent.value
      ? format((props.modelValue as number[])[1])
      : format((props.modelValue as number[])[0]);
  emit('dragstart', { value: currentValue.value });
}

function onTouchMoveRight(event: TouchEvent) {
  if (props.disabled) return;
  if (timer.value) {
    clearTimeout(timer.value);
  }
  showRightTooltip.value = true;
  touchRight.touchMove(event);
  const diff = (touchRight.deltaX.value / trackWidth.value) * (maxValue.value - minValue.value);
  rightSlider.newValue = format(rightSlider.startValue + diff);
  rightBarSlider(rightSlider.newValue);
  emit('dragmove', { value: currentValue.value });
}

function onTouchEndRight() {
  if (props.disabled) return;
  emit('dragend', { value: currentValue.value });
  timer.value = setTimeout(() => {
    showRightTooltip.value = false;
    clearTimeout(timer.value);
  }, 2000);
}

function rightBarSlider(value: number) {
  value = format(value);
  rightNewValue.value = value;
  emit('update:modelValue', [
    Math.min(leftNewValue.value, rightNewValue.value),
    Math.max(leftNewValue.value, rightNewValue.value),
  ]);
  rightBarPercent.value = formatPercent(value);
  styleControl();
}

function leftBarSlider(value: number) {
  value = format(value);
  const percent = formatPercent(value);
  if (!showRight.value) {
    emit('update:modelValue', value);
    leftNewValue.value = value;
    leftBarPercent.value = percent;
    barStyle.value = `width: ${percent}%;`;
  } else {
    leftNewValue.value = value;
    leftBarPercent.value = percent;
    emit('update:modelValue', [
      Math.min(leftNewValue.value, rightNewValue.value),
      Math.max(leftNewValue.value, rightNewValue.value),
    ]);
    styleControl();
  }
}

function styleControl() {
  const barLeft =
    leftBarPercent.value < rightBarPercent.value
      ? [leftBarPercent.value, rightBarPercent.value]
      : [rightBarPercent.value, leftBarPercent.value];

  barStyle.value = `width: ${barLeft[1] - barLeft[0]}%; left: ${barLeft[0]}%`;
  currentValue.value =
    leftNewValue.value < rightNewValue.value
      ? [leftNewValue.value, rightNewValue.value]
      : [rightNewValue.value, leftNewValue.value];
}

function equal(arr1: number[], arr2: number[]) {
  if (!isDef(arr1) || !isDef(arr2)) return false;
  return arr1[0] === arr2[0] && arr1[1] === arr2[1];
}

function format(value: number) {
  return (
    Math.round(Math.max(minValue.value, Math.min(value, maxValue.value)) / stepValue.value) *
    stepValue.value
  );
}

function formatPercent(value: number) {
  return ((value - minValue.value) / (maxValue.value - minValue.value)) * 100;
}

function calcBarPercent() {
  const { modelValue } = props;
  let value = !isArray(modelValue)
    ? format(modelValue)
    : leftBarPercent.value < rightBarPercent.value
      ? format(modelValue[0])
      : format(modelValue[1]);

  value = format(value);
  const percent = formatPercent(value);
  leftBarPercent.value = percent;
  barStyle.value = `width: ${percent}%;`;
}

// 生命周期
watch(
  () => props.modelValue,
  () => {
    setTimeout(initSlider, 0);
  },
  { immediate: true },
);

// 暴露方法
defineExpose({
  initSlider,
});
</script>
<style lang="scss" scoped>
.slider-button {
  @apply absolute top-1/2 -translate-y-1/2 -ml-7 touch-none;
}

.slider-btn-box {
  @apply w-14 h-14 rounded-full;

  box-shadow: 0 4rpx 10rpx 0 rgb(0 0 0 / 12%);
}
</style>
