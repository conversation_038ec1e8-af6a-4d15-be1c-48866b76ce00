<template>
  <view
    :class="`px-10rpx py-2 text-primary-28 font-400 bg-[rgba(64,64,64)] rounded-5 absolute left-1/2 -translate-x-1/2 text-white a-tooltip-box ${className}`"
  >
    {{ content }}
    <view class="absolute left-1/2 -translate-x-1/2 inner-arrow rounded-1"></view>
  </view>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    content: string;
    className?: string;
  }>(),
  {
    content: '',
    className: '',
  },
);
</script>
<style lang="scss" scoped>
.a-tooltip-box {
  box-shadow: 0 4rpx 10rpx 0 rgb(51 51 51 / 20%);

  .inner-arrow {
    bottom: -34rpx;
    width: 0;
    height: 0;
    border-top: 12rpx solid rgb(64 64 64);
    border-right: 12rpx solid transparent;
    border-bottom: 12rpx solid transparent;
    border-left: 12rpx solid transparent;
  }
}
</style>
