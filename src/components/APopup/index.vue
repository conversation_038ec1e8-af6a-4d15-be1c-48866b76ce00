<script setup lang="ts">
import AMask from '../AMask/AMask.vue';
import OSSImg from '../OSSImg/index.vue';

const props = withDefaults(
  defineProps<{
    height?: number | string;
    show?: boolean;
    closable?: boolean;
    closeOnClickModal?: boolean; // 点击遮罩层关闭
    modal?: boolean; // 是否显示遮罩层
    lockScroll?: boolean; // 是否锁定背景滚动
    safeAreaInsetBottom?: boolean; // 是否适配底部安全区域
    customStyle?: string;
    border?: boolean; // 是否显示标题边框
    title?: string;
    titleClass?: string; // 标题unocss
    center?: boolean; // 标题是否居中
    bottom?: boolean; // 是否显示底部按钮
    bottomBorder?: boolean; // 是否显示底部分割线
    bottomClass?: string; // 底部样式
    autoHeight?: boolean; // 是否自适应高度
    maxHeight?: number | string; // 最大高度
    headerHeight?: number | string; // 头部高度
    zIndex?: number;
  }>(),
  {
    show: true,
    closable: true,
    closeOnClickModal: true,
    modal: true,
    lockScroll: true,
    safeAreaInsetBottom: false,
    border: false,
    title: '',
    center: false,
    bottom: false,
    bottomBorder: true,
    bottomClass: '',
    autoHeight: false,
    titleClass: '',
    zIndex: 8,
  },
);

const emits = defineEmits(['update:show', 'close', 'before-enter']);

const handleClose = () => {
  emits('update:show', false);
  emits('close');
};

const handleBeforeEnter = () => {
  emits('before-enter');
};

const customStyle = computed(() => {
  if (props.autoHeight && props.maxHeight) {
    return `height: auto;max-height: ${props.maxHeight}rpx;${props.customStyle ? props.customStyle : ''}`;
  } else if (props.autoHeight && !props.maxHeight) {
    return `height: fit-content;${props.customStyle ? props.customStyle : ''}`;
  } else if (props.height) {
    return `height: ${props.height}rpx;${props.customStyle ? props.customStyle : ''}`;
  }
  return `${props.customStyle ? props.customStyle : ''}`;
});

const maxHeight = computed(() => {
  if (!props.bottom) {
    return `calc(${props.maxHeight}rpx - ${props.headerHeight ? `${props.headerHeight}rpx` : '118rpx'})`;
  } else {
    return `calc(${props.maxHeight}rpx - ${props.headerHeight ? `${props.headerHeight}rpx` : '264rpx'})`;
  }
});
</script>

<script lang="ts">
export default {
  name: 'APopup',
};
</script>

<template>
  <AMask :modelValue="props.show" :zIndex="props.zIndex" />
  <wd-popup
    custom-class="rd-t-8 relative"
    :modelValue="props.show"
    position="bottom"
    :custom-style="`z-index:${props.zIndex + 1};${customStyle}`"
    :close-on-click-modal="props.closeOnClickModal"
    :modal="false"
    :lock-scroll="props.lockScroll"
    :safe-area-inset-bottom="props.safeAreaInsetBottom"
    @close="handleClose"
    @before-enter="handleBeforeEnter"
  >
    <view class="relative h-full flex flex-col of-scroll">
      <!-- 顶部 -->
      <view
        class="h-28 w-full flex items-center relative"
        v-if="props.title"
        :style="props.headerHeight ? `height: ${props.headerHeight}rpx;` : ''"
        :class="[props?.border ? 'popheader' : '', props?.center ? 'header-center' : '']"
      >
        <view class="text-primary-36 leading-11 text-text-primary px-6" :class="props.titleClass">
          <text>{{ props.title }}</text>
        </view>
        <view class="absolute right-6 top-6" @click="handleClose">
          <OSSImg
            v-if="props.closable"
            className=""
            src="/images/common/popup/close.png"
            height="64"
            width="64"
          />
        </view>
      </view>
      <view class="relative" v-else>
        <slot name="header" />
        <OSSImg
          v-if="props.closable"
          className="absolute right-6 top-6"
          src="/images/common/popup/close.png"
          height="64"
          width="64"
          @click="handleClose"
        />
      </view>

      <!-- 内容 -->
      <view class="box-border flex-1 flex flex-col justify-between of-scroll">
        <view
          class="of-scroll px-6"
          :style="{
            maxHeight,
          }"
        >
          <slot />
        </view>
        <view
          v-if="props.bottom"
          class="p-24rpx bottom-0 left-0 w-full box-border relative"
          :class="bottomClass"
        >
          <view class="slider-1px absolute top-0 left-0 right-0" v-if="props.bottomBorder"></view>
          <slot name="bottom" />
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.popheader {
  position: relative;

  &::after {
    content: '';
    border-top: 1rpx solid #eee;

    @apply absolute bottom-0 left-0 right-0;
  }
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
