<template>
  <view
    class="flex items-center text-secondary-24 text-text-primary pl-6 box-border"
    :class="className"
  >
    <view class="w-10 shrink-0" v-if="showType"></view>
    <view class="flex-1">充电时段</view>
    <view class="shrink-0">充电价(元/度)</view>
    <view class="shrink-0 mx-10rpx">=</view>
    <view class="shrink-0 mr-30rpx">电费</view>
    <view class="shrink-0 mr-30rpx">+</view>
    <view class="shrink-0 mr-50rpx">服务费</view>
  </view>
  <view class="text-text-sub text-secondary-24">
    <view
      class="flex mt-4 bg-page-background rounded-4 px-6 py-4"
      v-for="(item, index) in priceList"
      :key="index"
    >
      <view
        class="mr-2 text-white text-auxiliary wh-8 rounded-2 flex-center"
        :style="{ backgroundColor: item.type === 'DC' ? '#3161FF' : '#00BBC8' }"
        v-if="showType"
      >
        {{ item.type === 'DC' ? '快' : '慢' }}
      </view>
      <view class="flex-1">
        <view>{{ item.beginTime }}-{{ item.endTime }}</view>
        <view
          class="text-brand-primary text-auxiliary"
          v-if="inTimeBetween(item.beginTime, item.endTime)"
        >
          当前时段
        </view>
      </view>
      <view class="flex w-101 items-center h-max">
        <view class="text-text-primary w-185rpx">
          <text class="mr-1">￥</text>
          <text class="text-primary-32">{{ priceFormat(item.price) }}</text>
        </view>
        <view class="w-123rpx">
          <text class="mr-1">￥</text>
          <text>{{ priceFormat(item.electPrice) }}</text>
        </view>
        <view>
          <text class="mr-1">￥</text>
          <text>{{ priceFormat(item.servicePrice) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { inTimeBetween } from '@/utils/time';

const props = withDefaults(
  defineProps<{
    priceList: any;
    showType: boolean;
    className?: string;
  }>(),
  {
    priceList: () => [],
    showType: true,
    className: '',
  },
);
const priceFormat = (price: number) => {
  if (price === undefined || price === null) return '--';
  return Number(price).toFixed(4);
};
</script>
