<template>
  <view class="flex items-center w-full text-auxiliary flex-1" :class="className">
    <view class="text-text-primary whitespace-nowrap">
      {{ progressName }}
    </view>
    <view :style="progressWidth ? { width: progressWidth } : { flex: 1 }" class="mx-2">
      <wd-progress
        :duration="5"
        :percentage="percentage || (totalNum ? ((leftNum || 0) / totalNum) * 100 : 0)"
        :hide-text="true"
        :color="statusMap[progressStatus].color"
      />
    </view>
    <view
      class="whitespace-nowrap"
      v-if="progressStatus && statusMap[progressStatus]"
      :style="{ color: statusMap[progressStatus].color }"
    >
      {{ statusMap[progressStatus].text }}
    </view>
    <view class="text-text-primary ml-2 flex items-center">
      <view class="font-DIN text-primary-32 font-bold">{{ numFormat(leftNum) }}</view>
      <view>/{{ numFormat(totalNum) }}</view>
    </view>
  </view>
</template>
<script setup lang="ts">
import type { SpaceStatus } from '@/utils/stationStatus';
import { statusMap } from '@/utils/stationStatus';

const props = withDefaults(
  defineProps<{
    progressName: string;
    percentage: number;
    progressStatus: SpaceStatus;
    progressWidth?: string;
    usedNum?: number;
    leftNum?: number;
    totalNum?: number;
    className?: string;
  }>(),
  {
    progressName: '',
    percentage: 0,
    progressStatus: 'NORMAL',
    progressWidth: '',
    usedNum: 0,
    leftNum: 0,
    totalNum: 0,
    className: '',
  },
);

const numFormat = (num: number) => {
  if (!num) return '0';
  if (num < 10) {
    return `0${num}`;
  }
  return num.toString();
};
</script>
<style scoped lang="scss"></style>
