<template>
  <view
    class="bg-white rounded-4 absolute w-58 px-4 pt-6rpx pb--1 top-18 left-0 box-border filter-pop"
    :style="customStyle"
  >
    <view
      class="absolute right-19 top--3 wh-6 bg-white rounded-lt-1 rotate-45"
      :class="customPopArrowClass"
    ></view>
    <template v-for="(item, index) in list" :key="index">
      <view
        class="w-full text-center text-brand-primary text-secondary-26 py-6"
        @click="() => emit('filterClick', item.value)"
      >
        {{ item.label }}
      </view>
      <view class="slider-1px" v-if="index !== list.length - 1"></view>
    </template>
  </view>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    list: any[];
    customStyle?: object;
    customPopArrowClass?: string;
  }>(),
  {
    list: () => [],
    customStyle: () => ({}),
    customPopArrowClass: '',
  },
);
const emit = defineEmits(['filterClick']);
</script>
<style lang="scss" scoped>
.filter-pop {
  z-index: 999;
  box-shadow: 0 5rpx 32rpx 0 rgb(51 51 51 / 25%);

  &-arrow {
    // box-shadow: -5rpx -5rpx 4rpx 0rpx rgba(51, 51, 51, 0.25);
  }
}
</style>
