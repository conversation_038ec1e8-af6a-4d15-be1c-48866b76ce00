<script setup lang="ts">
import { usePageRouteInfo } from '@/hooks/usePageRouteInfo';

const { route } = usePageRouteInfo();

const props = withDefaults(
  defineProps<{
    // 是否开启防抖
    openDebounce: boolean;
    // 防抖请求
    debounce: () => void | Promise<void>;
    // 权限标识
    auth: string;
  }>(),
  {
    openDebounce: false,
    debounce: () => {},
    auth: '',
  },
);

const emit = defineEmits(['click']);

// TODO: 抽离成全局权限配置或接口返回
const ButtonAuth = [
  {
    role: 'user',
    route: 'pages/mine/service/feedback/list',
    auth: ['list:view'],
  },
];

// 是否有权限
const isAuth = ButtonAuth.find((item) => item.route === route)?.auth.includes(props.auth);

// 防抖锁
const reqLock = ref(false);

// 点击事件
const handleClick = async () => {
  if (props.auth && !isAuth) {
    uni.showToast({
      title: '无权限',
      icon: 'none',
    });
    return;
  }
  if (reqLock.value) return;
  if (props.openDebounce) {
    reqLock.value = true;
    try {
      await props.debounce();
    } catch (error) {
      console.error(error);
    }
    reqLock.value = false;
  }
};
</script>

<script lang="ts">
export default {
  name: 'ADirectives',
};
</script>

<template>
  <view @click.stop="handleClick">
    <slot />
  </view>
</template>

<style scoped lang="scss"></style>
