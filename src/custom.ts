type ColorsType = {
  // 品牌色
  'brand-primary': string;
  'brand-deepen': string;
  'brand-light': string;
  'brand-lighter': string;

  // 文字色
  // todo 过于直接了，需要更多的语义化
  'text-primary': string;
  'text-secondary': string;
  'text-sub': string;
  'text-weak': string;

  // 状态色
  'status-success': string;
  'status-tip': string;
  'status-wait': string;
  'status-fail': string;

  // 其他
  'page-background': string;
  'light-card': string;
  'arrow-color': string;
  'divider-color': string;
  'price-color': string;
  'alert-color': string;
};

interface ErrorMask {
  image?: string;
  title?: string;
  desc?: string;
  buttonText?: string;
  imageHeight?: number | string;
  imageWidth?: number | string;
  [key: string]: any;
}

interface ModalConfig {
  title?: string;
  content?: string;
  showCancel?: boolean;
  cancelText?: string;
  cancelColor?: string;
  confirmText?: string;
  confirmColor?: string;
  success?: (res: any) => void;
  fail?: (err: any) => void;
  [key: string]: any;
}
