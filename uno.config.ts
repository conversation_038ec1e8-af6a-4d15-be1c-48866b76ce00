// uno.config.ts
import {
  Preset,
  defineConfig,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss';

import {
  presetApplet,
  presetRemRpx,
  transformerApplet,
  transformerAttributify,
} from 'unocss-applet';
import { Colors } from './config/colors';
import { createVariants } from './script/unocss/variants';

const isH5 = process.env?.UNI_PLATFORM === 'h5';
const isMp = process.env?.UNI_PLATFORM?.startsWith('mp') ?? false;

const presets: Preset[] = [];

const plat = process.env.UNI_PLATFORM as string;
const variants = createVariants();
const builtInPlatforms = [
  'app',
  'app-plus',
  'h5',
  'mp-360',
  'mp-alipay',
  'mp-baidu',
  'mp-jd',
  'mp-kuaishou',
  'mp-lark',
  'mp-qq',
  'mp-toutiao',
  'mp-weixin',
  'quickapp-webview',
  'quickapp-webview-huawei',
  'quickapp-webview-union',
];

presets.push(
  presetRemRpx({
    baseFontSize: 16,
    screenWidth: 750,
    mode: 'rem2rpx',
  }),
);

// @ts-ignore
const config = defineConfig({
  // todo uni-h5:hidden 在小程序上面会变成 uni-h5 hidden 小程序 css 不支持 :，最好从 transform 层面去把非本次编译的内容去掉，后面完善
  variants,
  presets: [
    presetApplet({ enable: !isH5 }),
    ...presets,
    // 支持图标，需要搭配图标库，eg: @iconify-json/carbon, 使用 `<button class="i-carbon-sun dark:i-carbon-moon" />`
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  safelist: ['top-35%', 'top-50%'],
  // 使用：uni-h5:等
  theme: {
    colors: Colors,
    platforms: builtInPlatforms.reduce(
      (acc, platform) => {
        acc[platform] = platform;
        const withoutPrefix = platform.replace(/^mp-/, '');
        if (withoutPrefix && withoutPrefix !== platform) {
          acc[withoutPrefix] = platform;
        }
        return acc;
      },
      { mp: 'mp', app: 'app', quickapp: 'quickapp' } as any,
    ),
  },

  /**
   * 自定义快捷语句
   * @see https://github.com/unocss/unocss#shortcuts
   */
  shortcuts: [
    {
      'flex-center': 'flex justify-center items-center',
      'flex-col-center': 'flex flex-col justify-center items-center',
      'flex-between': 'flex justify-between',
    },
    {
      'text-leading': 'text-10 font-semibold leading-38rpx',
      'text-primary-36': 'text-9 font-medium leading-42rpx',
      'text-primary-32': 'text-8 font-medium leading-38rpx',
      'text-primary-28': 'text-7 font-medium leading-42rpx',
      'text-secondary-26': 'text-26rpx font-normal leading-10',
      'text-secondary-24': 'text-6 font-normal leading-8',
      'text-auxiliary': 'text-22rpx font-normal leading-26rpx',
    },
    {
      'pb-safe': 'pb-safe-constant pb-safe-env',
      'pt-safe': 'pt-safe-constant pt-safe-env',
      'p-safe': 'p-safe-constant p-safe-env',
    },
    {
      'slider-1px': 'w-full h-1px transform-origin-c scale-y-50 bg-#eee',
    },
    {
      'common-card': 'w-full p-6 bg-white rounded-6 box-border',
      'radio-item': 'scale-70 transform-origin-right mr-0 pr-0',
    },
    {
      border1:
        'relative before:content-[""] before:left-0 before:top-0 before:position-absolute before:w-[200%] before:h-[200%] before:border-width-1 before:border-style-solid before:scale-50 before:transform-origin-left-top before:box-border before:pointer-events-none',
    },
  ],

  transformers: [
    // 启用 @apply 功能
    transformerDirectives({
      enforce: isH5 ? 'pre' : undefined,
    }),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
    // Don't change the following order
    transformerAttributify({
      // 解决与第三方框架样式冲突问题
      prefixedOnly: true,
      prefix: 'fg',
    }),
    transformerApplet(),
  ],
  rules: [
    [
      /^wh-(\d+)(rpx|px|em|rem|vh|vw|%)?$/,
      ([, d, unit]) => {
        if (unit) {
          return { width: `${d}${unit}`, height: `${d}${unit}` };
        } else {
          return { width: `${d * 4}rpx`, height: `${d * 4}rpx` };
        }
      },
      { autocomplete: 'wh-<num>' },
    ],
    [
      /^truncate-(\d+)?$/,
      ([, d]) => {
        if (d) {
          // 多行省略号
          return {
            overflow: hidden,
            'text-overflow': ellipsis,
            display: '-webkit-box',
            '-webkit-box-orient': vertical,
            '-webkit-line-clamp': d,
          };
        } else {
          // 单行省略号
          return { overflow: 'hidden', 'text-overflow': 'ellipsis', 'white-space': 'nowrap' };
        }
      },
      { autocomplete: 'ellipsis-<num>' },
    ],
    [
      /^text-(\d+)(rpx|px|em|rem|vh|vw|%)?-(\d+)(rpx|px|em|rem|vh|vw|%)?$/,
      ([, d, unit, l, lunit]) => {
        const fontSize = unit ? `${d}${unit}` : `${+d * 4}rpx`;
        if (l) {
          const lineHeight = lunit ? `${l}${lunit}` : `${+l * 4}rpx`;
          return { 'font-size': fontSize, 'line-height': lineHeight };
        }
        return { 'font-size': fontSize };
      },
      { autocomplete: 'text-<num>(rpx|px|em|rem|vh|vw|%)?-<num>(rpx|px|em|rem|vh|vw|%)?' },
    ],
    [
      /^bordered(-(t|b|l|r|y|x))?(-(\d+)(rpx|px|em|rem|vh|vw|%)?)?(-(solid|dashed|dotted|double|hidden|none))?(-(.*))?$/,
      ([, , dir, , d, unit, , type, , color]) => {
        const borderInfo = {};
        const dirListInfo = {
          t: ['top'],
          b: ['bottom'],
          l: ['left'],
          r: ['right'],
          y: ['top', 'bottom'],
          x: ['left', 'right'],
        };
        if (d) {
          const borderWidth = unit ? `${d}${unit}` : `${d}rpx`;
          if (dir) {
            borderInfo['border-width'] = 0;
            dirListInfo[dir]?.forEach((dirItem) => {
              borderInfo[`border-${dirItem}-width`] = borderWidth;
            });
          } else {
            borderInfo['border-width'] = borderWidth;
          }
        }
        if (type) {
          borderInfo['border-style'] = type;
        }
        if (color) {
          borderInfo['border-color'] = Colors[color] ? Colors[color] : color;
        }
        return borderInfo;
      },
      {
        autocomplete:
          'bordered(-(t|b|l|r|y|x))?(-(d+)(rpx|px|em|rem|vh|vw|%)?)?(-(solid|dashed|dotted|double|hidden|none))?(-(.*))?',
      },
    ],
    [
      /^(pb|mb|b)-safe-(\d+)(rpx|px|em|rem|vh|vw|%)?$/,
      ([, dir, d, unit]) => {
        const height = unit ? `${d}${unit}` : `${+d * 4}rpx`;
        const dirInfo = {
          pb: 'padding-bottom',
          mb: 'margin-bottom',
          b: 'bottom',
        };
        const key = dirInfo[dir] || '';
        return key
          ? {
              [key]: `calc(${height} + env(safe-area-inset-bottom))`,
            }
          : {};
      },
      { autocomplete: '(pb|mb|b)-safe-(d+)(rpx|px|em|rem|vh|vw|%)?' },
    ],
    [
      'p-safe-constant',
      {
        padding:
          'constant(safe-area-inset-top) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left)',
      },
    ],
    [
      'p-safe-env',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],

    [
      'pt-safe-constant',
      {
        'padding-top': 'constant(safe-area-inset-top)',
      },
    ],
    [
      'pt-safe-env',
      {
        'padding-top': 'env(safe-area-inset-top)',
      },
    ],
    [
      'pb-safe-constant',
      {
        'padding-bottom': 'constant(safe-area-inset-bottom)',
      },
    ],
    [
      'pb-safe-env',
      {
        'padding-bottom': 'env(safe-area-inset-bottom)',
      },
    ],
    [
      'head-top-bg',
      {
        background:
          'linear-gradient(180deg, rgba(94, 187, 108, 0.3) 0%, rgba(94, 187, 108, 0) 100%) no-repeat',
        'background-size': '100% 566rpx',
        width: '750rpx',
        top: 0,
        left: 0,
        height: '1624rpx',
        position: 'absolute',
      },
    ],
    [
      'font-DIN',
      {
        'font-family': 'D-DIN',
        'font-weight': 'bold',
      },
    ],
  ],
});

export default config;
