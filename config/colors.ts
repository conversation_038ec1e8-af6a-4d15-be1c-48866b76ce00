export const Colors = {
  // 品牌色
  'brand-primary': '#56BE66',
  'brand-deepen': '#CD2302',
  'brand-light': 'rgba(86,190,102,0.1)',
  'brand-lighter': 'rgba(86,190,102,0.1)',

  // 文字色
  // todo 过于直接了，需要更多的语义化
  'text-primary': '#111111', // 缩写在某些情况下有问题
  'text-primary-light': '#151515',
  'text-primary-lighter': '#333333',
  'text-secondary': '#666666',
  'text-sub': '#999999',
  'text-weak': '#cccccc',
  'text-gray': '#3D3D3D',
  'text-chart': '#6F7580',

  // 状态色
  'status-success': '#1677FF',
  'status-tip': '#FF8F1F',
  'status-wait': '#00B578',
  'status-fail': '#FF3141',
  'status-settlement': '#0095F1',

  // 其他
  'page-background': '#F8F8F8',
  'light-card': '#F6FBFF',
  'arrow-color': '#D8D8D8',
  'divider-color': '#EEEEEE',
  'price-color': '#FF6835',
  'alert-color': '#FF4935',
  'yellow-color': '#FFCC00',
  'white-color': '#FFFFFF',
  'text-warning': '#FF9500',
};
