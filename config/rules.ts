// 好像不行，后续删除
const Rules = [
  [
    /^wh-(\d+)(rpx|px|em|rem|vh|vw|%)?$/,
    ([, d, unit]) => {
      if (unit) {
        return { width: `${d}${unit}`, height: `${d}${unit}` };
      } else {
        return { width: `${d * 4}rpx`, height: `${d * 4}rpx` };
      }
    },
  ],
  [
    'p-safe-constant',
    {
      padding:
        'constant(safe-area-inset-top) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left)',
    },
  ],
  [
    'p-safe-env',
    {
      padding:
        'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
    },
  ],

  [
    'pt-safe-constant',
    {
      'padding-top': 'constant(safe-area-inset-top)',
    },
  ],
  [
    'pt-safe-env',
    {
      'padding-top': 'env(safe-area-inset-top)',
    },
  ],
  [
    'pb-safe-constant',
    {
      'padding-bottom': 'constant(safe-area-inset-bottom)',
    },
  ],
  [
    'pb-safe-env',
    {
      'padding-bottom': 'env(safe-area-inset-bottom)',
    },
  ],
];
