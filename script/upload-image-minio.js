/* eslint-disable */
const Minio = require('minio')
const path = require('path')
const chokidar = require('chokidar')
const fs = require('fs')
const chalk = require('chalk')
const crypto = require('crypto')

require('dotenv').config({ path: path.resolve(__dirname, `../env/.env.${process.env.NODE_ENV}`) })

const {
  MINIO_ENDPOINT,
  MINIO_API_PORT,
  MINIO_ACCESS_KEY,
  MINIO_SECRET_KEY,
  MINIO_BUCKET,
  MINIO_USE_SSL
} = process.env

// 检查必要的环境变量
if (!MINIO_ENDPOINT || !MINIO_ACCESS_KEY || !MINIO_SECRET_KEY || !MINIO_BUCKET || !MINIO_API_PORT) {
  console.log(chalk.red('请配置 Minio 环境变量'))
  process.exit(1)
}

// 创建 Minio 客户端
const minioClient = new Minio.Client({
  endPoint: MINIO_ENDPOINT,
  port: parseInt(MINIO_API_PORT),
  useSSL: MINIO_USE_SSL === 'true',
  accessKey: MINIO_ACCESS_KEY,
  secretKey: MINIO_SECRET_KEY,
  pathStyle: true // 使用路径样式访问
})

// 指定本地目录
const localDirectory = path.join(__dirname, '../oss-static')

// 计算文件的 MD5 哈希值
function calculateFileHash(filePath) {
  const fileContent = fs.readFileSync(filePath)
  const hash = crypto.createHash('md5')
  hash.update(fileContent)
  return hash.digest('hex')
}

// 检查文件是否存在，并且检查文件内容是否发生了变化
async function fileExistsAndUnchanged(filePath) {
  const fileName = path.relative(localDirectory, filePath)
  const localFileHash = calculateFileHash(filePath)
  try {
    const stat = await minioClient.statObject(MINIO_BUCKET, fileName)
    const minioFileHash = stat.metaData.md5hash
    return localFileHash === minioFileHash
  } catch (err) {
    if (err.code === 'NotFound') {
      return false
    }
    throw err
  }
}

// 上传文件
async function uploadFile(filePath) {
  // 检查文件是否已经存在，并且内容没有发生变化
  if (await fileExistsAndUnchanged(filePath)) {
    console.log(chalk.gray(`${filePath} 文件已存在, 跳过上传`))
    return
  }

  const fileName = path.relative(localDirectory, filePath)
  const fileHash = calculateFileHash(filePath)

  try {
    // 统一windows、linux路径分割符
    const formatFileName = fileName.split(path.sep).join('/')

    // 上传文件
    await minioClient.fPutObject(
      MINIO_BUCKET,
      formatFileName,
      filePath,
      {
        'Content-Type': 'application/octet-stream',
        'md5hash': fileHash
      }
    )
    console.log(chalk.green('上传成功:', formatFileName))
  } catch (err) {
    console.log(chalk.red('上传失败:', err))
  }
}

// 确保 bucket 存在
async function ensureBucketExists() {
  const exists = await minioClient.bucketExists(MINIO_BUCKET)
  if (!exists) {
    await minioClient.makeBucket(MINIO_BUCKET)
    console.log(chalk.green(`Bucket '${MINIO_BUCKET}' 创建成功`))
  }
}

// 启动监控
async function startWatching() {
  await ensureBucketExists()

  console.log(chalk.blue('开始监控文件夹:', localDirectory, '变更'))

  const watcher = chokidar.watch(localDirectory, {
    ignored: /(^|[\/\\])\../,
    persistent: true
  })

  watcher
    .on('add', (path) => {
      console.log(chalk.green(`添加文件：${path}`))
      uploadFile(path)
    })
    .on('change', (path) => {
      console.log(chalk.green(`文件变化，重新上传： ${path}`))
      uploadFile(path)
    })
}

startWatching().catch(console.error)