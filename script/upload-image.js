/* eslint-disable */
const OSS = require('ali-oss')
const path = require('path')
const chokidar = require('chokidar')
const fs = require('fs')
const chalk = require('chalk')
const crypto = require('crypto')
console.log('process.env.NODE_ENV', process.env.NODE_ENV)
require('dotenv').config({ path: path.resolve(__dirname, `../env/.env.${process.env.NODE_ENV}`) })

const { ALI_OSS_REGIN, ALI_OSS_ACCESS_KEY_ID, ALI_OSS_ACCESS_KEY_SECRET, ALI_OSS_BUCKET } =
  process.env

if (!ALI_OSS_REGIN || !ALI_OSS_ACCESS_KEY_ID || !ALI_OSS_ACCESS_KEY_SECRET || !ALI_OSS_BUCKET) {
  console.log(chalk.red('请配置 OSS 环境变量'))
  process.exit(1)
}

// 创建 OSS 客户端
const client = new OSS({
  region: ALI_OSS_REGIN,
  accessKeyId: ALI_OSS_ACCESS_KEY_ID,
  accessKeySecret: ALI_OSS_ACCESS_KEY_SECRET,
  bucket: ALI_OSS_BUCKET,
})

// 指定本地目录
const localDirectory = path.join(__dirname, '../oss-static')

// 计算文件的 MD5 哈希值
function calculateFileHash(filePath) {
  const fileContent = fs.readFileSync(filePath)
  const hash = crypto.createHash('md5')
  hash.update(fileContent)
  return hash.digest('hex')
}

// 检查文件是否存在，并且检查文件内容是否发生了变化
async function fileExistsAndUnchanged(filePath) {
  const fileName = path.relative(localDirectory, filePath)
  const localFileHash = calculateFileHash(filePath)
  try {
    const result = await client.head(fileName)
    const ossFileHash = result.meta.md5 // 假设你在上传文件时保存了文件的 MD5 哈希值到元数据
    return localFileHash === ossFileHash // 如果两个哈希值相同，那么文件没有发生变化
  } catch (err) {
    if (err.code === 'NoSuchKey') {
      return false
    }
    throw err
  }
}

// 上传文件
async function uploadFile(filePath) {
  // 检查文件是否已经存在，并且内容没有发生变化
  if (await fileExistsAndUnchanged(filePath)) {
    console.log(chalk.gray(`${filePath} 文件 oss 已存在, 跳过上传`))
    return
  }
  const fileName = path.relative(localDirectory, filePath)
  const fileContent = fs.readFileSync(filePath)
  const fileHash = calculateFileHash(filePath)
  try {
    // 统一windows、linux路径分割符，oss期望/分割符
    const formatFileName = fileName.split(path.sep).join('/')
    const response = await client.put('mini/' + formatFileName, filePath, {
      meta: {
        md5: fileHash, // 保存文件的 MD5 哈希值到元数据
      },
    })
    console.log(chalk.green('上传成功:', response.name))
  } catch (err) {
    console.log(chalk.red('上传失败:', err))
  }
}

console.log(chalk.blue('开始监控文件夹:', localDirectory, '变更'))
// 监视目录以及其子目录
const watcher = chokidar.watch(localDirectory, {
  ignored: /(^|[\/\\])\../, // ignore dotfiles
  persistent: true,
})

watcher
  .on('add', (path) => {
    console.log(chalk.green(`添加文件：${path}`))
    uploadFile(path)
  })
  .on('change', (path) => {
    console.log(chalk.green(`文件变化，重新上传： ${path}`))
    uploadFile(path)
  })
